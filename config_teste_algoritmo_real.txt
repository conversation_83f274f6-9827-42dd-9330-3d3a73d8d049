# Configuração de Teste para Algoritmo GNJOY LATAM Real
# Use esta configuração para testar o algoritmo XOR com seeds descobertos

######## Login options and server-specific options ########

master Latam - ROla: Freya/Nidhogg/Yggdrasil
server 1
username 
password 
loginPinCode 
char 

# Poseidon Settings
poseidonServer 127.0.0.1
poseidonPort 24390

bindIp
forceMapIP

# XKore 2 mode para teste
XKore 2
XKore_port 6901
XKore_dll NetRedirect.dll
XKore_injectDLL 1
XKore_autoAttachIfOneExe 1
XKore_silent 1
XKore_bypassBotDetection 0
XKore_exeName ragexe.exe

######## Debug específico para algoritmo de checksum ########

# Debug máximo para monitorar checksums
debug 2
debugPacket_sent 2
debugPacket_received 0
debugPacket_include 0085,0437,0360
debugPacket_exclude 
debugPacket_include_dumpMethod 1

# Logs detalhados
verboseLog 1
logToFile_Packet 1
logToFile_Debug 1

######## AI e movimento para teste controlado ########

# AI manual para controle total
ai_manual 1
ai_attack_auto 0
route_randomWalk 0
attackAuto 0
attackAuto_party 0

# Delays aumentados para observar comportamento
moveStepDelay 2000
moveRetryDelay 3000
attackDelay 2000

# Desabilitar ações autônomas durante teste
autoTalkCont 0
autoResponse 0
autoBreakTime 0
autoSaveMap 0

######## Configurações específicas para teste de checksum ########

# Mapa fixo para teste
lockMap 
route_step 15
route_maxWarpFee 0
route_maxNpcTries 0

# Desabilitar recursos desnecessários
dealAuto 0
partyAuto 0
guildAutoDeny 1
chatTitleOversize 0

# Timeouts
timeout_ex 60
timeout 15

######## Instruções de Teste ########

# COMO TESTAR:
# 1. Substitua SEU_USERNAME_AQUI, SUA_SENHA_AQUI, SEU_PIN_AQUI pelos valores reais
# 2. Inicie o OpenKore com esta configuração
# 3. Aguarde login e carregamento do mapa
# 4. Teste movimento manual: "move X Y" no console
# 5. Teste ação manual: "a" para atacar monstro próximo
# 6. Monitore logs para verificar:
#    - Seeds sendo usados (1, 2, 3, 4, 5...)
#    - Checksums calculados (0x62, 0x6F, 0x13, 0x88, 0xAA...)
#    - Ausência de desconexões forçadas

# LOGS ESPERADOS:
# [ROla] REAL XOR: seed_1=0x1F, packet_id=0x0085, bytes=[0x12,0x34,0x56], checksum=0x62
# [ROla] REAL XOR: seed_2=0x2C, packet_id=0x0437, bytes=[0x11,0x22,0x33,0x44,0x07], checksum=0x6F
# Sent move to: 100, 150 (seed: 1, checksum: 0x62)
# Sent Action: 7 on: 11223344 (seed: 2, checksum: 0x6F)

# SINAIS DE SUCESSO:
# ✅ Bot conecta e permanece conectado
# ✅ Comandos de movimento funcionam sem desconexão
# ✅ Comandos de ação funcionam sem desconexão  
# ✅ Seeds incrementam sequencialmente (1, 2, 3, 4, 5...)
# ✅ Checksums correspondem aos valores esperados

# SINAIS DE PROBLEMA:
# ❌ Desconexão imediata após movimento/ação
# ❌ Checksums diferentes dos esperados
# ❌ Seeds não incrementando corretamente
# ❌ Warnings sobre seeds desconhecidos muito cedo (antes do seed 6)

# COMANDOS DE TESTE MANUAL:
# move 100 150    # Testar movimento
# move 120 130    # Testar segundo movimento  
# a               # Atacar monstro próximo (se houver)
# sit             # Sentar (teste de ação)
# stand           # Levantar (teste de ação)

# MONITORAMENTO:
# Observe o console para logs [ROla] com detalhes dos checksums
# Verifique se não há mensagens de erro do servidor
# Confirme que o bot permanece conectado após múltiplos comandos
