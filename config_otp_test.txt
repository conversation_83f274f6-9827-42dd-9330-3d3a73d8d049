# Configuração para Testar OTP Token GNJOY LATAM
# Agora com handler de OTP token implementado

######## Login Settings ########
master Latam - ROla: Frey<PERSON>/Nidhogg/Yggdrasil
server 1
username christ<PERSON><PERSON><PERSON><PERSON>@gmail.com
password Chris2006@
loginPinCode 0103
char 0

# XKore 2 mode
XKore 2
XKore_port 6901
XKore_exeName ragexe.exe

######## DEBUG PARA OTP TOKEN ########
debug 2
verboseLog 1
debugPacket_sent 2
debugPacket_received 2
debugPacket_include_dumpMethod 2

# Focar nos pacotes de login e OTP
debugPacket_include 0825,0AE3,0081,0C32,0436,0085,0437

# Log to files
logToFile 1
logToFile_Packet 1
logToFile_Debug 1

######## AI SETTINGS ########
ai_manual 1
attackAuto 0
route_randomWalk 0
moveStepDelay 2000
attackDelay 2000

# Timeouts
timeout 30
timeout_ex 60

######## LOGS ESPERADOS ########
# ✅ Login inicial bem-sucedido
# ✅ Recebimento de OTP token (packet 0x0AE3)
# ✅ Processamento do token: "Received OTP token, processing..."
# ✅ Envio de token de volta ao servidor
# ✅ Login completo sem erro 0x0081
# ✅ Conexão ao map server
# ✅ Comandos de movimento funcionando

######## COMANDOS DE TESTE ########
# Após login completo:
# move 100 150    # Testar movimento (seed 1)
# move 120 130    # Testar movimento (seed 2)
# sit             # Testar ação (seed 3)
# stand           # Testar ação (seed 4)

######## SINAIS DE SUCESSO ########
# ✅ "Received OTP token, processing..." aparece nos logs
# ✅ Sem loops infinitos de packet 0x0825
# ✅ Sem packet 0x0081 (error) após OTP
# ✅ Progressão para map server
# ✅ [ROla] REAL XOR debug messages aparecem
# ✅ Seeds incrementam corretamente (1, 2, 3, 4, 5...)

######## SINAIS DE PROBLEMA ########
# ❌ Loop infinito de packet 0x0825
# ❌ Packet 0x0081 após receber OTP token
# ❌ "Received OTP token, processing..." não aparece
# ❌ Desconexão após movimento
