package Network::Receive::ROla;

use strict;
use base qw(Network::Receive::ServerType0);
use Globals qw($net $messageSender %config %masterServers);
use Log qw(debug);

sub new {
	my ($class) = @_;
	my $self = $class->SUPER::new(@_);
	
	my %packets = (
		'0C32' => ['account_server_info', 'v a4 a4 a4 a4 a26 C x17 a*', [qw(len sessionID accountID sessionID2 lastLoginIP lastLoginTime accountSex serverInfo)]],
		'0AE3' => ['received_login_token', 'v V Z6 a*', [qw(len unknown server_name login_token)]],
	);
	
	$self->{packet_list}{$_} = $packets{$_} for keys %packets;

	my %handlers = qw(
		account_server_info 0C32
		received_login_token 0AE3
	);

	$self->{packet_lut}{$_} = $handlers{$_} for keys %handlers;
	
	return $self;
}

sub received_login_token {
	my ($self, $args) = @_;

	# Process the OTP token and send it back to the server
	debug sprintf("Received OTP token from server '%s', processing...\n", $args->{server_name}), "connection", 1;
	debug sprintf("Token data length: %d bytes\n", length($args->{login_token})), "connection", 2;

	# Send the token back to the server using the existing sendTokenToServer method
	$messageSender->sendTokenToServer(
		$config{username},
		$config{password},
		$masterServers{$config{master}}->{master_version},
		$masterServers{$config{master}}->{version},
		$args->{login_token},
		$args->{len},
		$masterServers{$config{master}}->{OTP_ip} || $masterServers{$config{master}}->{ip},
		$masterServers{$config{master}}->{OTP_port} || $masterServers{$config{master}}->{port}
	);

	debug "OTP token sent back to server successfully\n", "connection", 1;
}

1;