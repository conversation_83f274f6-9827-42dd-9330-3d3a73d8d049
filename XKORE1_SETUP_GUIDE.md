# 🎯 Guia Completo: Configuração XKore 1 com DLL Injection

## 📋 **ARQUIVOS RECEBIDOS**

### 1. **recv.asi** (28.00 KB)
- **Função:** DLL de injection para resolver checksum
- **<PERSON><PERSON>:** Pasta do cliente Ragnarok (não OpenKore)

### 2. **recv.rar** (6.26 KB) 
- **Função:** Código fonte da DLL
- **<PERSON><PERSON>:** Para análise/modificação (opcional)

## 🎯 **CONFIGURAÇÃO PASSO A PASSO**

### **PASSO 1: Localizar Pasta do Cliente Ragnarok**

Encontre a pasta onde está instalado o **Ragexe.exe** do GNJOY LATAM:
```
Exemplo: C:\Ragnarok\
```

### **PASSO 2: Instalar recv.asi**

1. **Copie recv.asi** para a pasta do cliente Ragnarok
2. **Local correto:** Mesma pasta do Ragexe.exe
```
C:\Ragnarok\recv.asi
C:\Ragnarok\Ragexe.exe
```

### **PASSO 3: Configurar OpenKore para XKore 1**

Edite o arquivo `control/config.txt`:

```ini
# === CONFIGURAÇÃO XKORE 1 ===
XKore 1
XKore_silent 1
XKore_bypassBotDetection 0
XKore_exeName Ragexe.exe

# === SERVIDOR GNJOY LATAM ===
server ROla
username SEU_USERNAME
password SUA_SENHA
char 0

# === CONFIGURAÇÕES DE REDE ===
bindIp 127.0.0.1
forceMapIP 127.0.0.1

# === CONFIGURAÇÕES ESPECÍFICAS ===
gameGuard 2
secureLogin 1
secureLogin_type 0
secureLogin_requestCode 
secureLogin_account 0

# === DEBUG (OPCIONAL) ===
verbose 1
showDomain 1
```

### **PASSO 4: Configurar Servidor ROla**

Verifique se existe `tables/ROla/servers.txt`:

```ini
[GNJOY LATAM]
ip *************
port 10006
version ********
serverType ROla
serverEncoding Western
charBlockSize 175
addTableFolders ROla
chatLangCode 1
storageEncryptKey 0x050B6F79
gameGuard 2
secureLogin 1
secureLogin_type 0
secureLogin_requestCode 
secureLogin_account 0
```

## 🔧 **COMO FUNCIONA O XKORE 1**

### **Processo de Injection:**

1. **OpenKore inicia** com XKore 1
2. **OpenKore executa** o cliente Ragnarok (Ragexe.exe)
3. **recv.asi é carregado** automaticamente pelo cliente
4. **recv.asi intercepta** packets de rede
5. **recv.asi aplica** correção de checksum
6. **OpenKore controla** o cliente via injection

### **Vantagens do XKore 1:**

- ✅ **Resolve checksum** automaticamente
- ✅ **Cliente oficial** é usado
- ✅ **Menos detecção** de bot
- ✅ **Compatibilidade** total com servidor

## 📂 **ESTRUTURA DE ARQUIVOS FINAL**

### **Pasta do Cliente Ragnarok:**
```
C:\Ragnarok\
├── Ragexe.exe
├── recv.asi          ← ARQUIVO NOVO
├── data.grf
└── outros arquivos...
```

### **Pasta do OpenKore:**
```
C:\openkore2\openkore\
├── openkore.pl
├── control\config.txt ← CONFIGURADO
├── tables\ROla\
└── outros arquivos...
```

## 🚀 **TESTE DE FUNCIONAMENTO**

### **1. Iniciar OpenKore:**
```bash
perl openkore.pl
```

### **2. Verificar Logs:**
```
[XKore] Starting XKore mode 1...
[XKore] Launching Ragexe.exe...
[Network] Connecting to *************:10006...
[Login] Sending login packet...
[Login] Login successful!
```

### **3. Sinais de Sucesso:**
- ✅ Cliente Ragnarok abre automaticamente
- ✅ OpenKore conecta sem erro 5011
- ✅ Login é realizado com sucesso
- ✅ Personagem aparece no jogo

## 🔍 **TROUBLESHOOTING**

### **Problema: recv.asi não carrega**
```
Solução:
1. Verificar se recv.asi está na pasta correta
2. Executar como administrador
3. Verificar antivírus (pode bloquear DLL)
```

### **Problema: Erro 5011 ainda aparece**
```
Solução:
1. Verificar se XKore está configurado como 1
2. Confirmar que recv.asi está funcionando
3. Verificar logs do OpenKore
```

### **Problema: Cliente não abre**
```
Solução:
1. Verificar caminho do Ragexe.exe
2. Configurar XKore_exeName corretamente
3. Executar OpenKore como administrador
```

## 📊 **COMPARAÇÃO: XKore 0 vs XKore 1**

| Aspecto | XKore 0 | XKore 1 |
|---------|---------|---------|
| **Checksum** | ❌ Problema | ✅ Resolvido |
| **Detecção** | 🟡 Média | ✅ Baixa |
| **Compatibilidade** | ❌ Limitada | ✅ Total |
| **Configuração** | 🟡 Simples | 🟡 Média |
| **Estabilidade** | ❌ Instável | ✅ Estável |

## 🎯 **PRÓXIMOS PASSOS**

### **1. Implementar Configuração:**
- Copiar recv.asi para pasta do cliente
- Configurar config.txt para XKore 1
- Testar conexão

### **2. Verificar Funcionamento:**
- Monitorar logs de conexão
- Confirmar ausência de erro 5011
- Testar funcionalidades básicas

### **3. Otimizar (Opcional):**
- Analisar código fonte em recv.rar
- Personalizar DLL se necessário
- Implementar melhorias específicas

## 🚨 **IMPORTANTE**

### **Backup:**
- Faça backup do cliente original
- Salve configurações atuais do OpenKore
- Documente mudanças realizadas

### **Segurança:**
- recv.asi pode ser detectado por antivírus
- Adicione exceção se necessário
- Use apenas em servidores permitidos

**Esta solução deve resolver definitivamente o problema de checksum no GNJOY LATAM! 🎉**
