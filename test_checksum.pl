#!/usr/bin/perl
# GNJOY LATAM Checksum Testing Script
# This script helps test different checksum algorithms

use strict;
use warnings;

# Test different checksum algorithms
sub test_checksum_algorithms {
    my ($packet_data, $account_id, $tick, $packet_id) = @_;
    
    print "Testing checksum algorithms for packet 0x" . sprintf("%04X", $packet_id) . "\n";
    print "Packet data: " . unpack("H*", $packet_data) . "\n";
    print "Account ID: 0x" . sprintf("%08X", $account_id) . "\n";
    print "Tick: 0x" . sprintf("%08X", $tick) . "\n\n";
    
    # Algorithm 1: Simple additive
    my $checksum1 = 0;
    for my $i (0..length($packet_data)-1) {
        $checksum1 += ord(substr($packet_data, $i, 1));
    }
    $checksum1 = ($checksum1 + $account_id + $tick) & 0xFFFFFFFF;
    print "Algorithm 1 (Simple): 0x" . sprintf("%08X", $checksum1) . "\n";
    
    # Algorithm 2: XOR-based
    my $checksum2 = $account_id ^ $tick ^ $packet_id;
    for my $i (0..length($packet_data)-1) {
        $checksum2 ^= ord(substr($packet_data, $i, 1)) << ($i % 4);
    }
    $checksum2 &= 0xFFFFFFFF;
    print "Algorithm 2 (XOR): 0x" . sprintf("%08X", $checksum2) . "\n";
    
    # Algorithm 3: Multiplicative (common in RO)
    my $checksum3 = ($account_id * $tick) ^ $packet_id;
    for my $i (0..length($packet_data)-1) {
        my $byte = ord(substr($packet_data, $i, 1));
        $checksum3 = (($checksum3 * 0x343FD) + $byte) & 0xFFFFFFFF;
    }
    print "Algorithm 3 (Multiplicative): 0x" . sprintf("%08X", $checksum3) . "\n";
    
    # Algorithm 4: CRC-like
    my $checksum4 = 0xFFFFFFFF;
    for my $i (0..length($packet_data)-1) {
        my $byte = ord(substr($packet_data, $i, 1));
        $checksum4 = (($checksum4 << 1) ^ $byte) & 0xFFFFFFFF;
    }
    $checksum4 ^= $account_id ^ $tick;
    print "Algorithm 4 (CRC-like): 0x" . sprintf("%08X", $checksum4) . "\n";
    
    # Algorithm 5: GNJOY-style (hypothesis)
    my $checksum5 = $packet_id;
    my $multiplier = 0x41C64E6D;
    my $increment = 0x3039;
    
    for my $i (0..length($packet_data)-1) {
        my $byte = ord(substr($packet_data, $i, 1));
        $checksum5 = (($checksum5 * $multiplier) + $increment + $byte) & 0xFFFFFFFF;
    }
    $checksum5 = ($checksum5 + $account_id + $tick) & 0xFFFFFFFF;
    print "Algorithm 5 (GNJOY-style): 0x" . sprintf("%08X", $checksum5) . "\n";
    
    print "\n" . "="x50 . "\n\n";
}

# Test with sample data
print "GNJOY LATAM Checksum Algorithm Tester\n";
print "="x50 . "\n\n";

# Sample test data
my $sample_account_id = 0x12345678;
my $sample_tick = 0x87654321;

# Test movement packet (0x0085)
my $move_coords = pack("C3", 0x12, 0x34, 0x56); # Sample coordinates
test_checksum_algorithms($move_coords, $sample_account_id, $sample_tick, 0x0085);

# Test action packet (0x0437)
my $action_data = pack("a4 C", "\x11\x22\x33\x44", 0x07); # Sample target ID + action
test_checksum_algorithms($action_data, $sample_account_id, $sample_tick, 0x0437);

print "Instructions:\n";
print "1. Run this script to see different checksum outputs\n";
print "2. Compare with actual checksums from Ragexe.exe analysis\n";
print "3. Implement the matching algorithm in ROla.pm\n";
print "4. Test with GNJOY LATAM server\n";
