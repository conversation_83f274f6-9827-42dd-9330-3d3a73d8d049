# XKore 0 - Teste Rápido para Validar Checksum
# Cliente oficial faz login, OpenKore só injeta movimento/ação

######## XKore 0 Configuration ########
XKore 0
XKore_dll NetRedirect.dll
XKore_injectDLL 1
XKore_autoAttachIfOneExe 1
XKore_silent 0
XKore_bypassBotDetection 1
XKore_exeName ragexe.exe

######## Server (não usado em XKore 0) ########
master Latam - ROla: Freya/Nidhogg/Yggdrasil

######## Debug para Checksum ########
debug 2
verboseLog 1
debugPacket_sent 2
debugPacket_received 1
debugPacket_include 0085,0437

# Log checksum debug
logToFile 1
logToFile_Debug 1

######## AI Manual ########
ai_manual 1
attackAuto 0
route_randomWalk 0

######## INSTRUÇÕES SIMPLES ########
# 1. Feche qualquer Ragexe.exe
# 2. perl openkore.pl
# 3. OpenKore abre cliente automaticamente
# 4. Faça login MANUAL no cliente
# 5. Entre no jogo normalmente
# 6. No console OpenKore: move 100 150
# 7. Observe se aparece: [ROla] REAL XOR: seed_1=0x1F...

######## RESULTADO ESPERADO ########
# SE FUNCIONAR:
# ✅ Algoritmo de checksum está correto
# ✅ Problema é só na autenticação XKore 2
# ✅ Pode usar XKore 0 como solução
#
# SE NÃO FUNCIONAR:
# ❌ Problema no algoritmo de checksum
# ❌ Precisa revisar implementação
# ❌ IDA Pro necessário para checksum
