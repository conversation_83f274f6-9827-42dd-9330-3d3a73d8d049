# GNJOY LATAM Checksum Implementation Guide

## 🎯 Current Status
✅ **Framework Implemented**: ROla.pm now has checksum infrastructure
❌ **Missing**: Actual checksum algorithm from Ragexe.exe

## 🛠 Implementation Overview

### Files Modified
- `src/Network/Send/ROla.pm` - Added checksum framework and custom packet methods

### New Methods Added
1. `calculateChecksum($packet_data, $packet_id)` - Placeholder checksum calculation
2. `sendMove($x, $y)` - Movement packets with checksum
3. `sendAction($monID, $flag)` - Action packets with checksum

## 🔍 Reverse Engineering Steps

### Phase 1: Tools Setup
**Required Tools:**
- **IDA Pro** (preferred) or **Ghidra** (free alternative)
- **x64dbg** for dynamic analysis
- **Cheat Engine** for memory analysis
- **HxD** or similar hex editor

### Phase 2: Ragexe.exe Analysis

#### Step 1: Load Executable
```bash
# Load Ragexe.exe (30/05/2025) into IDA Pro/Ghidra
# Look for packet transmission functions
```

#### Step 2: Locate Packet Functions
**Search for these patterns:**
- String references: "send", "packet", "0085", "0437"
- Function calls to `send()`, `sendto()`, or `WSASend()`
- Network-related API calls

#### Step 3: Find Checksum Calculation
**Look for:**
- Loops that iterate over packet data
- Mathematical operations (XOR, ADD, MUL, shifts)
- Constants used in calculations
- Account ID or tick-based calculations

### Phase 3: Algorithm Extraction

#### Common RO Checksum Patterns
```c
// Pattern 1: Simple additive checksum
checksum = 0;
for (i = 0; i < packet_length; i++) {
    checksum += packet_data[i];
}
checksum ^= account_id;

// Pattern 2: CRC-like calculation
checksum = initial_value;
for (i = 0; i < packet_length; i++) {
    checksum = (checksum << 1) ^ packet_data[i];
}

// Pattern 3: Complex with multiple factors
checksum = (account_id * tick) ^ packet_id;
for (i = 0; i < packet_length; i++) {
    checksum = ((checksum * 0x343FD) + packet_data[i]) & 0xFFFFFFFF;
}
```

## 🧪 Testing Framework

### Current Placeholder Implementation
```perl
sub calculateChecksum {
    my ($self, $packet_data, $packet_id) = @_;
    
    # TODO: Replace this with actual algorithm
    my $checksum = 0;
    my $account_id = unpack("V", $accountID) if $accountID;
    my $tick = getTickCount();
    
    # Simple additive checksum (PLACEHOLDER)
    for my $i (0..length($packet_data)-1) {
        $checksum += ord(substr($packet_data, $i, 1));
    }
    
    $checksum = ($checksum + ($account_id || 0) + $tick) & 0xFFFFFFFF;
    
    return pack("V", $checksum);
}
```

### Testing Process
1. **Enable Debug Logging**:
   ```
   debugPacket_sent 2
   debugPacket_include 0085,0437
   ```

2. **Monitor Server Response**:
   - Connection maintained = checksum working
   - Immediate disconnect = checksum failed

3. **Iterative Testing**:
   - Test different checksum algorithms
   - Compare with packet captures from official client

## 🔧 Implementation Template

### Once Algorithm is Found
Replace the `calculateChecksum` method in `ROla.pm`:

```perl
sub calculateChecksum {
    my ($self, $packet_data, $packet_id) = @_;
    
    # ACTUAL ALGORITHM GOES HERE
    my $checksum = 0;
    my $account_id = unpack("V", $accountID) if $accountID;
    my $tick = getTickCount();
    
    # Example implementation (replace with actual):
    $checksum = ($account_id * $tick) ^ $packet_id;
    for my $i (0..length($packet_data)-1) {
        my $byte = ord(substr($packet_data, $i, 1));
        $checksum = (($checksum * 0x343FD) + $byte) & 0xFFFFFFFF;
    }
    
    return pack("V", $checksum);
}
```

## 📊 Validation Checklist

### ✅ Framework Complete
- [x] ROla.pm has checksum infrastructure
- [x] sendMove() method implemented
- [x] sendAction() method implemented
- [x] Debug logging enabled

### ❌ Pending Implementation
- [ ] Extract actual checksum algorithm from Ragexe.exe
- [ ] Replace placeholder calculateChecksum() method
- [ ] Test with GNJOY LATAM server
- [ ] Validate no disconnections on movement/actions

## 🚀 Next Steps

1. **Reverse Engineer**: Use IDA Pro/Ghidra on Ragexe.exe (30/05/2025)
2. **Extract Algorithm**: Find the exact checksum calculation
3. **Implement**: Replace placeholder in calculateChecksum()
4. **Test**: Verify bot can move and attack without disconnection
5. **Success**: Full bot functionality restored

## 📞 Support

If you need assistance with:
- IDA Pro/Ghidra usage
- Algorithm implementation
- Testing and validation

The framework is ready - only the actual checksum algorithm extraction remains!
