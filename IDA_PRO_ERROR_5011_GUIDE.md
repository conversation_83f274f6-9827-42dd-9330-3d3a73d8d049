# Guia IDA Pro: Investigação Específica do Erro 5011

## 🎯 Objetivo
Encontrar exatamente por que o servidor GNJOY LATAM retorna erro 5011 (account authentication failed) mesmo com OTP token correto.

## 📋 Passo a Passo Detalhado

### Passo 1: Localizar Geração do Erro 5011

**Comando 1: Busca Binária**
```
Alt + B
Digite: 93 13
Explicação: 5011 em decimal = 0x1393 em hex = 93 13 em little-endian
```

**Comando 2: Busca por Texto**
```
Alt + T
Digite: 5011
Procurar por referências diretas ao número
```

**Comando 3: Busca por Packet ID**
```
Alt + B
Digite: 3E 08
Explicação: Packet 083E em little-endian
```

### Passo 2: Analisar Contexto do Erro

**Quando encontrar 93 13 ou 5011:**
1. **Pressione Ctrl + P** (ir para início da função)
2. **Adicione comentário** (tecla ;) explicando o que encontrou
3. **Procure por padrões**:
   ```assembly
   ; Padrão típico:
   mov     eax, 1393h      ; Carregar erro 5011
   push    eax             ; Colocar na pilha
   call    send_error      ; Enviar erro ao cliente
   ```

### Passo 3: Rastrear Causa do Erro

**Scroll para cima** na função e procure:

**A) Validação de Credenciais:**
```assembly
; Procurar por:
call    strcmp              ; Comparação de strings
call    validate_password   ; Validação de senha
call    check_username      ; Verificação de usuário
test    eax, eax           ; Teste do resultado
jz      error_5011         ; Jump se falhou
```

**B) Validação de Token:**
```assembly
; Procurar por:
call    decrypt_token       ; Descriptografia do token
call    validate_token      ; Validação do token
cmp     eax, expected_value ; Comparar com valor esperado
jne     error_5011         ; Jump se diferente
```

**C) Status da Conta:**
```assembly
; Procurar por:
call    get_account_status  ; Obter status da conta
cmp     eax, 1             ; Conta ativa?
jne     error_5011         ; Jump se não ativa
```

### Passo 4: Identificar Dados Específicos

**Procurar por estas strings/dados:**
- **Username**: "<EMAIL>"
- **Password**: Dados criptografados
- **Token**: Dados do OTP token
- **Account ID**: Números de identificação

**Como procurar:**
```
Alt + T
Digite: christianoccruz
(procurar referências ao username)
```

### Passo 5: Analisar Formato Esperado

**Quando encontrar a validação, documentar:**
1. **Formato esperado** do username
2. **Tipo de criptografia** da senha
3. **Formato do token** OTP
4. **Ordem dos campos** no packet
5. **Validações adicionais** (região, VIP, etc.)

## 🔍 Investigações Específicas

### Investigação A: Formato do Username
```
Procurar por:
- Validação de email vs username simples
- Conversão de maiúscula/minúscula
- Encoding (UTF-8, ASCII, etc.)
- Tamanho máximo permitido
```

### Investigação B: Criptografia da Senha
```
Procurar por:
- Algoritmo usado (Rijndael, MD5, SHA, etc.)
- Chaves de criptografia
- Salt ou IV usado
- Formato final esperado
```

### Investigação C: Validação do Token OTP
```
Procurar por:
- Formato esperado do token
- Validação de timestamp
- Associação token-conta
- Expiração do token
```

### Investigação D: Validações Adicionais
```
Procurar por:
- Verificação de região (LATAM)
- Status VIP/Premium
- Limite de conexões simultâneas
- Blacklist de IPs
```

## 📊 Template de Documentação

```
ANÁLISE DO ERRO 5011
===================

Função encontrada em: 0x________
Causa do erro: [DESCREVER]

Validações identificadas:
1. Username: [FORMATO ESPERADO]
2. Password: [CRIPTOGRAFIA USADA]
3. Token OTP: [FORMATO/VALIDAÇÃO]
4. Outras: [LISTAR]

Código Assembly relevante:
[COLAR CÓDIGO AQUI]

Solução proposta:
[COMO CORRIGIR NO OPENKORE]
```

## 🎯 Alvos Prioritários

### Prioridade 1: Validação de Credenciais
- Encontrar como username/password são validados
- Verificar se formato está correto

### Prioridade 2: Token OTP
- Confirmar se token está sendo processado corretamente
- Verificar associação token-conta

### Prioridade 3: Packet Structure
- Confirmar se estrutura do packet 0825 está correta
- Verificar ordem e tamanho dos campos

## 🚨 Sinais de Sucesso

**Você encontrou algo útil quando:**
- ✅ Vê referência direta ao erro 5011
- ✅ Encontra validação de username/password
- ✅ Identifica processamento do token OTP
- ✅ Vê comparações que podem estar falhando

## 🔧 Próximos Passos

1. **Execute a investigação IDA Pro**
2. **Documente os achados**
3. **Identifique a causa específica**
4. **Implemente correção no OpenKore**
5. **Teste a solução**

**Foque na causa específica do erro 5011 - isso vai nos dar a solução exata!**
