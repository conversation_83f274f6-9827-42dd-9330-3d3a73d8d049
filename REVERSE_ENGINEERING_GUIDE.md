# Ragexe.exe Reverse Engineering Guide for GNJOY LATAM

## 🎯 Objective
Extract the exact checksum algorithm used by Ragexe.exe (30/05/2025) for packets 0x0085 (movement) and 0x0437 (actions).

## 🛠 Tools Required

### Primary Tools
- **IDA Pro** (Professional) or **<PERSON><PERSON><PERSON>** (Free)
- **x64dbg** (Dynamic analysis)
- **Cheat Engine** (Memory analysis)

### Download Links
- Ghidra: https://ghidra-sre.org/
- x64dbg: https://x64dbg.com/
- Cheat Engine: https://www.cheatengine.org/

## 🔍 Step-by-Step Analysis

### Phase 1: Static Analysis with IDA Pro/Ghidra

#### Step 1: Load Executable
```
1. Open IDA Pro/Ghidra
2. Load Ragexe.exe (30/05/2025)
3. Wait for auto-analysis to complete
4. Set architecture to x86 (32-bit)
```

#### Step 2: Find Network Functions
**Search for these strings:**
- "send"
- "WSASend"
- "sendto"
- "socket"

**Look for these hex patterns:**
- `85 00` (packet 0x0085)
- `37 04` (packet 0x0437)

#### Step 3: Locate Packet Transmission
```assembly
; Look for patterns like:
mov ax, 0085h        ; Movement packet ID
mov ax, 0437h        ; Action packet ID

; Or in little-endian:
mov word ptr [esp], 8500h
mov word ptr [esp], 3704h
```

#### Step 4: Find Checksum Calculation
**Common patterns to look for:**
```assembly
; Pattern 1: Loop over packet data
mov ecx, packet_length
xor eax, eax
loop_start:
    add al, [esi+ecx-1]
    loop loop_start

; Pattern 2: Account ID usage
mov eax, [account_id]
xor eax, [tick_count]
add eax, checksum

; Pattern 3: Multiplicative operations
imul eax, 343FDh
add eax, ebx
```

### Phase 2: Dynamic Analysis with x64dbg

#### Step 1: Setup Breakpoints
```
1. Load Ragexe.exe in x64dbg
2. Set breakpoints on:
   - WSASend
   - send
   - sendto
3. Run until connected to server
```

#### Step 2: Capture Packet Transmission
```
1. Trigger movement in game
2. Break at send function
3. Examine packet data in memory
4. Step backwards to find checksum calculation
```

#### Step 3: Trace Checksum Algorithm
```
1. Set breakpoints before packet send
2. Examine registers and memory
3. Trace backwards to find calculation
4. Document the algorithm
```

### Phase 3: Memory Analysis with Cheat Engine

#### Step 1: Find Packet Buffer
```
1. Connect to game server
2. Search for packet header (85 00 or 37 04)
3. Find packet buffer location
4. Monitor memory changes
```

#### Step 2: Identify Checksum Location
```
1. Compare packets with/without checksum
2. Find 4-byte difference (checksum)
3. Set watchpoint on checksum location
4. Trace what writes to it
```

## 🧪 Common RO Checksum Algorithms

### Algorithm Type 1: Simple Additive
```c
uint32_t checksum = 0;
for (int i = 0; i < packet_length; i++) {
    checksum += packet_data[i];
}
checksum += account_id + tick;
```

### Algorithm Type 2: XOR-based
```c
uint32_t checksum = account_id ^ tick ^ packet_id;
for (int i = 0; i < packet_length; i++) {
    checksum ^= packet_data[i] << (i % 4);
}
```

### Algorithm Type 3: Linear Congruential
```c
uint32_t checksum = packet_id;
for (int i = 0; i < packet_length; i++) {
    checksum = (checksum * 0x41C64E6D + 0x3039 + packet_data[i]) & 0xFFFFFFFF;
}
checksum += account_id + tick;
```

### Algorithm Type 4: CRC-style
```c
uint32_t checksum = 0xFFFFFFFF;
for (int i = 0; i < packet_length; i++) {
    checksum = ((checksum << 1) ^ packet_data[i]) & 0xFFFFFFFF;
}
checksum ^= account_id ^ tick;
```

## 🔧 Implementation Process

### Step 1: Extract Algorithm
Document the exact assembly code and convert to high-level algorithm.

### Step 2: Convert to Perl
Replace the `calculateChecksum` method in `ROla.pm`:

```perl
sub calculateChecksum {
    my ($self, $packet_data, $packet_id) = @_;
    
    # EXTRACTED ALGORITHM HERE
    my $checksum = 0;
    my $account_id = unpack("V", $accountID) if $accountID;
    my $tick = getTickCount();
    
    # Example (replace with actual):
    for my $i (0..length($packet_data)-1) {
        my $byte = ord(substr($packet_data, $i, 1));
        $checksum = (($checksum * 0x343FD) + $byte) & 0xFFFFFFFF;
    }
    $checksum = ($checksum + $account_id + $tick) & 0xFFFFFFFF;
    
    return pack("V", $checksum);
}
```

### Step 3: Test Implementation
1. Use test configuration
2. Monitor for disconnections
3. Adjust algorithm if needed

## 📊 Validation Checklist

- [ ] Found packet transmission functions
- [ ] Located checksum calculation code
- [ ] Documented algorithm parameters
- [ ] Implemented in ROla.pm
- [ ] Tested with server (no disconnections)
- [ ] Verified movement and actions work

## 🚨 Important Notes

1. **Backup**: Always backup original files
2. **Legal**: Only reverse engineer for interoperability
3. **Testing**: Test thoroughly before production use
4. **Documentation**: Document findings for future reference

## 📞 Next Steps

Once you extract the algorithm:
1. Replace placeholder in `ROla.pm`
2. Test with GNJOY LATAM server
3. Verify full bot functionality
4. Document the working algorithm

The framework is ready - you just need the actual checksum algorithm!
