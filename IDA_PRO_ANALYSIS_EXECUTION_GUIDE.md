# IDA Pro Analysis Execution Guide for GNJOY LATAM

## 🎯 Executing Your Analysis Plan

Based on your comprehensive analysis framework, here's the practical execution guide with specific IDA Pro commands and techniques.

## 📋 Phase 1 Execution: Locate Network Transmission Functions

### Step 1.1: Binary Search for Packet IDs
```
IDA Pro Commands:
1. Alt + B (Binary Search)
2. Enter: 85 00
3. Search Options: "Find all occurrences"
4. Document each result:
   - Address: 0x________
   - Context: [Function name/purpose]
   - Usage: [How packet ID is used]
```

**Expected Results Pattern:**
```assembly
; Movement packet construction:
00401234  66 C7 44 24 04 85 00    mov word ptr [esp+4], 85h
0040123B  88 44 24 06             mov [esp+6], al        ; coord byte 1
0040123F  88 5C 24 07             mov [esp+7], bl        ; coord byte 2
00401243  88 4C 24 08             mov [esp+8], cl        ; coord byte 3
```

### Step 1.2: Trace to Packet Construction Functions
```
For each packet ID occurrence:
1. Press Ctrl + P (go to function start)
2. Press N to rename function (e.g., "send_movement_packet")
3. Press ; to add comments explaining packet structure
4. Look for send/WSASend calls in same function
```

### Step 1.3: Cross-Reference Analysis
```
IDA Pro Commands:
1. Navigate to send/WSASend import
2. Press Ctrl + X (cross-references)
3. Examine each calling function
4. Look for functions that:
   - Build packet headers (0x0085, 0x0437)
   - Process coordinate/target data
   - Call checksum functions
```

## 📋 Phase 2 Execution: Checksum Algorithm Discovery

### Step 2.1: Identify Checksum Calculation Patterns
**Search Strategy:**
```
1. Look for loops near packet construction:
   - Search for: E2 XX (loop instruction)
   - Search for: 33 C0 (xor eax, eax - checksum init)
   
2. Search for GetTickCount usage:
   - Shift + F4 (Names window)
   - Search: "GetTickCount"
   - Press Ctrl + X on GetTickCount
   
3. Search for account ID access:
   - Look for: A1 XX XX XX XX (mov eax, ds:global_var)
   - Search for large constants (account ID values)
```

### Step 2.2: Algorithm Pattern Recognition
**Look for these specific patterns:**

**Pattern A: Simple Additive Checksum**
```assembly
33 C0                   xor     eax, eax          ; checksum = 0
8B 4D 08                mov     ecx, [ebp+8]      ; packet_length
8B 75 0C                mov     esi, [ebp+0Ch]    ; packet_data
                        loc_loop:
02 04 0E                add     al, [esi+ecx]     ; checksum += data[i]
E2 FB                   loop    loc_loop          ; repeat
```

**Pattern B: Multiplicative with Constants**
```assembly
69 C0 FD 43 03 00       imul    eax, 343FDh       ; multiply by magic
03 C3                   add     eax, ebx          ; add current byte
25 FF FF FF FF          and     eax, 0FFFFFFFFh   ; mask to 32-bit
```

**Pattern C: Account ID Integration**
```assembly
A1 XX XX XX XX          mov     eax, ds:account_id ; load account ID
FF 15 XX XX XX XX       call    ds:GetTickCount   ; get tick count
03 C1                   add     eax, ecx          ; combine with checksum
```

### Step 2.3: Document Algorithm Components
**Create analysis notes:**
```
Function: send_movement_packet (0x________)
Checksum calculation starts at: 0x________

Variables identified:
- Account ID: ds:________ (global variable at 0x________)
- Tick Count: from GetTickCount() call at 0x________
- Packet Data: [ebp+0Ch] (function parameter)
- Packet Length: [ebp+08h] (function parameter)

Constants found:
- Magic multiplier: 0x________
- XOR constant: 0x________
- Addition constant: 0x________

Algorithm steps:
1. Initialize checksum = 0
2. [Document each assembly instruction]
3. [...]
4. Return checksum in EAX
```

## 📋 Phase 3 Execution: Algorithm Extraction and Validation

### Step 3.1: Assembly to Pseudocode Conversion
**Systematic approach:**
```
For each assembly instruction, document:
1. What it does in plain English
2. Which variables it affects
3. The mathematical operation performed

Example conversion:
Assembly: 69 C0 FD 43 03 00    imul eax, 343FDh
English: Multiply checksum by magic constant 0x343FD
C code:  checksum = (checksum * 0x343FD) & 0xFFFFFFFF;
```

### Step 3.2: Create Test Implementation
**Validation pseudocode:**
```c
uint32_t calculate_checksum(uint8_t* packet_data, int length, 
                           uint32_t account_id, uint32_t tick) {
    uint32_t checksum = 0;
    
    // Your extracted algorithm here:
    for (int i = 0; i < length; i++) {
        checksum += packet_data[i];
    }
    checksum = (checksum * 0x343FD + tick) & 0xFFFFFFFF;
    checksum ^= account_id;
    
    return checksum;
}
```

### Step 3.3: Verify Algorithm Completeness
**Validation checklist:**
- [ ] Algorithm processes all packet data bytes
- [ ] Uses account ID (verify global variable address)
- [ ] Uses tick count (verify GetTickCount call)
- [ ] Produces 32-bit output
- [ ] Same algorithm for both 0x0085 and 0x0437
- [ ] Checksum appended to packet before send

## 🔧 Advanced Analysis Techniques

### Dynamic Analysis Integration
**If static analysis is insufficient:**
```
1. Use x64dbg for dynamic analysis:
   - Set breakpoint on packet send functions
   - Capture actual packet data and checksums
   - Compare with your algorithm output

2. Memory analysis:
   - Find packet buffer in memory
   - Watch checksum calculation in real-time
   - Validate variable values during execution
```

### Alternative Search Methods
**If primary searches fail:**
```
1. String-based searches:
   - Search for: "packet", "checksum", "send"
   - Look for debug strings or error messages

2. Constant-based searches:
   - Search for common RO constants: 0x343FD, 0x41C64E6D
   - Look for CRC polynomials or hash constants

3. API-based searches:
   - Find all GetTickCount references
   - Trace account ID storage/retrieval
   - Look for encryption/hash library calls
```

## 📊 Documentation Template

### Complete Analysis Report
```
GNJOY LATAM CHECKSUM ALGORITHM ANALYSIS
======================================

Executable: Ragexe.exe (30/05/2025)
Analysis Date: [DATE]
Analyst: [YOUR NAME]

PACKET FUNCTIONS IDENTIFIED:
- Movement (0x0085): Function at 0x________
- Action (0x0437): Function at 0x________

CHECKSUM FUNCTION:
- Address: 0x________
- Assembly code: [PASTE COMPLETE ASSEMBLY]

ALGORITHM VARIABLES:
- Account ID: ds:________ (global variable)
- Tick Count: GetTickCount() at 0x________
- Packet Data: Function parameter
- Constants: [LIST ALL MAGIC NUMBERS]

EXTRACTED ALGORITHM:
[COMPLETE C PSEUDOCODE]

PERL IMPLEMENTATION:
[READY-TO-USE PERL CODE FOR ROla.pm]

VALIDATION NOTES:
[TEST RESULTS AND VERIFICATION]
```

## 🎯 Success Validation

**Algorithm extraction successful when:**
1. ✅ Complete assembly code documented
2. ✅ All variables and constants identified
3. ✅ Pseudocode conversion completed
4. ✅ Perl implementation ready
5. ✅ Test vectors available (if possible)

**Ready for OpenKore integration when:**
1. ✅ calculateChecksum() method implemented
2. ✅ No server disconnections during testing
3. ✅ Both movement and action packets work
4. ✅ Algorithm documented for future reference

This execution guide provides the practical steps to implement your comprehensive analysis framework. Follow each phase systematically, and you'll successfully extract the GNJOY LATAM checksum algorithm!
