#!/usr/bin/perl
# Debug Token Format for GNJOY LATAM
# Analyze the exact token format being sent vs expected

use strict;
use warnings;

print "GNJOY LATAM TOKEN FORMAT ANALYZER\n";
print "="x50 . "\n\n";

# Extract token data from your logs
my $received_token_hex = "62 38 A0 FB 29 10 00 3A 04 75 40 FB 29 10 56 79 32 33 7A 42 37 74 58 51 59 4A 34 75 38 75 32 52 68 69 50 6B 4E 31 6B 75 53 35 6E 36 64 7A 77 75 4F 51 67 4B 51 31 53 64 39 50 76 76 44 45 67 57 77 68 32 6A 74 35 6C 50 43 76 6C 54 76 47 43 61 65 57 6D 69 66 7A 77 4A 50 42 33 46 37 5A 58 4A 76 4E 4A 2F 4B 31 72 56 61 45 45 73 45 65 6A 78 34 6C 44 4D";

# Convert hex to binary
$received_token_hex =~ s/\s+//g;  # Remove spaces
my $token_binary = pack("H*", $received_token_hex);

print "RECEIVED TOKEN ANALYSIS:\n";
print "Hex: $received_token_hex\n";
print "Length: " . length($token_binary) . " bytes\n";
print "ASCII: " . $token_binary . "\n\n";

# Analyze token structure
print "TOKEN STRUCTURE ANALYSIS:\n";
print "First 16 bytes (hex): " . unpack("H*", substr($token_binary, 0, 16)) . "\n";
print "First 16 bytes (ASCII): " . substr($token_binary, 0, 16) . "\n";

# Look for patterns
print "\nPATTERN ANALYSIS:\n";
my @bytes = unpack("C*", $token_binary);
for my $i (0..15) {
    printf "Byte %2d: 0x%02X (%3d) '%c'\n", $i, $bytes[$i], $bytes[$i], 
           ($bytes[$i] >= 32 && $bytes[$i] <= 126) ? chr($bytes[$i]) : '.';
}

print "\nTOKEN VALIDATION CHECKS:\n";

# Check if token looks like base64
if ($token_binary =~ /^[A-Za-z0-9+\/=]+$/) {
    print "✅ Token appears to be Base64 encoded\n";
} else {
    print "❌ Token is NOT Base64 encoded\n";
}

# Check for null termination
if ($token_binary =~ /\0/) {
    print "✅ Token contains null bytes (C-string style)\n";
} else {
    print "❌ Token does NOT contain null bytes\n";
}

# Check for specific patterns
if ($token_binary =~ /Vy23z/) {
    print "✅ Token contains 'Vy23z' pattern (consistent with logs)\n";
} else {
    print "❌ Token missing expected pattern\n";
}

print "\nPOSSIBLE ISSUES:\n";
print "1. Token might need additional processing (encryption/decryption)\n";
print "2. Token might need specific padding or formatting\n";
print "3. Server might expect token in different position in packet\n";
print "4. Token might have expiration that we're missing\n";

print "\nIDA PRO INVESTIGATION TARGETS:\n";
print "1. Search for '006A' error code handling\n";
print "2. Look for token validation functions\n";
print "3. Find where error code 30 is generated\n";
print "4. Analyze token processing in login functions\n";

print "\nRECOMMENDED IDA PRO SEARCHES:\n";
print "- Search for hex: '6A 00' (packet 006A)\n";
print "- Search for decimal: '30' (error code)\n";
print "- Search for 'token', 'validate', 'auth'\n";
print "- Look for functions that process 431-byte packets\n";

print "\nNEXT STEPS:\n";
print "1. Use IDA Pro to find error code 30 generation\n";
print "2. Analyze token validation function\n";
print "3. Check if token needs additional processing\n";
print "4. Verify packet structure matches server expectations\n";

print "\n" . "="x50 . "\n";
print "TOKEN HANDLER IS WORKING - NOW NEED TO FIX TOKEN FORMAT!\n";
print "="x50 . "\n";
