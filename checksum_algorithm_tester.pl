#!/usr/bin/perl
# GNJOY LATAM Checksum Algorithm Tester
# Use this script to test different checksum algorithms as you discover them

use strict;
use warnings;
use Data::Dumper;

print "="x60 . "\n";
print "GNJOY LATAM Checksum Algorithm Tester\n";
print "="x60 . "\n\n";

# Test data - replace with actual values from your analysis
my $test_cases = [
    {
        name => "Movement Packet (0x0085)",
        packet_id => 0x0085,
        packet_data => pack("C3", 0x12, 0x34, 0x56), # Sample coordinates
        account_id => 0x12345678,  # Replace with actual account ID
        tick => 0x87654321,        # Replace with actual tick value
    },
    {
        name => "Action Packet (0x0437)", 
        packet_id => 0x0437,
        packet_data => pack("a4 C", "\x11\x22\x33\x44", 0x07), # Sample target + action
        account_id => 0x12345678,  # Replace with actual account ID
        tick => 0x87654321,        # Replace with actual tick value
    }
];

# Algorithm implementations - replace these with your extracted algorithms
sub algorithm_simple_additive {
    my ($packet_data, $packet_id, $account_id, $tick) = @_;
    
    my $checksum = 0;
    
    # Simple additive checksum
    for my $i (0..length($packet_data)-1) {
        $checksum += ord(substr($packet_data, $i, 1));
    }
    
    $checksum = ($checksum + $account_id + $tick) & 0xFFFFFFFF;
    
    return $checksum;
}

sub algorithm_multiplicative {
    my ($packet_data, $packet_id, $account_id, $tick) = @_;
    
    my $checksum = $packet_id;
    my $multiplier = 0x343FD;  # Common RO constant
    
    # Multiplicative algorithm
    for my $i (0..length($packet_data)-1) {
        my $byte = ord(substr($packet_data, $i, 1));
        $checksum = (($checksum * $multiplier) + $byte) & 0xFFFFFFFF;
    }
    
    $checksum = ($checksum + $account_id + $tick) & 0xFFFFFFFF;
    
    return $checksum;
}

sub algorithm_xor_based {
    my ($packet_data, $packet_id, $account_id, $tick) = @_;
    
    my $checksum = $account_id ^ $tick ^ $packet_id;
    
    # XOR-based algorithm
    for my $i (0..length($packet_data)-1) {
        my $byte = ord(substr($packet_data, $i, 1));
        $checksum ^= $byte << ($i % 4);
    }
    
    $checksum &= 0xFFFFFFFF;
    
    return $checksum;
}

sub algorithm_crc_style {
    my ($packet_data, $packet_id, $account_id, $tick) = @_;
    
    my $checksum = 0xFFFFFFFF;
    
    # CRC-style algorithm
    for my $i (0..length($packet_data)-1) {
        my $byte = ord(substr($packet_data, $i, 1));
        $checksum = (($checksum << 1) ^ $byte) & 0xFFFFFFFF;
    }
    
    $checksum ^= $account_id ^ $tick;
    
    return $checksum;
}

# TODO: Replace this with your extracted algorithm from IDA Pro
sub algorithm_extracted {
    my ($packet_data, $packet_id, $account_id, $tick) = @_;
    
    # PLACEHOLDER - Replace with your actual extracted algorithm
    my $checksum = 0;
    
    # Example implementation (replace with actual):
    # Step 1: Initialize
    # Step 2: Process packet data
    # Step 3: Apply account ID and tick
    # Step 4: Return result
    
    print "WARNING: Using placeholder algorithm - replace with extracted code!\n";
    
    return algorithm_simple_additive($packet_data, $packet_id, $account_id, $tick);
}

# Test all algorithms
my @algorithms = (
    { name => "Simple Additive", func => \&algorithm_simple_additive },
    { name => "Multiplicative", func => \&algorithm_multiplicative },
    { name => "XOR-based", func => \&algorithm_xor_based },
    { name => "CRC-style", func => \&algorithm_crc_style },
    { name => "EXTRACTED (Your Algorithm)", func => \&algorithm_extracted },
);

# Run tests
foreach my $test_case (@$test_cases) {
    print "Testing: $test_case->{name}\n";
    print "Packet ID: 0x" . sprintf("%04X", $test_case->{packet_id}) . "\n";
    print "Packet Data: " . unpack("H*", $test_case->{packet_data}) . "\n";
    print "Account ID: 0x" . sprintf("%08X", $test_case->{account_id}) . "\n";
    print "Tick: 0x" . sprintf("%08X", $test_case->{tick}) . "\n";
    print "-" x 40 . "\n";
    
    foreach my $algorithm (@algorithms) {
        my $checksum = $algorithm->{func}->(
            $test_case->{packet_data},
            $test_case->{packet_id},
            $test_case->{account_id},
            $test_case->{tick}
        );
        
        printf "%-25s: 0x%08X\n", $algorithm->{name}, $checksum;
    }
    
    print "\n" . "="x60 . "\n\n";
}

# Generate Perl code for OpenKore
print "PERL CODE FOR OpenKore ROla.pm:\n";
print "="x40 . "\n";
print <<'EOF';
sub calculateChecksum {
    my ($self, $packet_data, $packet_id) = @_;
    
    my $checksum = 0;
    my $account_id = unpack("V", $accountID) if $accountID;
    my $tick = getTickCount();
    
    # TODO: Replace this with your extracted algorithm
    # Example implementation:
    for my $i (0..length($packet_data)-1) {
        $checksum += ord(substr($packet_data, $i, 1));
    }
    $checksum = ($checksum + ($account_id || 0) + $tick) & 0xFFFFFFFF;
    
    debug sprintf("Calculated checksum: 0x%08X for packet 0x%04X\n", 
                  $checksum, $packet_id), "sendPacket", 2;
    
    return pack("V", $checksum);
}
EOF

print "\nINSTRUCTIONS:\n";
print "1. Extract the actual algorithm from IDA Pro analysis\n";
print "2. Replace algorithm_extracted() function above with your findings\n";
print "3. Test the algorithm with this script\n";
print "4. Copy the working algorithm to ROla.pm\n";
print "5. Test with GNJOY LATAM server\n";

print "\nNOTES:\n";
print "- Replace test data with actual values from packet captures\n";
print "- Compare outputs with checksums from real client\n";
print "- The correct algorithm should match server expectations\n";
print "- Test both movement (0x0085) and action (0x0437) packets\n";
