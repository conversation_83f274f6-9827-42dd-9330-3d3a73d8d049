# OpenKore Configuration for GNJOY LATAM Checksum Testing
# Copy this to control/config.txt for testing

######## Login options and server-specific options ########

master Latam - ROla: <PERSON><PERSON>/Nidhogg/Yggdrasil
server 1
username YOUR_USERNAME_HERE
password YOUR_PASSWORD_HERE
loginPinCode YOUR_PIN_HERE
char 0

# Poseidon Settings
poseidonServer 127.0.0.1
poseidonPort 24390

bindIp
forceMapIP

# XKore 2 mode for testing
XKore 2
XKore_port 6901
XKore_dll NetRedirect.dll
XKore_injectDLL 1
XKore_autoAttachIfOneExe 1
XKore_silent 1
XKore_bypassBotDetection 0
XKore_exeName ragexe.exe

######## Debugging options for checksum testing ########

# Enable packet debugging
debugPacket_sent 2
debugPacket_received 0
debugPacket_include 0085,0437,0360
debugPacket_exclude 
debugPacket_include_dumpMethod 1

# AI and movement settings for testing
ai_manual 1
ai_attack_auto 0
route_randomWalk 0
attackAuto 0
attackAuto_party 0

# Movement delays for testing
moveStepDelay 1000
moveRetryDelay 2000
attackDelay 1000

# Disable autonomous actions during testing
autoTalkCont 0
autoResponse 0
autoBreakTime 0
autoSaveMap 0

######## Testing specific settings ########

# Minimal movement for testing
lockMap 
route_step 15
route_maxWarpFee 0
route_maxNpcTries 0

# Disable unnecessary features during testing
dealAuto 0
partyAuto 0
guildAutoDeny 1
chatTitleOversize 0
verboseLog 1

# Timeout settings
timeout_ex 60
timeout 15

######## Instructions ########
# 1. Replace YOUR_USERNAME_HERE, YOUR_PASSWORD_HERE, YOUR_PIN_HERE with actual values
# 2. Start OpenKore with this config
# 3. Monitor console for checksum debug messages
# 4. Test movement with 'move X Y' command
# 5. Test actions with 'a' command on monsters
# 6. Check if server disconnects (indicates checksum failure)
# 7. Adjust checksum algorithm in ROla.pm based on results
