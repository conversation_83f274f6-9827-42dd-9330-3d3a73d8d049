# <PERSON><PERSON> can reply with a message when someone talks to you. In this file,
# you can configure what <PERSON><PERSON> should say when someone says something.
#
# Each line has the following format:
# (words) TAB (answers)
#
# (words) is a comma-seperated list of words. When the message (of the
# one who talks to <PERSON><PERSON>) contains a word that's in this list, <PERSON><PERSON> will
# pick a random answer from (answers), which is a comma-seperated list
# of possible answers.
#
# For example:
# bot,botter	no,I'm not a bot,huh?
#
# When someone says something that contains the word "bot" or "botter",
# <PERSON><PERSON> will reply with "no", "I'm not a bot" or "huh?".

bot,botter	no,I'm not a bot,huh?
