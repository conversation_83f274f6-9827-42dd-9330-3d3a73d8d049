
#include "tiger.h"

/*
   The following macro denotes that an optimization
   for Alpha is required. It is used only for
   optimization of time. Otherwise it does nothing.
 */

/*
 * this is the 32bit version, the 64bit version above doesn't work
 * for me on Sparc/Solaris with gcc
 */

#define PASSES 3

dword table[4*256][2] = {
	{0x0E1A833ADL, 0x0FC74C963L}, {0x097F47AE2L, 0x0DA204122L}, {0x0BF32463CL, 0x05E22924AL}, {0x0D10B3E43L, 0x020E43242L},
	{0x054D7B96FL, 0x097FCAAB3L}, {0x0D23D4038L, 0x04DB37205L}, {0x0D1976B25L, 0x0A8C112B0L}, {0x0BA7BA2CFL, 0x031AE125CL},
	{0x015527C7EL, 0x08FB1D950L}, {0x079C452EAL, 0x00C94F136L}, {0x03039DD6BL, 0x03EBDF084L}, {0x0F0396399L, 0x09EA53EA3L},
	{0x0222B8CEBL, 0x0B4D4554AL}, {0x05EA8C2DAL, 0x008C1DCB3L}, {0x0FC389BFEL, 0x012F61A94L}, {0x0833371BFL, 0x059F9B856L},
	{0x08D41FA94L, 0x046332E71L}, {0x080E98E27L, 0x0614CF48DL}, {0x0F574A7CEL, 0x0429B91F2L}, {0x0639ADC22L, 0x051AA8F57L},
	{0x034B3A49BL, 0x025FF4405L}, {0x01E6788B0L, 0x0181369A4L}, {0x05B1D00FBL, 0x0C07E56BCL}, {0x0914F84E2L, 0x0B698A295L},
	{0x04873ABFEL, 0x04208B7E6L}, {0x0EA32EEA7L, 0x01B282B29L}, {0x0FEF3A675L, 0x09AAE77B4L}, {0x00B3089FFL, 0x058D30330L},
	{0x09A8F0F9FL, 0x0CB5F7715L}, {0x0035982DBL, 0x06B994AEAL}, {0x0FD26A84CL, 0x0C22BD508L}, {0x0D28EEB59L, 0x0467BC128L},
	{0x038E9A18CL, 0x091028490L}, {0x078CE826BL, 0x00958A6F8L}, {0x04AB7F860L, 0x037F491BAL}, {0x0F62A8A01L, 0x09250DB5CL},
	{0x024909FC6L, 0x0A4F2ED68L}, {0x04B90D039L, 0x0F3635F54L}, {0x0D48495C0L, 0x0F81B99A8L}, {0x067127605L, 0x02B8233EEL},
	{0x06C93EA6EL, 0x0144F948DL}, {0x05BAF6A64L, 0x03ACC560CL}, {0x0CBBE8F8EL, 0x0078FFFF4L}, {0x02647BF56L, 0x01111D8CDL},
	{0x0F2E47242L, 0x0D1D998FFL}, {0x0B80B52DBL, 0x0CF71B901L}, {0x0FF25C599L, 0x0624F918DL}, {0x021DA45F4L, 0x044EDD9E9L},
	{0x0D4816874L, 0x0DBC1E9BEL}, {0x071B486A0L, 0x0A0635953L}, {0x080F959F1L, 0x02B5D9062L}, {0x089B928EFL, 0x0D3062861L},
	{0x0047C9AF2L, 0x0320587CAL}, {0x078BA18B2L, 0x0DEB34603L}, {0x05E0A2A96L, 0x020B8DDA5L}, {0x03ED55827L, 0x0A08CC327L},
	{0x080B319DDL, 0x0E6867213L}, {0x0CC1CF620L, 0x05A5F80FFL}, {0x089785898L, 0x073607624L}, {0x0305FD5BCL, 0x0CA4FAC4AL},
	{0x05948F5F6L, 0x0D764BAC9L}, {0x07CBC12DCL, 0x022492738L}, {0x0F123D2E7L, 0x013645D01L}, {0x08025AF9FL, 0x0415FE2BAL},
	{0x0702A1F6BL, 0x025804FCCL}, {0x06AA99AE5L, 0x0378F0BCFL}, {0x0C63BAA83L, 0x00FB6902AL}, {0x01C38D6CEL, 0x005CB7467L},
	{0x0E358852EL, 0x0C008201CL}, {0x0A5E3503AL, 0x0AA133CA2L}, {0x0D880CF6CL, 0x049651091L}, {0x005A94A4AL, 0x026754470L},
	{0x0A4D4584DL, 0x0A8CD5FB9L}, {0x03C7A72EDL, 0x069E3BBD2L}, {0x0472331A2L, 0x0EF50FE64L}, {0x04B660B13L, 0x0837C71D7L},
	{0x0B1AC68A9L, 0x0EDEFDBA3L}, {0x0216EE2DDL, 0x07511865FL}, {0x00322FF35L, 0x0C3991875L}, {0x0DE701939L, 0x04ED0FA7BL},
	{0x01CC2C653L, 0x07F5EA4EAL}, {0x0539E9E29L, 0x0DE9BBE3AL}, {0x00C6EFB14L, 0x0F32F9FE2L}, {0x0BEC874ACL, 0x04671C17CL},
	{0x0C3258069L, 0x04E0BCA7EL}, {0x0D11CA8B3L, 0x085632351L}, {0x062075441L, 0x07102549DL}, {0x0DC7C2C6DL, 0x09B6FD4CAL},
	{0x0C7E487BDL, 0x07B143D5FL}, {0x0BDE70EAAL, 0x08887E5C5L}, {0x005ED0ABBL, 0x04B317594L}, {0x0567D217AL, 0x04DBA4554L},
	{0x019F1DB5DL, 0x0F46AFD7DL}, {0x0D60FA2DDL, 0x0D8F9F487L}, {0x005200C82L, 0x063BED4F9L}, {0x02DBC73D4L, 0x03B42034CL},
	{0x0B75A8D4AL, 0x0BA1D0AF8L}, {0x04B84A26EL, 0x076A76095L}, {0x041A05CA6L, 0x0E8888F9AL}, {0x04157128BL, 0x087271D81L},
	{0x0A3017B95L, 0x0DD1E63C0L}, {0x00E46E03CL, 0x060B319F0L}, {0x0EB8DF917L, 0x0A9AF9799L}, {0x0B2401E8FL, 0x010596503L},
	{0x0FB05C62CL, 0x04D5B1AE5L}, {0x02E558A66L, 0x0A71B10A8L}, {0x0C2A7E3C5L, 0x0B812EDD5L}, {0x0618557D0L, 0x006E81AE1L},
	{0x081555E01L, 0x0FAF51E57L}, {0x08BB072DEL, 0x03CC163BEL}, {0x0062E19D0L, 0x013D39F6DL}, {0x07C07ED7FL, 0x039B41B0DL},
	{0x063F33432L, 0x004DC6F16L}, {0x03469A6A2L, 0x01DC31300L}, {0x097E2AD38L, 0x0CCE18E53L}, {0x0D4E7C06AL, 0x0B9DD6A86L},
	{0x093ED76C1L, 0x06B100D22L}, {0x04B7F38C4L, 0x03B1200AFL}, {0x065F48EEDL, 0x0C15BDB85L}, {0x08913F0A2L, 0x09553F55CL},
	{0x01F25F59CL, 0x00F92F88BL}, {0x09FC20723L, 0x0B7AF4A9CL}, {0x09062BCDFL, 0x024036415L}, {0x08B8C7D37L, 0x0BF16EE6FL},
	{0x0E8BAC1B4L, 0x000703031L}, {0x03F6232EEL, 0x08F98D1D5L}, {0x0081D362DL, 0x0C4085BE1L}, {0x0CB534719L, 0x0362624DEL},
	{0x00F9BFB2AL, 0x05EABC524L}, {0x03D5FBBE7L, 0x0A4DFC56BL}, {0x0DD250EC9L, 0x0B05A8E0BL}, {0x067766E48L, 0x0FA93B69BL},
	{0x072CA61FCL, 0x0E913A674L}, {0x07898704DL, 0x01762F64EL}, {0x0EF7A23B2L, 0x0FAF81E81L}, {0x050D6D2C4L, 0x00B4D8695L},
	{0x03345240CL, 0x0D1E9D511L}, {0x00F3F93EFL, 0x0D643758FL}, {0x05E2C95F8L, 0x080E4EC45L}, {0x09694A39EL, 0x07844B3FCL},
	{0x0401E4468L, 0x016FB510BL}, {0x0E413F2EFL, 0x0E270400CL}, {0x00A1B437BL, 0x0742D1655L}, {0x029AEB1B4L, 0x023A83CAFL},
	{0x0AB34A221L, 0x0986A2A42L}, {0x02644BF2CL, 0x04CEB68D6L}, {0x013675F5BL, 0x0A4B28DC3L}, {0x0F9051C27L, 0x03B49F390L},
	{0x052A66C28L, 0x0781640D6L}, {0x0A4C2C8C5L, 0x0F2B2EDFEL}, {0x069F0B888L, 0x02295527DL}, {0x027AAC4E7L, 0x0803716EEL},
	{0x05666637BL, 0x0A42FB3B8L}, {0x0709D1FACL, 0x0F5D7AF62L}, {0x01CE66E02L, 0x0FCC56385L}, {0x0A1ABC9F4L, 0x032828789L},
	{0x0A872B71CL, 0x01D8673E6L}, {0x0A9C5C2E0L, 0x04548BE23L}, {0x01C1A60C9L, 0x01442D2D9L}, {0x078F91B5EL, 0x0201A4561L},
	{0x046DC6919L, 0x0E3298061L}, {0x01F39C360L, 0x0E3071A31L}, {0x0589AB0EDL, 0x0891B8D7BL}, {0x09C85BA06L, 0x06CFF4FA5L},
	{0x032835753L, 0x00629EA39L}, {0x0D1FB004EL, 0x0DD02D4ADL}, {0x0F2775D5DL, 0x05A32957AL}, {0x0FD7DA60AL, 0x00531A727L},
	{0x07A8692EBL, 0x07666904EL}, {0x0F10AAB69L, 0x0046BDA55L}, {0x0D991371BL, 0x069B6EBB5L}, {0x0BCB2EF5BL, 0x0EBB05C06L},
	{0x000C73ACFL, 0x0230194B0L}, {0x05E7682E0L, 0x099102D5AL}, {0x01D187D16L, 0x0C4769D5EL}, {0x0C73585F9L, 0x02E8C5D32L},
	{0x0E2641001L, 0x03DF8E57FL}, {0x0072FC7A5L, 0x07A13CDADL}, {0x09EDC017EL, 0x07D848C33L}, {0x02F1468E4L, 0x0AEB59CABL},
	{0x0125F428FL, 0x0942C838BL}, {0x00E2558C7L, 0x0A862CA4CL}, {0x07CFDE223L, 0x072EFD966L}, {0x0D440982CL, 0x08A2B3770L},
	{0x09E97D15AL, 0x038BD7EE4L}, {0x062772725L, 0x0240E0438L}, {0x0975B1025L, 0x0C59762F5L}, {0x0C6CA15B2L, 0x0A4EE2093L},
	{0x0772BAD73L, 0x0398BB69AL}, {0x0021752E1L, 0x0FCE89B71L}, {0x01F169A64L, 0x0759B59C2L}, {0x01680EF94L, 0x01B0E66F3L},
	{0x09E0DD7F8L, 0x087B74B9DL}, {0x0F014CBEAL, 0x0112E7F18L}, {0x0D41E6200L, 0x061ED8CFBL}, {0x0B2A306C3L, 0x0EF6BE8B0L},
	{0x0013B4DBBL, 0x0122F2DEDL}, {0x04B5E904FL, 0x084B2B1FBL}, {0x0F67477F9L, 0x0AB8C1C62L}, {0x0AB147A4FL, 0x0F015C8C9L},
	{0x0C2B700CAL, 0x00AF45B8AL}, {0x0D2E5B3E2L, 0x043923F2BL}, {0x06516E93FL, 0x03178EA25L}, {0x0D1C13B28L, 0x06D2BF510L},
	{0x0DF802036L, 0x04F16D764L}, {0x0B7C912E2L, 0x05FC00AA9L}, {0x01105A7C2L, 0x015B01446L}, {0x064DC494EL, 0x0187F7EC4L},
	{0x03AA58EE0L, 0x0C175A0ABL}, {0x0F9FADF2EL, 0x0B94A2273L}, {0x02A51B391L, 0x045468BA3L}, {0x04533B4B1L, 0x0201035C5L},
	{0x0E11838E6L, 0x0A132C63FL}, {0x07787D8C8L, 0x06F02A79AL}, {0x070EA1CCEL, 0x0D329506EL}, {0x072E76C72L, 0x0751E5803L},
	{0x0E6D74F3AL, 0x0DD3B3910L}, {0x043523FAFL, 0x06226690EL}, {0x023D0C248L, 0x0AE686165L}, {0x0FCD9618FL, 0x01759C9ADL},
	{0x037E493EAL, 0x04691F94EL}, {0x06C7AE2E2L, 0x0A29878D0L}, {0x01303C41FL, 0x0C5E5D0BAL}, {0x0B327B3E9L, 0x016E17785L},
	{0x0D54E35D8L, 0x01C4406C9L}, {0x0E2EFD363L, 0x04056D4EEL}, {0x060930423L, 0x03ABF8B6CL}, {0x0D7C25290L, 0x051D681CAL},
	{0x0C1F43312L, 0x03F356091L}, {0x0A4A12041L, 0x03A628E49L}, {0x0F960A194L, 0x0FBD6935AL}, {0x048AB4E94L, 0x0FA08E95CL},
	{0x009F87EA9L, 0x09F8216B6L}, {0x0B4C0CB7BL, 0x071CA94F1L}, {0x0E09A9B52L, 0x01A49E9A6L}, {0x007E087E5L, 0x0D0879E2AL},
	{0x09F48169EL, 0x05C1C1A28L}, {0x0212BA2E3L, 0x00660E7F7L}, {0x02401D16DL, 0x0750A9B3EL}, {0x012721D84L, 0x013638F56L},
	{0x071E6FCCFL, 0x066036BD7L}, {0x0DAD4E7B8L, 0x0E7628749L}, {0x0A5D665B5L, 0x02E188A14L}, {0x06A42F07FL, 0x0938CDEDFL},
	{0x0A1D02E4EL, 0x0BD3709E3L}, {0x0D1DA68C9L, 0x015B274E8L}, {0x083E7466AL, 0x02372C756L}, {0x01F7E30B7L, 0x06FF27995L},
	{0x02D18BD19L, 0x061C9F44CL}, {0x0352D4738L, 0x0915ECEE5L}, {0x0AE45746CL, 0x0762A60D6L}, {0x011F7BD4CL, 0x099C562B8L},
	{0x0F6AD8941L, 0x062A73CF2L}, {0x0D5DD72E4L, 0x06948552EL}, {0x02600FEBAL, 0x0162F47A2L}, {0x061CE872EL, 0x000D59827L},
	{0x01D8EA3B7L, 0x0A0C2C1F5L}, {0x0C3CAEBFCL, 0x08E8E39B4L}, {0x0EB08B656L, 0x012718ADCL}, {0x00DD1AE5DL, 0x0D4322AD4L},
	{0x080BD2979L, 0x04B4AA345L}, {0x00E04B042L, 0x0F1016B97L}, {0x00D6DDB3FL, 0x05C1F0A42L}, {0x0E64112DAL, 0x0E5FC0AEEL},
	{0x04128EC99L, 0x03300D1E2L}, {0x0A59AC3F5L, 0x0B0E2F9C8L}, {0x06C0F4D75L, 0x0E20BE816L}, {0x02C0FD3A3L, 0x042F33745L},
	{0x06EF1FCF5L, 0x068225DDCL}, {0x08A7E32E4L, 0x0CC1FC445L}, {0x0280E0B08L, 0x0C6540226L}, {0x0BF09E1C9L, 0x00D47A0E9L},
	{0x0C9176AAEL, 0x0FB812614L}, {0x0BCBFFF31L, 0x0169AEC1FL}, {0x0214A17E8L, 0x0F6EA8984L}, {0x090604C3CL, 0x005F877E9L},
	{0x0708914A5L, 0x0DA4D4CA8L}, {0x04B3DF8CAL, 0x0CC615147L}, {0x087E37005L, 0x074CC4E4EL}, {0x0CD15F4FCL, 0x06AE69A27L},
	{0x075491B08L, 0x0F656BF79L}, {0x016085FB1L, 0x0CF8623BBL}, {0x03ACA268FL, 0x05FFC6F46L}, {0x037160909L, 0x00C210BC2L},
	{0x0C6557FA9L, 0x07FAD6FA7L}, {0x03F20F2E5L, 0x010E7327CL}, {0x02A0D1856L, 0x07679CEABL}, {0x00E545B63L, 0x00BC9B9BAL},
	{0x064BF1196L, 0x045507C22L}, {0x0B594F375L, 0x0BDA69E8AL}, {0x0778D687AL, 0x0EB42894CL}, {0x022F0FA1BL, 0x046AEC3EEL},
	{0x050661FD0L, 0x05840D6FAL}, {0x077664043L, 0x0A7B148E6L}, {0x0005A05DBL, 0x0AC69813BL}, {0x093E8E61FL, 0x0DFD02B70L},
	{0x098695A78L, 0x0C89D8C1FL}, {0x08775DB7EL, 0x0EE1A4E9EL}, {0x0F784FF98L, 0x0BBDDE786L}, {0x0521D2F60L, 0x0C56FC05FL},
	{0x02EBAE25CL, 0x085388081L}, {0x0E4D1C2E5L, 0x073CFA193L}, {0x02BFB35A3L, 0x0169E891FL}, {0x05DA0B50EL, 0x0F83BC17BL},
	{0x00057D88EL, 0x08F1FD140L}, {0x0AD8A07BAL, 0x054C241F6L}, {0x0BCCFC90BL, 0x0DFAB8904L}, {0x0B58F98F9L, 0x0885410F4L},
	{0x030420A1CL, 0x0E6537F5CL}, {0x0A48088CCL, 0x082013E95L}, {0x08AD0AAB0L, 0x0D506C537L}, {0x06AABC831L, 0x054DABCC9L},
	{0x0BC8A89E8L, 0x09AD46AB5L}, {0x0F8E3673AL, 0x00EAE7881L}, {0x0B54EC8A2L, 0x027BE6EB6L}, {0x06C2545C7L, 0x08E9D94DCL},
	{0x0861E6600L, 0x08BB2A25BL}, {0x0A88282E6L, 0x0D6971FCAL}, {0x02DF943F1L, 0x0C7C24593L}, {0x0BCFB2FA9L, 0x0F5ADDA4CL},
	{0x0ACF08F75L, 0x0D9DE375EL}, {0x0967F0BFFL, 0x0FBDEF351L}, {0x0F2011A9DL, 0x0B31488BCL}, {0x0481F46D8L, 0x0B91A6C09L},
	{0x01F2EF548L, 0x0745619AEL}, {0x0D1B9D044L, 0x05E612534L}, {0x014573F86L, 0x00DB30833L}, {0x0317FBA54L, 0x0DAC44C03L},
	{0x0D0AAC857L, 0x05C2B474BL}, {0x07850E3F7L, 0x01D41A364L}, {0x07309A1BCL, 0x093AFE6F6L}, {0x0773C7B2DL, 0x038CB6969L},
	{0x0ED73D9B4L, 0x0913DC335L}, {0x05D3452E7L, 0x0296F8EF2L}, {0x03FF8604FL, 0x077E70007L}, {0x00A478943L, 0x0F22FE20DL},
	{0x04898466DL, 0x024AD9C7CL}, {0x08F650F33L, 0x083E9A6CCL}, {0x038447B2FL, 0x0A77D8874L}, {0x0EB9EE4B7L, 0x0FACFB90EL},
	{0x0FF0BF073L, 0x0F359B200L}, {0x00EE218CDL, 0x039B11BE3L}, {0x09EDDC45BL, 0x025504C2FL}, {0x008429C77L, 0x05FBDDD5CL},
	{0x0F4CAF7C7L, 0x02F6225E1L}, {0x0E9BD7FB4L, 0x03CD5DD57L}, {0x031B37AC5L, 0x0F08F6D27L}, {0x082449184L, 0x0F1083DE6L},
	{0x045D75C67L, 0x0A8B8E51FL}, {0x002E512F7L, 0x08D47FC19L}, {0x031F67D9CL, 0x0270CCC8BL}, {0x05992E3EEL, 0x0E090FBDEL},
	{0x0F321FD55L, 0x07E6BF28AL}, {0x0785A1378L, 0x02AF55827L}, {0x07E76CCB0L, 0x09CD6872DL}, {0x07D2D9295L, 0x03B750513L},
	{0x0EFD7EBAFL, 0x0816C5C52L}, {0x04A1C5056L, 0x014010282L}, {0x018536921L, 0x05DFD8F2BL}, {0x0EF168E99L, 0x0C4B76D95L},
	{0x027DB3636L, 0x0F1A90277L}, {0x05A2BFB70L, 0x05B69083BL}, {0x00E7D53DFL, 0x06C60E567L}, {0x09D5BC7EBL, 0x0BA360274L},
	{0x0BD2BCF1BL, 0x0BE4306E9L}, {0x0B787E2F8L, 0x0E01F5B40L}, {0x032F59AEAL, 0x0C72187FFL}, {0x0A8DD5D89L, 0x0ED0203AFL},
	{0x09FC9A45CL, 0x0B82A57A8L}, {0x0704F17BDL, 0x0C1110B92L}, {0x0C3B91D42L, 0x0704F87E5L}, {0x000BD3074L, 0x06D3B5218L},
	{0x0CFB4E6DBL, 0x01F6FF5B4L}, {0x07745A8DEL, 0x0FF61F831L}, {0x091DAFEF7L, 0x086AAC317L}, {0x0B5E960BCL, 0x049A1EEEEL},
	{0x04BFB65A6L, 0x0C3F0E01DL}, {0x0CB98873DL, 0x07BFD322EL}, {0x0CC382CF9L, 0x0D8516CA7L}, {0x0B752ED41L, 0x06364D601L},
	{0x0158042CEL, 0x0B4CE28C3L}, {0x06B38A2F9L, 0x033E7C967L}, {0x034F3A738L, 0x078564373L}, {0x0F729B723L, 0x0EA841C60L},
	{0x03B616B44L, 0x002F9BDC6L}, {0x069252BF1L, 0x0582DBDFDL}, {0x009FB7ED3L, 0x064A8869DL}, {0x0934CDE52L, 0x0AEE1AE2DL},
	{0x0AE90D106L, 0x0AD619F06L}, {0x0A47FE057L, 0x0CBB1EFD1L}, {0x01B4093CCL, 0x0AE460613L}, {0x08CBC52DFL, 0x0CF9B7E27L},
	{0x06F1CA416L, 0x08537CDA4L}, {0x03C0503FAL, 0x08A916D11L}, {0x08AF20502L, 0x03432D4D7L}, {0x0C26A13A8L, 0x02DA2AB8EL},
	{0x07CE4B572L, 0x0CB49499EL}, {0x020E972F9L, 0x096CE389EL}, {0x036E1C485L, 0x0287B0EF7L}, {0x0557421CEL, 0x0D7F62432L},
	{0x0D70A123BL, 0x05DB812D5L}, {0x0521A2F36L, 0x0F0396068L}, {0x04F3DCF65L, 0x058017655L}, {0x026DB8C31L, 0x0EF97FB22L},
	{0x08E7CDC32L, 0x02C743869L}, {0x0D19838D0L, 0x0A600D580L}, {0x0A5C62892L, 0x0D6E34A00L}, {0x0537034F1L, 0x034950F70L},
	{0x0833CD385L, 0x0587EAB4AL}, {0x0BC738FB6L, 0x0A92597F4L}, {0x048BDDE1CL, 0x0A1135B17L}, {0x0DD71390EL, 0x0E6D07F1BL},
	{0x0D4492826L, 0x0D1D46B78L}, {0x0D59B32FAL, 0x0FA96A6B5L}, {0x048E0D1D3L, 0x0C890CA6CL}, {0x0A4CF8B69L, 0x0D5683DF3L},
	{0x073A2D923L, 0x0977778F3L}, {0x04B00337BL, 0x0975513C4L}, {0x0857020F7L, 0x04D79751DL}, {0x0C86B2A10L, 0x0205D4738L},
	{0x06E59C76EL, 0x0BA77D2CBL}, {0x00DC17058L, 0x08160CC3FL}, {0x02F4DCD68L, 0x00E908D0CL}, {0x02A431614L, 0x0B98F9FC9L},
	{0x0A65C02F5L, 0x02AC489E0L}, {0x02DD01B73L, 0x0C9B9C2E7L}, {0x00577B726L, 0x01D04D347L}, {0x0E8896F65L, 0x09F0E4498L},
	{0x03CADABE9L, 0x0D75F8C42L}, {0x08A4CF2FAL, 0x05D6E15ECL}, {0x049EEEE30L, 0x078C585E0L}, {0x0F31BF513L, 0x0D2EA45C4L},
	{0x02E4A801BL, 0x0E146DD01L}, {0x033F537BFL, 0x02E61C53FL}, {0x0CAA27188L, 0x021D275C5L}, {0x05BEAD80EL, 0x05203944DL},
	{0x05E35C299L, 0x0487A7B1DL}, {0x04AFBC8D1L, 0x05CB0B2DEL}, {0x0A8C3523DL, 0x0273DC108L}, {0x000170847L, 0x03E792002L},
	{0x0DA6D4165L, 0x0EC0B6676L}, {0x09E4E973FL, 0x0D85DFCCAL}, {0x0D321803FL, 0x089E55A87L}, {0x0F29085DCL, 0x0584C1825L},
	{0x0A4F11E9DL, 0x0EDDAAE2CL}, {0x03EEDC2FBL, 0x0A0468303L}, {0x04BEC0B8EL, 0x029E94154L}, {0x042565FBEL, 0x0DF5C5E85L},
	{0x0CAD34702L, 0x03B05332FL}, {0x02CEA4BF4L, 0x0C57D68AAL}, {0x010E5D21AL, 0x0153B748DL}, {0x0EE7A76EDL, 0x093B9D042L},
	{0x03D01BDC5L, 0x0C68D157FL}, {0x077240059L, 0x03800A98DL}, {0x0224AF703L, 0x05FDA04F4L}, {0x0D7EAEA69L, 0x0A473B05CL},
	{0x0FE8D70D4L, 0x0BE42441CL}, {0x00FBB230CL, 0x0F7E127BDL}, {0x091EC5949L, 0x0E5C6D2B7L}, {0x01DA7AB32L, 0x0127AEDA2L},
	{0x00B569141L, 0x0F464CF06L}, {0x0E39F82FCL, 0x0031EF23BL}, {0x04DEB18DCL, 0x0C90E0CD8L}, {0x090A2C958L, 0x0CCCE6656L},
	{0x0667BFEFAL, 0x076C4983DL}, {0x025C04F48L, 0x06D981A05L}, {0x0562723ACL, 0x00A947436L}, {0x0710924CCL, 0x0D47F2D57L},
	{0x01DEEA8F0L, 0x05580BEC1L}, {0x0A45E58D2L, 0x013609F2CL}, {0x0ACC08CD9L, 0x0878748F0L}, {0x0AEBDDC8CL, 0x0296D4195L},
	{0x012ADBF44L, 0x0818921A2L}, {0x07F28AFC9L, 0x016745190L}, {0x05FA63252L, 0x052B759F8L}, {0x028AFD199L, 0x0DBA7B13FL},
	{0x063BA04F4L, 0x0FAEFE1D0L}, {0x0A84052FCL, 0x067E66062L}, {0x04FD93529L, 0x07933C84CL}, {0x0FFFD23F3L, 0x0CA4F7F27L},
	{0x00214B5E2L, 0x0C092FE5BL}, {0x01EB5438DL, 0x004A4CD70L}, {0x09C69844DL, 0x0EE0D73FEL}, {0x00398C2AAL, 0x00524795CL},
	{0x0FDCAA32CL, 0x0E3835823L}, {0x0D087905BL, 0x0EEB086DBL}, {0x0363611AEL, 0x0AF248BECL}, {0x07571BEAEL, 0x0AE56D1EEL},
	{0x035CEEEC3L, 0x043D00F48L}, {0x0E0963B85L, 0x026188C84L}, {0x01D600B6CL, 0x0CE97D138L}, {0x033B6F7F0L, 0x084E586CDL},
	{0x0CB1E87A8L, 0x0006A02BAL}, {0x05DF212FDL, 0x0BACEDF89L}, {0x050D84277L, 0x0295883C0L}, {0x04E488D9EL, 0x0C7B187E8L},
	{0x0ADBC6CD9L, 0x01A515379L}, {0x006AA57C2L, 0x09BB07FDBL}, {0x0D1ACD5DFL, 0x0D26673A6L}, {0x0A6287089L, 0x047DAC661L},
	{0x0DDA79E58L, 0x06196F175L}, {0x00DA0D8D3L, 0x0C9007C7AL}, {0x0BFBDB674L, 0x0D8C1CFE8L}, {0x04B44A0C1L, 0x014406227L},
	{0x059EE2D33L, 0x01517ECDEL}, {0x06103B742L, 0x045ACB677L}, {0x0DA2BE476L, 0x02A785868L}, {0x04ECD2D56L, 0x04D135A4AL},
	{0x02373FA5CL, 0x016F52484L}, {0x001A3E2FEL, 0x01D964DB0L}, {0x052D66FC5L, 0x0DA7D4F44L}, {0x09D94F738L, 0x0B42380BAL},
	{0x0495413C1L, 0x05410B988L}, {0x0FF905B06L, 0x032DC2246L}, {0x017EE2661L, 0x0C6CF726EL}, {0x039A71E68L, 0x088801266L},
	{0x0CC839983L, 0x0FF989BD8L}, {0x04ADA205CL, 0x0A560532AL}, {0x039334B49L, 0x0007D02D4L}, {0x0221782E4L, 0x0994AF270L},
	{0x08D0F5CA3L, 0x0E85ECA75L}, {0x0D261430FL, 0x06430E15AL}, {0x098D5BD8FL, 0x09669D0A8L}, {0x058D543BDL, 0x007412FD7L},
	{0x09AD77D0FL, 0x01D70456FL}, {0x0B654A2FEL, 0x0706EBCD7L}, {0x054D47C12L, 0x07AA20AB9L}, {0x0EBEF51D3L, 0x0B195987BL},
	{0x0F5EDDAB9L, 0x0AFEF1EA6L}, {0x0E8856F4BL, 0x0DAE8D4A1L}, {0x06D1087F2L, 0x0AB387226L}, {0x0CC37BC46L, 0x0B9466F7BL},
	{0x0AC5F84BFL, 0x08E9B343AL}, {0x0770368D5L, 0x080BF49D9L}, {0x0B3BAE01FL, 0x0381A46D1L}, {0x0F9EB7406L, 0x01E3483C9L},
	{0x0A11F9B12L, 0x0BAA5A71BL}, {0x042DECFCBL, 0x073D41B4DL}, {0x066909699L, 0x0034A57D8L}, {0x073EC7913L, 0x0B07FF354L},
	{0x0F22CE0B3L, 0x023FB6739L}, {0x06BF672FFL, 0x0C4452A0EL}, {0x056D39960L, 0x02AC7B63DL}, {0x03A2BCB7EL, 0x0BF17A14CL},
	{0x0918581A0L, 0x0F9AE74B4L}, {0x0E17B6380L, 0x061F4871DL}, {0x0A353D884L, 0x09F9171DEL}, {0x05EC66A25L, 0x0FAFCBC81L},
	{0x08C3C8FFBL, 0x01C9EDE8CL}, {0x0A33CB05DL, 0x05B0F3078L}, {0x03D3075F5L, 0x050B77ADDL}, {0x0C0BE5629L, 0x0932E1302L},
	{0x0C43FCA82L, 0x07CEB85A1L}, {0x0B34B4B88L, 0x093684620L}, {0x0245A6FA3L, 0x06F2BDF18L}, {0x08EE49F7AL, 0x079BDC8E1L},
	{0x05A805366L, 0x039868813L}, {0x020A732F0L, 0x0271D9925L}, {0x057D1A6BDL, 0x0DBEC71A1L}, {0x099762518L, 0x0AC89B90DL},
	{0x03C2E4898L, 0x0436DD9D2L}, {0x0D95077C4L, 0x008103988L}, {0x0E8953915L, 0x083F97196L}, {0x0E1550803L, 0x03CA20886L},
	{0x06C187A26L, 0x09AA177EEL}, {0x0D066F8D6L, 0x0375F2627L}, {0x0C6A61ACAL, 0x08954BDC9L}, {0x09682384CL, 0x00928A45CL},
	{0x0E850F9F2L, 0x04E226247L}, {0x024B9D745L, 0x0B2FC7013L}, {0x0E11448BCL, 0x0DB1C5648L}, {0x099FBC5D1L, 0x032EB9C6EL},
	{0x0B2E4D61AL, 0x04F01AAFDL}, {0x0D559F200L, 0x08AE5075DL}, {0x069C0B30BL, 0x07B113D25L}, {0x0E8C19FB3L, 0x0A9FBC2DEL},
	{0x0D8C6FF8FL, 0x08D3C2FE0L}, {0x0C2457B09L, 0x0AF2CECE3L}, {0x02ED88AA7L, 0x06762704EL}, {0x084E5B6E2L, 0x07D68559BL},
	{0x04CF57552L, 0x028A40130L}, {0x00D8F406FL, 0x012BF1DC6L}, {0x0402DAF90L, 0x0B101F1C5L}, {0x06D452A6EL, 0x08E123495L},
	{0x00C703861L, 0x0117930DDL}, {0x0A5265301L, 0x0D1909BF6L}, {0x0AFDF11C6L, 0x047FDDE89L}, {0x0A302EB37L, 0x0EC1951FBL},
	{0x0194949CEL, 0x0468BBBC7L}, {0x0890AC201L, 0x0DDBD7684L}, {0x06BCED059L, 0x02B35F899L}, {0x0371DF95EL, 0x0A67DDAAFL},
	{0x0746EB687L, 0x0D8FB840EL}, {0x0BB3B8F4DL, 0x037389E5EL}, {0x0641AEB39L, 0x05CCB7007L}, {0x0177454C1L, 0x0AE1EA190L},
	{0x03BD1608DL, 0x0B7A7AA92L}, {0x04AB988E7L, 0x0ED0F0375L}, {0x0CAA34466L, 0x0D9AE34B1L}, {0x044180C81L, 0x0030CC5EEL},
	{0x0309167D1L, 0x0D3B01D73L}, {0x01583EFCEL, 0x0E023C5EAL}, {0x06D99EAEFL, 0x0B4DE55C9L}, {0x0BE1A019EL, 0x0A5572579L},
	{0x081ADCC71L, 0x05C16DDA1L}, {0x03EBB8201L, 0x03195E4ABL}, {0x06DCCEDA6L, 0x0DB5AB41DL}, {0x0856863F8L, 0x0A4EFE360L},
	{0x020076D7FL, 0x022BAEA2CL}, {0x0A4208382L, 0x0DE5341B9L}, {0x0BA4C3CCAL, 0x040247FBFL}, {0x0A9F302AFL, 0x0E0C4FEA5L},
	{0x01BBD5BB9L, 0x035BA44E4L}, {0x076E2D060L, 0x0C85FFA14L}, {0x04429D93BL, 0x0014B78BDL}, {0x01BECFEA3L, 0x078055527L},
	{0x053B1A641L, 0x0A5F7FB09L}, {0x086F16B8AL, 0x000B7F0DDL}, {0x03B44C3F9L, 0x010CEDDF9L}, {0x0D92137F5L, 0x06E84FA06L},
	{0x0E9F13F25L, 0x06291FE7BL}, {0x0E35D5202L, 0x0946D53D2L}, {0x06FCB0AF4L, 0x07C8F7F91L}, {0x0D4A3CD93L, 0x09150FB31L},
	{0x0CB9F2466L, 0x06C884F3AL}, {0x0AC1687C7L, 0x0756FF324L}, {0x0FF8F9D5CL, 0x0349D7F77L}, {0x03C83A08EL, 0x021894AAAL},
	{0x0FB8A56E5L, 0x0C3BDED46L}, {0x0A31B18E8L, 0x0A4BFE0C4L}, {0x0CDA06E01L, 0x03AF8BBA9L}, {0x0E1BFD0C6L, 0x0FEFFE670L},
	{0x077C1D5B0L, 0x0774ED8A0L}, {0x0F76EF747L, 0x02F5B2AB0L}, {0x0F80E9C03L, 0x08CAF5439L}, {0x0E4385D5BL, 0x017B2CE93L},
	{0x04156B2D9L, 0x0681C1056L}, {0x0A80E1203L, 0x0F735C1F9L}, {0x060C91742L, 0x02CA43B05L}, {0x033FF273DL, 0x09ED204F3L},
	{0x06737DB5EL, 0x0B647A559L}, {0x0950B9B0BL, 0x00D7BA68FL}, {0x035C1EEEEL, 0x018F67E3FL}, {0x0CF125E6DL, 0x0523F97BFL},
	{0x0DB664110L, 0x052B087A9L}, {0x0D0455061L, 0x07F0FD773L}, {0x0472603D6L, 0x05295FFA6L}, {0x0B883C2E9L, 0x073E976C9L},
	{0x09BE21420L, 0x03A85B646L}, {0x068DC7304L, 0x03EEF55A3L}, {0x0B6C8751CL, 0x0F880DC69L}, {0x0FE3083B2L, 0x0D1F09310L},
	{0x0A8BA259CL, 0x07FA73120L}, {0x05CBFE203L, 0x04A1D3020L}, {0x072B734AFL, 0x0DCC9F68AL}, {0x0824A91D8L, 0x09B441CC4L},
	{0x003D08246L, 0x001060A67L}, {0x08EE09F40L, 0x0A49758FBL}, {0x07B043F7FL, 0x00D5F7EE7L}, {0x062A2FC5BL, 0x093E5E3B5L},
	{0x0BA424C4CL, 0x0D0C220FBL}, {0x00D7EA8EAL, 0x05A5FCD12L}, {0x0D19D98ACL, 0x08A3132A2L}, {0x08F56A41BL, 0x0E8E3F702L},
	{0x0BF02439FL, 0x00CCC93DCL}, {0x0E8490FC0L, 0x05D738F86L}, {0x074834E26L, 0x0556153A9L}, {0x00947A929L, 0x09A2E67ADL},
	{0x0001FA840L, 0x08522530AL}, {0x00161A204L, 0x0AEE49E47L}, {0x074B641FDL, 0x07CFEB2FEL}, {0x0D096FB83L, 0x089B62585L},
	{0x0AF78493DL, 0x04BC56085L}, {0x077D6A385L, 0x04BA30B66L}, {0x0B1469001L, 0x0F1C87DAFL}, {0x0F4319A3AL, 0x0D5AB30CAL},
	{0x09A2F3778L, 0x06EC5CA5DL}, {0x04998E062L, 0x035BEB4C1L}, {0x05B133D72L, 0x0B2EE769EL}, {0x06619963EL, 0x06DDD875BL},
	{0x0E322820FL, 0x0DE137172L}, {0x059B68B8DL, 0x07D17BA79L}, {0x0324D2730L, 0x0C152DBD9L}, {0x0145FDF8FL, 0x0535C3C2AL},
	{0x078731BF3L, 0x08BAD75E4L}, {0x0B6127205L, 0x001BC0D7EL}, {0x076B46E4BL, 0x02D137D72L}, {0x02FE1652DL, 0x086383D56L},
	{0x05A11F025L, 0x09594C593L}, {0x060CBA7D9L, 0x0E2BFBDC1L}, {0x00688E192L, 0x0D5206D57L}, {0x087B04818L, 0x006518CDFL},
	{0x08A0B32A3L, 0x0FCC863AFL}, {0x076C138EBL, 0x0010EAA60L}, {0x0D499C247L, 0x0EB8BB99AL}, {0x03CED7851L, 0x0E3C71895L},
	{0x00643B17FL, 0x090595E08L}, {0x0CA14075AL, 0x08CABE45CL}, {0x00FF7F049L, 0x03D334219L}, {0x03F66F5E6L, 0x00C9A00B7L},
	{0x0D0D78EA7L, 0x0913896BEL}, {0x06BC43205L, 0x054947CA6L}, {0x077B37B98L, 0x0DD3839E6L}, {0x07E3CCFC8L, 0x083AA4627L},
	{0x0F6B9B71DL, 0x0EF532BB1L}, {0x068B0BB1EL, 0x07ADB603CL}, {0x04CBB4234L, 0x0C9896C10L}, {0x01A40E6F7L, 0x04707D9D4L},
	{0x06AD82DDFL, 0x07BDB0D01L}, {0x0A3FA7064L, 0x0EC5E911FL}, {0x05E10671DL, 0x00328FD86L}, {0x003B05A73L, 0x068C1A8EEL},
	{0x02A53F0FEL, 0x063903CAEL}, {0x03B819316L, 0x0AB3F1F4FL}, {0x0CDB2D953L, 0x09A14CA5AL}, {0x0497D2B4CL, 0x0C6C8D534L},
	{0x0372C015BL, 0x0A8B3B898L}, {0x02F65F206L, 0x0B76CEACDL}, {0x079B188E6L, 0x08D6DF46AL}, {0x0DD783963L, 0x0711C5EE8L},
	{0x092416E04L, 0x03A1280DFL}, {0x051A6BF53L, 0x011E71297L}, {0x082FD93C6L, 0x0BEF26CC8L}, {0x0ADDF94D6L, 0x088CD25E9L},
	{0x049B4280BL, 0x009DEA653L}, {0x0D024C8ECL, 0x0C7BE87BEL}, {0x0D896FCE3L, 0x03BD53082L}, {0x0DA834C96L, 0x0DDBB3927L},
	{0x04E742F6EL, 0x035E71934L}, {0x0ABFE1FD3L, 0x0CAD34933L}, {0x08B7CA26DL, 0x00605418AL}, {0x0547541A3L, 0x08FF6A9C2L},
	{0x09F80740EL, 0x0BE3DD962L}, {0x0D416C207L, 0x01B3459F4L}, {0x08BBFA533L, 0x02D81B0EEL}, {0x02BC3930DL, 0x07E9E67B9L},
	{0x03EEA25FCL, 0x074E1E6EDL}, {0x04A8BC397L, 0x0B8F2B502L}, {0x0C83FF457L, 0x0925B6B80L}, {0x0406E32B4L, 0x0BA7362EEL},
	{0x029901336L, 0x097D140B5L}, {0x00C5D0065L, 0x0A20E7E6DL}, {0x0620D91B8L, 0x06372747EL}, {0x0A1572EB9L, 0x052A5C970L},
	{0x062945EDEL, 0x0072EF7DAL}, {0x02C6CAB9FL, 0x0DA667416L}, {0x049377B76L, 0x072E6C9CAL}, {0x06F8C670AL, 0x038347E5FL},
	{0x0F7E5F7B2L, 0x0B4B8FB4CL}, {0x089C88207L, 0x06E1CC71BL}, {0x08DAEB281L, 0x0DEA67B52L}, {0x07A1F0DA8L, 0x07B0F7F7BL},
	{0x0D982DCE3L, 0x0CEA04B0CL}, {0x03371C7DCL, 0x04F1E676DL}, {0x00E7245E9L, 0x086B46B48L}, {0x0D2FEE093L, 0x0FB28BEF3L},
	{0x0097D0E62L, 0x025E4E918L}, {0x0497658EDL, 0x07E5E641DL}, {0x0EB83268EL, 0x08C1FB77AL}, {0x0871A10DBL, 0x0D8AE5AC9L},
	{0x095B49D4DL, 0x0C965D471L}, {0x09DD9275CL, 0x0F9FAAE09L}, {0x006F15480L, 0x0DEC640FAL}, {0x07A949D60L, 0x0F16142DCL},
	{0x06F496A66L, 0x0CA431C17L}, {0x03E795208L, 0x0C1E43642L}, {0x08EACDFDFL, 0x08ECB37D7L}, {0x0C96A6742L, 0x06871884CL},
	{0x0852B93DBL, 0x0196EA11AL}, {0x02B66CB10L, 0x0E72A1AD8L}, {0x053B4A67BL, 0x07A2D6AF0L}, {0x0658D8E72L, 0x03CEE0BF8L},
	{0x0F95909ADL, 0x0A4E7836AL}, {0x076A09066L, 0x059BE5BBCL}, {0x06509BB64L, 0x0B4CCFB77L}, {0x05EEEF2FEL, 0x04D98EA02L},
	{0x0B9D5CCBDL, 0x09CBCB207L}, {0x00E37B319L, 0x0188ED9ECL}, {0x0D4BB2D99L, 0x04BB7C83AL}, {0x094ABB3C7L, 0x0BB9F1769L},
	{0x0C6ADED19L, 0x0D1CE3EF1L}, {0x0E22A1208L, 0x025BCA469L}, {0x080AAEC2CL, 0x02EF0F24BL}, {0x018B5C1EDL, 0x066F3900DL},
	{0x021C34AC3L, 0x0533D0638L}, {0x0245BDF55L, 0x08E36CC44L}, {0x099E7F70CL, 0x06F866AB8L}, {0x0F80D3C50L, 0x07D94570EL},
	{0x0D835F4D9L, 0x032E92CCCL}, {0x0A3D9D8EFL, 0x0340E416BL}, {0x0EF805039L, 0x0EC683E63L}, {0x025B1E410L, 0x0C2827B5BL},
	{0x0DDF50B2DL, 0x06EF39FADL}, {0x07EA43FD5L, 0x0372203DFL}, {0x0926606A3L, 0x0B7984F6AL}, {0x0AFB2E92EL, 0x064DDEBE6L},
	{0x02EF250CDL, 0x0D7495FDBL}, {0x097DCE209L, 0x088941390L}, {0x082A9097AL, 0x0DE15BECFL}, {0x076F13B88L, 0x06365A9DEL},
	{0x0CD5BF1BAL, 0x0ADFC6C46L}, {0x01D41D39AL, 0x015427FAFL}, {0x0DF29589EL, 0x043EF6960L}, {0x08B9CDA3FL, 0x0AF4AA413L},
	{0x0B802FF05L, 0x0C0FCC61EL}, {0x0D0032077L, 0x00F5D280AL}, {0x06906E50FL, 0x00505726FL}, {0x0FC84C633L, 0x0478C0B94L},
	{0x0F1053A9CL, 0x0203A7D33L}, {0x0EF11CB92L, 0x047B63EB2L}, {0x05020DFBDL, 0x02379C7AAL}, {0x0BACA0F84L, 0x02D0BB073L},
	{0x08656D371L, 0x0EDD471A5L}, {0x05C7DA21AL, 0x0DB6B81C8L}, {0x094A716C8L, 0x08F3A7933L}, {0x0C54C9522L, 0x060D7A2AFL},
	{0x068F4B8A2L, 0x0F7BBC164L}, {0x00636E7DEL, 0x0BC6E211AL}, {0x0156BAA20L, 0x037586928L}, {0x02D2B781EL, 0x0E000F018L},
	{0x098EEEA30L, 0x04EFF6F70L}, {0x00C3C68F0L, 0x0EBBD1EB9L}, {0x0F28C8AD4L, 0x03DB2B55BL}, {0x0C258B856L, 0x0BD769CEEL},
	{0x014267A0CL, 0x0F2805AD9L}, {0x0608F475FL, 0x0664A68A5L}, {0x01DEBB8D6L, 0x08F6A4FEAL}, {0x0C5C135EBL, 0x0E63984F0L},
	{0x0EEBA4624L, 0x0F35F928FL}, {0x0012F721AL, 0x03E33F0EFL}, {0x095963315L, 0x02F6F25B7L}, {0x014970FCDL, 0x05D59BA60L},
	{0x0049C6FAAL, 0x0328A2782L}, {0x0FE1BEB13L, 0x0547AD475L}, {0x05AAEFBB1L, 0x02BB068D1L}, {0x0B0BB26FCL, 0x021B64D2DL},
	{0x078CBE56CL, 0x0DDF209C2L}, {0x03965B079L, 0x0C60D0558L}, {0x07CF31FAAL, 0x0655FE957L}, {0x0A92B9A78L, 0x032602C27L},
	{0x03846A97BL, 0x0C5C7386FL}, {0x0D1FCC31BL, 0x085EE9399L}, {0x0DBA581E0L, 0x0FC4BC61BL}, {0x0DFD85B41L, 0x0A077598EL},
	{0x0561FB9D8L, 0x0FADAB459L}, {0x0B5D0321BL, 0x0921B6E16L}, {0x097944063L, 0x0DF84E03BL}, {0x063E36968L, 0x05BCBC331L},
	{0x0B0342691L, 0x08C498C90L}, {0x0E701FF58L, 0x0FB8686E0L}, {0x090E05C43L, 0x000196899L}, {0x0434AC4DBL, 0x0526C9922L},
	{0x067A7D098L, 0x06B05A224L}, {0x0768FF8F1L, 0x0915DFB07L}, {0x0F679B470L, 0x08DFC2C43L}, {0x070EF7C9BL, 0x0B76ABD70L},
	{0x06C67E8EBL, 0x0870E1505L}, {0x0416A5FD8L, 0x09472CD7CL}, {0x0A96F6AFAL, 0x0682C4E5BL}, {0x0FAE081A8L, 0x059A52D1BL},
	{0x0BD733C8BL, 0x00064D533L}, {0x06A81F21CL, 0x0E5E3DD3DL}, {0x099925DB1L, 0x08FA8ACAFL}, {0x0B13ED302L, 0x0583DDBF2L},
	{0x05CDDDD89L, 0x0D608E2BEL}, {0x0E0F6F39CL, 0x082A2394BL}, {0x0E622ADD4L, 0x0F4726751L}, {0x0D6CA72B9L, 0x09422E637L},
	{0x04783DBC3L, 0x0E9083C86L}, {0x0A3B8407AL, 0x07CBDE2B7L}, {0x070F04945L, 0x0B6A9604FL}, {0x047B26EBEL, 0x02C544DC9L},
	{0x08087175BL, 0x05945E39CL}, {0x0B2C7DB95L, 0x0B405F86FL}, {0x0671A3303L, 0x0C41DC58BL}, {0x005F7A70FL, 0x012D3E298L},
	{0x015D8AF4FL, 0x016EFE70EL}, {0x01F33C21CL, 0x048BB4B64L}, {0x09B917A1EL, 0x020DD6723L}, {0x0008A3DADL, 0x045BFE4C4L},
	{0x0F7759470L, 0x010D737CDL}, {0x0D9EC07D1L, 0x029BDEBB6L}, {0x02C550E66L, 0x0E8EB6709L}, {0x0685910A8L, 0x0D5D8323CL},
	{0x02750C6FFL, 0x0770BD5D9L}, {0x0DFE188F3L, 0x0580DD856L}, {0x0FA76EE1BL, 0x0EE46A33BL}, {0x01E8540E0L, 0x0A24DDE02L},
	{0x0A39746CAL, 0x02B9CC032L}, {0x023346751L, 0x0D3A91242L}, {0x024D40C1DL, 0x030FD4DCBL}, {0x0100FCD75L, 0x0DC10B625L},
	{0x07D2C22F3L, 0x01D6A08E8L}, {0x0D4D4821DL, 0x0AB93BA8BL}, {0x0AC9F876CL, 0x0D0F22398L}, {0x06FC5A758L, 0x04220FC85L},
	{0x0930E4B68L, 0x06B959DEBL}, {0x0C1D10B15L, 0x0C1C99E21L}, {0x061975FF8L, 0x0CC4466C1L}, {0x0FBE8CE87L, 0x0068D8F42L},
	{0x0073CB12BL, 0x0061E7F3BL}, {0x00C1BD07BL, 0x0335DCF05L}, {0x083FC73E1L, 0x016E3E738L}, {0x0E4593203L, 0x027476E5BL},
	{0x0C7B8853AL, 0x0FED3AED8L}, {0x0A4A2E31EL, 0x0E23D4D35L}, {0x0E29EE527L, 0x0ADDEC4FBL}, {0x02A16F3DCL, 0x0854E8BA2L},
	{0x0D58095A6L, 0x023F52AC2L}, {0x08886521DL, 0x0FF6B28B2L}, {0x0AE9EA4B9L, 0x08017EE1CL}, {0x0BE1001F2L, 0x040920556L},
	{0x03FA60250L, 0x0B554F2F9L}, {0x0BAB60F6AL, 0x058E5408DL}, {0x0A7DAB089L, 0x0B1AD6679L}, {0x09E786C65L, 0x04733DB57L},
	{0x0E619BC56L, 0x09411188DL}, {0x0394418F4L, 0x00EBDB5A4L}, {0x00D6308B6L, 0x03E902A34L}, {0x0BB2C1425L, 0x0AC31FF94L},
	{0x0EBD8B4AAL, 0x0B01A8B6EL}, {0x0151F7FEAL, 0x002C17718L}, {0x0A059BE30L, 0x009CF4C3BL}, {0x0351D1933L, 0x04E7C5F3FL},
	{0x04CE5185AL, 0x039704B9CL}, {0x03D37121EL, 0x0523397E9L}, {0x0A08CB107L, 0x0304CAA80L}, {0x00C6C6B9DL, 0x03D041D27L},
	{0x0EB4EB947L, 0x00F235817L}, {0x0A3AC13AFL, 0x0FFF1F3F8L}, {0x0ED1C011BL, 0x0A5166531L}, {0x021071A44L, 0x089F9285CL},
	{0x0D6F5A782L, 0x01213B2EFL}, {0x0766E507CL, 0x0E90DAC53L}, {0x087E9AD8CL, 0x0673C6E20L}, {0x092EF0648L, 0x0112B8FEEL},
	{0x01FF9F329L, 0x082616904L}, {0x0858CFBA7L, 0x02165A20BL}, {0x07E13974AL, 0x075A0C37BL}, {0x040254F99L, 0x007AA24BCL},
	{0x0A4498B0EL, 0x04FFB6D76L}, {0x0E2E8E21FL, 0x0B51A0501L}, {0x0A28ADE55L, 0x0D1616504L}, {0x05BB7D537L, 0x03A8626E8L},
	{0x087E7603FL, 0x049E2BE35L}, {0x0AC9117E3L, 0x0960DA553L}, {0x0335E62BDL, 0x0897F65E9L}, {0x0B396B823L, 0x0CAAF7461L},
	{0x0B6D1A2BDL, 0x0A0265B31L}, {0x0A297A8F5L, 0x0C55C92F2L}, {0x001603252L, 0x09FD9A12CL}, {0x069B3E86BL, 0x097150027L},
	{0x032192299L, 0x054A8469AL}, {0x0F6FA8764L, 0x040F9DDFEL}, {0x03BDE6053L, 0x0E1814BACL}, {0x06B3C65F0L, 0x0B1E8F849L},
	{0x00CAE0EB1L, 0x046868E40L}, {0x0979AA21FL, 0x008E26438L}, {0x0A389EBA2L, 0x081862188L}, {0x0AA023FD2L, 0x037F83EB9L},
	{0x0228F2727L, 0x094A11343L}, {0x094872B28L, 0x02E2958CEL}, {0x07881B34EL, 0x07ED764A2L}, {0x046165601L, 0x0FB55C166L},
	{0x096BE9DE9L, 0x03F29F593L}, {0x0DFC0E07EL, 0x090AC89A1L}, {0x08AE6D727L, 0x0B776E518L}, {0x03F86DA8DL, 0x01C1F9070L},
	{0x056396108L, 0x017EE2430L}, {0x067570320L, 0x05F8D07D2L}, {0x0F988496DL, 0x04E72C2ECL}, {0x076449B57L, 0x07A16CDD7L},
	{0x064027165L, 0x05C01A02AL}, {0x05B4B7210L, 0x06CBAD25FL}, {0x0A58708F0L, 0x031BBECFCL}, {0x0095EA97DL, 0x0256A477AL},
	{0x0CE17DE1EL, 0x0EE707961L}, {0x08D7C2F6DL, 0x0C5350A29L}, {0x0BEC304D0L, 0x06240546AL}, {0x0D9A504E0L, 0x03C1B1D7BL},
	{0x0758A9815L, 0x0BD2C9EF5L}, {0x00CFA38F6L, 0x07B0C7F50L}, {0x0146C6CFDL, 0x0EF232814L}, {0x0065ABCA0L, 0x0810921C9L},
	{0x07A4A9078L, 0x0E93501C6L}, {0x0E8C58FEDL, 0x07F2132C5L}, {0x0B7421277L, 0x0BA533A1CL}, {0x0805BB1BDL, 0x033449154L},
	{0x0CB56E419L, 0x0628CC1F4L}, {0x000EC3211L, 0x0CF824186L}, {0x0B785154EL, 0x0D1D0A870L}, {0x057990317L, 0x022EC5F4CL},
	{0x06AB09506L, 0x0283FDE70L}, {0x0766133A1L, 0x06C41BD94L}, {0x0F4066561L, 0x056A95312L}, {0x07C35A2CEL, 0x07EC16A70L},
	{0x055668350L, 0x04B2F3848L}, {0x03923707FL, 0x0565C66F0L}, {0x09EE301D2L, 0x018C06C00L}, {0x0DD2D9EC3L, 0x007F3B102L},
	{0x09E6ADFE8L, 0x0BB7CEF6DL}, {0x058321BAAL, 0x09EB56CA8L}, {0x0750DFB80L, 0x02634B15CL}, {0x09B52E714L, 0x0EC8266E1L},
	{0x023BB67CCL, 0x06816E3DFL}, {0x0B59E0211L, 0x0226ABFADL}, {0x0B984229BL, 0x082F463E4L}, {0x0A6E57DB2L, 0x02F5E680DL},
	{0x016584CFEL, 0x072FE349EL}, {0x06F4737E6L, 0x0F36C6F0FL}, {0x03A48B6F3L, 0x03A0253DAL}, {0x00EC450ADL, 0x0AF77B685L},
	{0x045438E8CL, 0x0D932D1AAL}, {0x0755DC8F8L, 0x022AC5CAFL}, {0x0185996A8L, 0x0306DAF0DL}, {0x0B4F080E5L, 0x08CFD425BL},
	{0x0C18A0E57L, 0x07EB3CC03L}, {0x0C9AF9766L, 0x0AD48979BL}, {0x043C7C49AL, 0x09225398CL}, {0x0A66A0D7AL, 0x0A6B03A6EL},
	{0x09B1FDA70L, 0x07F9104B9L}, {0x06A4FC212L, 0x075322ED4L}, {0x0BA724FE9L, 0x032292F69L}, {0x0F530D75DL, 0x02CCF70DEL},
};

#define t1 (table)
#define t2 (table+256)
#define t3 (table+256*2)
#define t4 (table+256*3)

#define sub64(s0, s1, p0, p1) \
      temps0 = (p0); \
      tcarry = s0 < temps0; \
      s0 -= temps0; \
      s1 -= (p1) + tcarry;

#define add64(s0, s1, p0, p1) \
      temps0 = (p0); \
      s0 += temps0; \
      tcarry = s0 < temps0; \
      s1 += (p1) + tcarry;

#define xor64(s0, s1, p0, p1) \
      s0 ^= (p0); \
      s1 ^= (p1);

#define mul5(s0, s1) \
      tempt0 = s0<<2; \
      tempt1 = (s1<<2)|(s0>>30); \
      add64(s0, s1, tempt0, tempt1);

#define mul7(s0, s1) \
      tempt0 = s0<<3; \
      tempt1 = (s1<<3)|(s0>>29); \
      sub64(tempt0, tempt1, s0, s1); \
      s0 = tempt0; \
      s1 = tempt1;

#define mul9(s0, s1) \
      tempt0 = s0<<3; \
      tempt1 = (s1<<3)|(s0>>29); \
      add64(s0, s1, tempt0, tempt1);

#define save_abc \
      aa0 = a0; \
      aa1 = a1; \
      bb0 = b0; \
      bb1 = b1; \
      cc0 = c0; \
      cc1 = c1;

#define round(a0,a1,b0,b1,c0,c1,x0,x1,mul) \
      xor64(c0, c1, x0, x1); \
      temp0  = t1[((c0)>>(0*8))&0xFF][0] ; \
      temp1  = t1[((c0)>>(0*8))&0xFF][1] ; \
      temp0 ^= t2[((c0)>>(2*8))&0xFF][0] ; \
      temp1 ^= t2[((c0)>>(2*8))&0xFF][1] ; \
      temp0 ^= t3[((c1)>>(0*8))&0xFF][0] ; \
      temp1 ^= t3[((c1)>>(0*8))&0xFF][1] ; \
      temp0 ^= t4[((c1)>>(2*8))&0xFF][0] ; \
      temp1 ^= t4[((c1)>>(2*8))&0xFF][1] ; \
      sub64(a0, a1, temp0, temp1); \
      temp0  = t4[((c0)>>(1*8))&0xFF][0] ; \
      temp1  = t4[((c0)>>(1*8))&0xFF][1] ; \
      temp0 ^= t3[((c0)>>(3*8))&0xFF][0] ; \
      temp1 ^= t3[((c0)>>(3*8))&0xFF][1] ; \
      temp0 ^= t2[((c1)>>(1*8))&0xFF][0] ; \
      temp1 ^= t2[((c1)>>(1*8))&0xFF][1] ; \
      temp0 ^= t1[((c1)>>(3*8))&0xFF][0] ; \
      temp1 ^= t1[((c1)>>(3*8))&0xFF][1] ; \
      add64(b0, b1, temp0, temp1); \
      if((mul)==5) \
	{mul5(b0, b1);} \
      else \
	if((mul)==7) \
	  {mul7(b0, b1);} \
	else \
	  {mul9(b0, b1)};

#define pass(a0,a1,b0,b1,c0,c1,mul) \
      round(a0,a1,b0,b1,c0,c1,x00,x01,mul); \
      round(b0,b1,c0,c1,a0,a1,x10,x11,mul); \
      round(c0,c1,a0,a1,b0,b1,x20,x21,mul); \
      round(a0,a1,b0,b1,c0,c1,x30,x31,mul); \
      round(b0,b1,c0,c1,a0,a1,x40,x41,mul); \
      round(c0,c1,a0,a1,b0,b1,x50,x51,mul); \
      round(a0,a1,b0,b1,c0,c1,x60,x61,mul); \
      round(b0,b1,c0,c1,a0,a1,x70,x71,mul);

#define key_schedule \
      sub64(x00, x01, x70^0xA5A5A5A5, x71^0xA5A5A5A5); \
      xor64(x10, x11, x00, x01); \
      add64(x20, x21, x10, x11); \
      sub64(x30, x31, x20^((~x10)<<19), ~x21^(((x11)<<19)|((x10)>>13))); \
      xor64(x40, x41, x30, x31); \
      add64(x50, x51, x40, x41); \
      sub64(x60, x61, ~x50^(((x40)>>23)|((x41)<<9)), x51^((~x41)>>23)); \
      xor64(x70, x71, x60, x61); \
      add64(x00, x01, x70, x71); \
      sub64(x10, x11, x00^((~x70)<<19), ~x01^(((x71)<<19)|((x70)>>13))); \
      xor64(x20, x21, x10, x11); \
      add64(x30, x31, x20, x21); \
      sub64(x40, x41, ~x30^(((x20)>>23)|((x21)<<9)), x31^((~x21)>>23)); \
      xor64(x50, x51, x40, x41); \
      add64(x60, x61, x50, x51); \
      sub64(x70, x71, x60^0x89ABCDEF, x61^0x01234567);

#define feedforward \
      xor64(a0, a1, aa0, aa1); \
      sub64(b0, b1, bb0, bb1); \
      add64(c0, c1, cc0, cc1);

#ifdef UNROLL_COMPRESS
#define compress \
      save_abc \
      pass(a0,a1,b0,b1,c0,c1,5); \
      key_schedule; \
      pass(c0,c1,a0,a1,b0,b1,7); \
      key_schedule; \
      pass(b0,b1,c0,c1,a0,a1,9); \
      for(pass_no=3; pass_no<PASSES; pass_no++) { \
        key_schedule \
	pass(a0,a1,b0,b1,c0,c1,9); \
	tmpa=a0; a0=c0; c0=b0; b0=tmpa; \
	tmpa=a1; a1=c1; c1=b1; b1=tmpa;} \
      feedforward
#else
#define compress \
      save_abc \
      for(pass_no=0; pass_no<PASSES; pass_no++) { \
        if(pass_no != 0) {key_schedule} \
	pass(a0,a1,b0,b1,c0,c1,(pass_no==0?5:pass_no==1?7:9)) \
	tmpa=a0; a0=c0; c0=b0; b0=tmpa; \
	tmpa=a1; a1=c1; c1=b1; b1=tmpa;} \
      feedforward
#endif

#define tiger_compress_macro(str, state) \
{ \
  register dword a0, a1, b0, b1, c0, c1, tmpa; \
  dword aa0, aa1, bb0, bb1, cc0, cc1; \
  dword x00, x01, x10, x11, x20, x21, x30, x31, \
         x40, x41, x50, x51, x60, x61, x70, x71; \
  register dword temp0, temp1, tempt0, tempt1, temps0, tcarry; \
  int pass_no; \
\
  a0 = state[0]; \
  a1 = state[1]; \
  b0 = state[2]; \
  b1 = state[3]; \
  c0 = state[4]; \
  c1 = state[5]; \
\
  x00=str[0*2]; x01=str[0*2+1]; x10=str[1*2]; x11=str[1*2+1]; \
  x20=str[2*2]; x21=str[2*2+1]; x30=str[3*2]; x31=str[3*2+1]; \
  x40=str[4*2]; x41=str[4*2+1]; x50=str[5*2]; x51=str[5*2+1]; \
  x60=str[6*2]; x61=str[6*2+1]; x70=str[7*2]; x71=str[7*2+1]; \
\
  compress; \
\
  state[0] = a0; \
  state[1] = a1; \
  state[2] = b0; \
  state[3] = b1; \
  state[4] = c0; \
  state[5] = c1; \
}

#ifdef UNROLL_COMPRESS
/* The compress function is inlined */
#define tiger_compress(str, state) \
  tiger_compress_macro(((dword*)str), ((dword*)state))
#else
/* The compress function is a function */
void tiger_compress(const dword *str, dword state[6])
{
    tiger_compress_macro(((dword*)str), ((dword*)state));
}
#endif

void tiger(const dword *str, dword length, dword *res)
{
    register dword i, j;
    byte temp[64];

    res[0]=0x89ABCDEF;
    res[1]=0x01234567;
    res[2]=0x76543210;
    res[3]=0xFEDCBA98;
    res[4]=0xC3B2E187;
    res[5]=0xF096A5B4;

    for(i=length; i>=64; i-=64) {
#ifdef WORDS_BIGENDIAN
        for(j=0; j<64; j++)
            temp[j^3] = ((byte*)str)[j];
        tiger_compress(((dword*)temp), res);
#else

		tiger_compress(str, res);
#endif

        str += 16;
    }

#ifdef WORDS_BIGENDIAN
    for(j=0; j<i; j++)
        temp[j^3] = ((byte*)str)[j];

    temp[j^3] = 0x01;
    j++;
    for(; j&7; j++)
        temp[j^3] = 0;
#else

	for(j=0; j<i; j++)
	    temp[j] = ((byte*)str)[j];

	temp[j++] = 0x01;
	for(; j&7; j++)
	    temp[j] = 0;
#endif

    if(j>56) {
        for(; j<64; j++)
            temp[j] = 0;
        tiger_compress(((dword*)temp), res);
        j=0;
    }

    for(; j<56; j++)
        temp[j] = 0;
    ((dword*)(&(temp[56])))[0] = ((dword)length)<<3;
    ((dword*)(&(temp[56])))[1] = 0;
    tiger_compress(((dword*)temp), res);
}

