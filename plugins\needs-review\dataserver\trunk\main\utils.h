/*  Kore Shared Data Server
 *  Copyright (C) 2005  <PERSON><PERSON> <hongli AT navi DOT cx>
 *
 *  This program is free software; you can redistribute it and/or modify
 *  it under the terms of the GNU General Public License as published by
 *  the Free Software Foundation; either version 2 of the License, or
 *  (at your option) any later version.
 *
 *  This program is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU General Public License for more details.
 *
 *  You should have received a copy of the GNU General Public License
 *  along with this program; if not, write to the Free Software
 *  Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA  02111-1307  USA
 */

#ifndef _UTILS_H_
#define _UTILS_H_


unsigned int calc_hash  (const char *str);
unsigned int calc_hash2 (const char *str);


void message (const char *format, ...);
void error (const char *format, ...);
void debug (const char *format, ...);

#define DEBUG debug   /* All uppercase for readability. */


#endif /* _UTILS_H_ */
