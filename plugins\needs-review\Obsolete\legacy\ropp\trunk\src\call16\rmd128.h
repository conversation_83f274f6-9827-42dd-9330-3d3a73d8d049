/* RIPEMD-128
* AUTHOR:   <PERSON><PERSON>, ESAT-COSIC
* DATE:     1 March 1996
* VERSION:  1.0
* Copyright (c) Katholieke Universiteit Leuven
* 1996, All Rights Reserved
*/

//
// Changed to conform to padded packets emulator by <PERSON>
// $Id: rmd128.h 5135 2006-11-18 22:06:15Z mouseland $
// 

#ifndef  rmd128H
#define  rmd128H

#include "../typedefs.h"

void MDinit(dword *MDbuf);
void MDfinish(dword *MDbuf, byte *strptr, dword lswlen, dword mswlen);

#endif
