package Network::Receive::ROla;
use strict;
use base qw(Network::Receive::ServerType0);
use Globals qw($net $messageSender);  # Importar $messageSender

sub new {
    my ($class) = @_;
    my $self = $class->SUPER::new(@_);
    
    my %packets = (
        '0C32' => ['account_server_info', 'v a4 a4 a4 a4 a26 C x17 a*', [qw(len sessionID accountID sessionID2 lastLoginIP lastLoginTime accountSex serverInfo)]],
        '0AE3' => ['received_login_token', 'v x4 Z6 a*', [qw(len unknown server_name token)]],
    );
    
    $self->{packet_list}{$_} = $packets{$_} for keys %packets;

    my %handlers = qw(
        account_server_info 0C32
        received_login_token 0AE3
    );

    $self->{packet_lut}{$_} = $handlers{$_} for keys %handlers;
    
    return $self;
}

sub received_login_token {
    my ($self, $args) = @_;
    
    # Usar $messageSender para acessar o handler de envio
    $messageSender->enableChecksum();
    $messageSender->sendOTPToken($args->{token});
}

1;