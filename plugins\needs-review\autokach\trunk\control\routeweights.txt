#
# Map routing weights
#
# This control file allows you to modify <PERSON><PERSON>'s map routing by specifying
# weights for maps that <PERSON><PERSON> is walking through to reach the destination.
# <PERSON><PERSON> generally tries to use the shortest path to get to the desired
# destination map. The path is counted in number of steps (tiles) the
# character would have to walk.
# Map routing weights can be seen as additional steps that <PERSON><PERSON> thinks
# it would have to do on a specific map. Positive weights makes <PERSON><PERSON>
# avoid a map, negative weights makes it prefer a map.
#
# Example:
# Specifying a weight of 500 for prt_fild08 makes <PERSON><PERSON> avoid walking
# through prt_fild08, as long as there is an alternative path that has
# not more than 500 additional steps.
# Therefore you could say <PERSON><PERSON> prefers to walk up to 500 additional steps
# rather than walking through prt_fild08
#
# Using a weight of 10000 makes it pretty sure to completely avoid a map.
#

# Portal weight. This can be used to make <PERSON><PERSON> avoid/prefer walking through
# map portals. Only change this, if you exactly know what you're doing!
PORTAL 20

# NPC weight. This can be used to avoid/prefer going to the destination
# by using a NPC. Only change this, if you exactly know what you're doing!
NPC 200

# Maps where you can exit to the same place you came from,
# which confuses routing if added to portals thoughtlessly.
bat_room 10000
moc_para01 10000

# Add your map weights here. Format: <mapname> <weight>
