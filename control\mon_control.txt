# This file allows specific AI control for certain monsters
#
# Syntax:
# <monster> <attack> <teleport> <search> <skillcancel> <lv> <joblv> <hp> <sp> <weight>
#
# <monster>:  Name of monster as found in monsters.txt (not case sensitive)
#
# <attack>:
# -1 means to leave the monster alone, even if it attacks you.
#  0 means to leave the monster alone, unless it attacks you.
#  1 means to always auto-attack this monster.
#  2 means always aggressive, auto-attack this monster when it appears, even if sitting.
#  3 means to attack the monster once (provoke) then leave it, useful for mobbing.
#
# <teleport>:
# < 0 (-1, -2, etc.) to set exact critical distance for this monster. Teleport, if the monster reaches it.
# 1 to teleport if the monster is on the screen.
# 2 to teleport if the monster attacks you.
#  -> This is only used in auto-attack mode!
# 3 to disconnect for 30 seconds if the monster is on your screen.
# >= 4 (4, 5, etc.) to set the time that will be disconnected (in seconds) if the monster is on your screen.
#
# <search>: Put a 1 to only attack the monster in the search mode.
# This is only used in auto-attack mode.
#
# <skillcancel>: Set to 1 if you want to interrupt spells casted by this
# monster.
#
# <lv>: Only auto-attack this monster if your level is higher than the
# specified level.
#
# <joblv>: Only auto-attack this monster if your job level is higher than
# the specified level.
#
# <hp>: Only auto-attack this monster if your HP is higher than the
# specified amount. The HP is not specified in percentage.
#
# <sp>: Only auto-attack this monster if your SP is higher than the
# specified amount. The SP is not specified in percentage.
#
# <weight>: Counts this monster as the specified amount aggressives. Supports floating point numbers (eg 1.8237402).
# Example:
#	(config.txt)
#	teleportAuto_minAggressives 6
#	teleportAuto_minAggressivesInLock 6
#
#	(mon_control.txt)
#	Hydra 1 0 0 0 0 0 0 0 0.2
#	Merman 1 0 0 0 0 0 0 0 2
#
#	If there's five hydras and two sword fish attacks the bot, it won't
#	teleport away since the aggressives are counted as 5*0.2 + 2*1 = 3
#	However, two marcs and two merman will make it tele away because
#	it sees 2*1* + 2*2 = 6 aggressives.
#
#
# Monsters not found in this file, or flags not specified will default to:
# <attack> = 1


##### Eggs #####
Ant's Egg 0 0 0
PecoPeco's Egg 0 0 0
Pupa 0 0 0
Thief Bug's Egg 0 0 0

##### Alchemist Summons ####
# Summoned Parasite
1555 0 0 0 

# Summoned Flora
1575 0 0 0 

# Summoned Hydra
1579 0 0 0 

# Summoned Mandragora
1589 0 0 0 

# Summoned Geographer
1590 0 0 0 

##### Plants #####
Black Mushroom 0 0 0
Blue Plant 0 0 0
Green Plant 0 0 0
Red Mushroom 0 0 0
Red Plant 0 0 0
Shining Plant 0 0 0
White Plant 0 0 0
Yellow Plant 0 0 0


##### Homunculus #####
6001 -1 0 0
6002 -1 0 0
6003 -1 0 0
6004 -1 0 0
6005 -1 0 0
6006 -1 0 0
6007 -1 0 0
6008 -1 0 0
6009 -1 0 0
6010 -1 0 0
6011 -1 0 0
6012 -1 0 0
6013 -1 0 0
6014 -1 0 0
6015 -1 0 0
6016 -1 0 0

##### Mercenary #####
6017 -1 0 0
6018 -1 0 0
6019 -1 0 0
6020 -1 0 0
6021 -1 0 0
6022 -1 0 0
6023 -1 0 0
6024 -1 0 0
6025 -1 0 0
6026 -1 0 0
6027 -1 0 0
6028 -1 0 0
6029 -1 0 0
6030 -1 0 0
6031 -1 0 0
6032 -1 0 0
6033 -1 0 0
6034 -1 0 0
6035 -1 0 0
6036 -1 0 0
6037 -1 0 0
6038 -1 0 0
6039 -1 0 0
6040 -1 0 0
6041 -1 0 0
6042 -1 0 0
6043 -1 0 0
6044 -1 0 0
6045 -1 0 0
6046 -1 0 0


##### MVPs and Dangerous Monsters #####
##MOB ID : MVP
1511 -1 1 0 #Amon Ra
2476 -1 1 0 #Amdarias
1388 -1 1 0 #Archangeling
1647 -1 1 0 #Assassin Cross Eremes
1785 -1 1 0 #Atroce
1039 -1 1 0 #Baphomet
1630 -1 1 0 #Bacsojin
1874 -1 1 0 #Beelzebub
2068 -1 1 0 #Boitata
2319 -1 1 0 #Buwaya
2238 -1 1 0 #Champion Chen
2240 -1 1 0 #Clown Alphoccio
2236 -1 1 0 #Creator Flamel
2253 -1 1 0 #Daehyon
1302 -1 1 0 #Dark Illusion
1272 -1 1 0 #Dark Lord
1719 -1 1 0 #Detale
1046 -1 1 0 #Doppelganger
1389 -1 1 0 #Dracula
1112 -1 1 0 #Drake
1115 -1 1 0 #Eddga
1658 -1 1 0 #Egnigem Cenia
1418 -1 1 0 #Evil Snake Lord
1871 -1 1 0 #Fallen Bishop
1252 -1 1 0 #Garm
2251 -1 1 0 #Gioia
1768 -1 1 0 #Gloom Under Night
2165 -1 1 0 #Gold Queen Scaraba
1086 -1 1 0 #Golden Thief Bug
1885 -1 1 0 #Gopinich
2241 -1 1 0 #Gypsy Trentini
1649 -1 1 0 #High Priest Magaleta
1651 -1 1 0 #High Wizard Katrinn
1832 -1 1 0 #Ifrit
1492 -1 1 0 #Incantation Samurai
2255 -1 1 0 #Kades
1734 -1 1 0 #Kiel D-01
2202 -1 1 0 #Kraken
1779 -1 1 0 #Ktullanux
1980 -1 1 0 #Kubkin
1688 -1 1 0 #Lady Tany
2156 -1 1 0 #Leak
1646 -1 1 0 #Lord Knight Seyren
1373 -1 1 0 #Lord of Death
2131 -1 1 0 #Lost Dragon 
1147 -1 1 0 #Maya
1289 -1 1 0 #Maya Purple
1059 -1 1 0 #Mistress
1150 -1 1 0 #Moonlight
2022 -1 1 0 #Nidhoggr's Shadow
2362 -1 1 0 #Nightmare Amon Ra
1262 -1 1 0 #Mutant Dragon
1087 -1 1 0 #Orc Hero
1190 -1 1 0 #Orc Lord
1038 -1 1 0 #Osiris
2235 -1 1 0 #Paladin Randel
1157 -1 1 0 #Pharaoh
1159 -1 1 0 #Phreeoni
1502 -1 1 0 #Pori Pori
2237 -1 1 0 #Professor Celia
2249 -1 1 0 #Pyuriel
2087 -1 1 0 #Queen Scaraba
2475 -1 1 0 #Root of Corruption
1623 -1 1 0 #RSX-0806
2341 -1 1 0 #RWC Boss
1650 -1 1 0 #Sniper Shecil
2239 -1 1 0 #Stalker Gertie
1251 -1 1 0 #Stormy Knight
1583 -1 1 0 #Tao Gunka
1708 -1 1 0 #Thanatos
1312 -1 1 0 #Turtle General
1751 -1 1 0 #Valkyrie Randgris
1685 -1 1 0 #Vesper
1648 -1 1 0 #Whitesmith Harword
1917 -1 1 0 #Wounded Morroc
1658 -1 1 0 #Ygnizem
