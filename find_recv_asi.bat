@echo off
echo ========================================
echo  PROCURANDO recv.asi NO SISTEMA
echo ========================================
echo.

echo 🔍 Procurando recv.asi em todo o sistema...
echo.

REM Procurar recv.asi em todo o disco C:
echo 📂 Procurando em C:\...
dir C:\recv.asi /s /b 2>nul

echo.
echo 📂 Procurando arquivos .asi em C:\...
dir C:\*.asi /s /b 2>nul

echo.
echo 📂 Procurando em Downloads...
dir "%USERPROFILE%\Downloads\recv.asi" /b 2>nul
dir "%USERPROFILE%\Downloads\*.asi" /b 2>nul

echo.
echo 📂 Procurando em Desktop...
dir "%USERPROFILE%\Desktop\recv.asi" /b 2>nul
dir "%USERPROFILE%\Desktop\*.asi" /b 2>nul

echo.
echo 📂 Procurando em pasta atual...
dir "recv.asi" /b 2>nul
dir "*.asi" /b 2>nul

echo.
echo ========================================
echo  BUSCA CONCLUÍDA
echo ========================================
echo.
echo Se recv.asi foi encontrado acima, anote o caminho completo
echo Se não foi encontrado, você precisa:
echo 1. Pedir recv.asi já compilado, OU
echo 2. Compilar usando Visual Studio
echo.
pause
