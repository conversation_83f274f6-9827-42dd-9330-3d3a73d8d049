# IDA Pro Beginner's Guide: GNJOY LATAM Checksum Reverse Engineering

## 🎯 Goal
Find the exact checksum algorithm used for packets 0x0085 (movement) and 0x0437 (actions) in Ragexe.exe (30/05/2025).

## 📋 Prerequisites
- ✅ IDA Pro installed and Ragexe.exe loaded
- ✅ Auto-analysis completed (wait for "AU: idle" in status bar)
- ✅ This guide open for reference

## 🖥️ Part 1: Setting Up IDA Pro Interface

### Step 1: Arrange Your Windows
```
┌─────────────────────────────────────────────────────────────────┐
│ IDA Pro - Optimal Layout for Reverse Engineering               │
├─────────────┬─────────────────────────────┬─────────────────────┤
│ Functions   │     Main Disassembly        │     Strings         │
│ Window      │        Window               │     Window          │
│             │                             │                     │
│ sub_401000  │ 00401000  mov eax, ebx      │ "send packet"       │
│ sub_401050  │ 00401003  add eax, 5        │ "checksum"          │
│ sub_401100  │ 00401006  call sub_401050   │ "invalid"           │
│ ...         │ ...                         │ ...                 │
├─────────────┴─────────────────────────────┴─────────────────────┤
│                    Hex Dump Window                              │
│ 00401000: 8B C3 83 C0 05 E8 45 00 00 00                       │
└─────────────────────────────────────────────────────────────────┘
```

1. **Main Disassembly Window** (should be open by default)
   - Shows: Address | Hex Bytes | Assembly Instructions
   - Example: `00401000  8BC3  mov eax, ebx`

2. **Open Functions Window**:
   - Menu: `View` → `Open subviews` → `Functions`
   - Drag to left side and dock
   - Shows all detected functions as a list

3. **Open Strings Window**:
   - Menu: `View` → `Open subviews` → `Strings`
   - Drag to right side and dock
   - Shows all text strings found in executable

4. **Open Hex View**:
   - Menu: `View` → `Open subviews` → `Hex dump`
   - Drag to bottom and dock
   - Shows raw binary data in hexadecimal

### Step 2: Configure Search Options
1. **Enable case-insensitive search**:
   - Menu: `Options` → `General`
   - Check "Case insensitive identifiers"
   - Click `OK`

## 🔍 Part 2: Finding Network Functions

### Step 3: Search for Network API Functions
1. **Open Names Window**:
   - Press `Shift + F4` (or Menu: `View` → `Open subviews` → `Names`)
   - This shows all imported functions

2. **Search for network functions**:
   - In Names window, press `Ctrl + F`
   - Search for: `send`
   - Look for these functions:
     - `send`
     - `sendto` 
     - `WSASend`
     - `WSASendTo`

3. **Navigate to network function**:
   - Double-click on `send` or `WSASend` in the Names window
   - This takes you to the import stub

### Step 4: Find References to Network Functions
1. **Find cross-references**:
   - With cursor on `send` function, press `Ctrl + X`
   - This opens "Cross-references to send" window
   - You'll see a list of all places that call this function

2. **Examine each reference**:
   - Double-click each reference to go to that location
   - Look for functions that seem to send game packets

## 🎯 Part 3: Identifying Packet Transmission Functions

### Step 5: Search for Packet IDs
1. **Search for movement packet (0x0085)**:
   - Press `Alt + B` (Binary search)
   - Enter: `85 00` (little-endian format)
   - Click `OK`
   - Press `F3` to find next occurrence

2. **Search for action packet (0x0437)**:
   - Press `Alt + B` again
   - Enter: `37 04` (little-endian format)
   - Click `OK`
   - Press `F3` to find next occurrence

### Step 6: Analyze Packet Construction
When you find a packet ID reference:

1. **Look for packet construction pattern**:
```assembly
; Example pattern to look for:
mov     word ptr [esp+4], 85h     ; Packet ID 0x0085
mov     byte ptr [esp+6], al      ; First coordinate byte
mov     byte ptr [esp+7], bl      ; Second coordinate byte
mov     byte ptr [esp+8], cl      ; Third coordinate byte
```

2. **Find the function containing this code**:
   - Press `Ctrl + P` to go to function start
   - This is likely your packet sending function

## 🔧 Part 4: Locating Checksum Calculation

### Step 7: Trace Backwards from Packet Send
1. **From packet construction, look backwards**:
   - Scroll up in the disassembly
   - Look for loops or mathematical operations before the send call

2. **Common checksum patterns to identify**:

**Pattern A: Simple Loop (Most Common)**
```assembly
; What it looks like in IDA Pro:
********  33 C0           xor     eax, eax          ; Clear checksum
********  8B 4D 08        mov     ecx, [ebp+8]      ; Load packet length
********  8B 75 0C        mov     esi, [ebp+0Ch]    ; Load packet data ptr
********                  loc_401028:               ; Loop start label
********  02 04 0E        add     al, [esi+ecx]     ; Add byte to checksum
0040102B  E2 FB           loop    loc_401028        ; Decrement ecx, jump if not zero
```

**Pattern B: Account ID Usage**
```assembly
; What it looks like in IDA Pro:
********  A1 00 50 40 00  mov     eax, ds:account_id ; Load account ID (global var)
********  03 05 04 50 40  add     eax, ds:tick_count ; Add tick count
0040103B  33 C3           xor     eax, ebx          ; XOR with checksum
```

**Pattern C: Multiplicative Operations (Advanced)**
```assembly
; What it looks like in IDA Pro:
********  69 C0 FD 43 03  imul    eax, 343FDh       ; Multiply by magic constant
********  00
********  03 C3           add     eax, ebx          ; Add current byte
********  25 FF FF FF FF  and     eax, 0FFFFFFFFh   ; Keep only 32 bits
```

**Visual Pattern Recognition:**
```
Look for these instruction sequences:
┌─────────────────────────────────────────┐
│ CHECKSUM CALCULATION INDICATORS:        │
├─────────────────────────────────────────┤
│ ✅ xor eax, eax     (clear register)    │
│ ✅ mov ecx, [...]   (loop counter)      │
│ ✅ add al, [...]    (accumulate bytes)  │
│ ✅ loop loc_xxxxx   (repeat loop)       │
│ ✅ imul eax, xxxxx  (multiply by const) │
│ ✅ mov eax, ds:xxx  (load global var)   │
└─────────────────────────────────────────┘
; What it looks like in IDA Pro:
********  69 C0 FD 43 03  imul    eax, 343FDh       ; Multiply by magic constant
********  00
********  03 C3           add     eax, ebx          ; Add current byte
********  25 FF FF FF FF  and     eax, 0FFFFFFFFh   ; Keep only 32 bits
```

**Visual Pattern Recognition:**
```
Look for these instruction sequences:
┌─────────────────────────────────────────┐
│ CHECKSUM CALCULATION INDICATORS:        │
├─────────────────────────────────────────┤
│ ✅ xor eax, eax     (clear register)    │
│ ✅ mov ecx, [...]   (loop counter)      │
│ ✅ add al, [...]    (accumulate bytes)  │
│ ✅ loop loc_xxxxx   (repeat loop)       │
│ ✅ imul eax, xxxxx  (multiply by const) │
│ ✅ mov eax, ds:xxx  (load global var)   │
└─────────────────────────────────────────┘
```

### Step 8: Identify Checksum Variables
1. **Look for these variable types**:
   - Account ID (usually 4 bytes, loaded from memory)
   - Tick count (often from GetTickCount API call)
   - Packet data (bytes being processed in loop)
   - Constants (immediate values like 0x343FD)

2. **Track data flow**:
   - Right-click on a register → `Track register`
   - This highlights where the register is used

## 📖 Part 5: Reading Assembly Code

### Step 9: Understanding Common Instructions

**Data Movement:**
```assembly
mov eax, ebx    ; Copy ebx to eax
lea eax, [ebx+4]; Load address (ebx+4) into eax
```

**Arithmetic:**
```assembly
add eax, ebx    ; eax = eax + ebx
sub eax, ebx    ; eax = eax - ebx
imul eax, 5     ; eax = eax * 5
xor eax, ebx    ; eax = eax XOR ebx
```

**Loops:**
```assembly
mov ecx, 10     ; Counter = 10
loop_start:
  ; ... loop body ...
loop loop_start ; Decrement ecx, jump if not zero
```

### Step 10: Converting Assembly to Algorithm
1. **Document each operation**:
   - Write down what each instruction does
   - Track which variables are used

2. **Example conversion**:
```assembly
; Assembly code:
xor     eax, eax          ; checksum = 0
mov     ecx, packet_len   ; i = packet_length
mov     esi, packet_data  ; ptr = packet_data
loop_start:
add     al, [esi+ecx-1]   ; checksum += packet_data[i-1]
loop    loop_start        ; i--, continue if i > 0
add     eax, [account_id] ; checksum += account_id
```

```c
// Equivalent C code:
uint32_t checksum = 0;
for (int i = packet_length; i > 0; i--) {
    checksum += packet_data[i-1];
}
checksum += account_id;
```

## 🚨 Part 6: Common Beginner Mistakes & Troubleshooting

### Mistake 1: Wrong Packet ID Format
❌ **Wrong**: Searching for `0085` or `8500`
✅ **Correct**: Search for `85 00` (little-endian)

### Mistake 2: Missing Function Context
❌ **Wrong**: Looking at isolated instructions
✅ **Correct**: Always press `Ctrl + P` to see full function

### Mistake 3: Ignoring Data Types
❌ **Wrong**: Assuming all operations are 32-bit
✅ **Correct**: Check instruction suffixes:
- `mov al, bl` = 8-bit operation
- `mov ax, bx` = 16-bit operation  
- `mov eax, ebx` = 32-bit operation

### Mistake 4: Not Following Cross-References
❌ **Wrong**: Giving up when you can't find something
✅ **Correct**: Use `Ctrl + X` to find all references

## 🔍 Part 7: Advanced Search Techniques

### Step 11: Alternative Search Methods
1. **Search for strings**:
   - Look for debug strings like "send packet", "checksum", etc.
   - In Strings window, press `Ctrl + F`

2. **Search for constants**:
   - Press `Alt + I` (Immediate value search)
   - Search for common RO constants like `0x343FD`

3. **Search for API calls**:
   - Look for `GetTickCount` calls (often used in checksums)
   - Search for `timeGetTime` as alternative

### Step 12: Using IDA's Analysis Features
1. **Create function if needed**:
   - If IDA didn't detect a function, press `P` at function start
   - This helps with analysis

2. **Add comments**:
   - Press `;` to add comments to lines
   - Document what you think each part does

3. **Rename variables**:
   - Press `N` to rename variables/functions
   - Use descriptive names like "checksum", "account_id"

## 📝 Part 8: Documentation Template

### Step 13: Document Your Findings
Create a text file with this template:

```
GNJOY LATAM Checksum Algorithm Analysis
=====================================

Packet ID: 0x0085 (Movement)
Function Address: 0x[ADDRESS]
Assembly Code:
[PASTE ASSEMBLY HERE]

Algorithm Steps:
1. Initialize checksum = 0
2. [DOCUMENT EACH STEP]
3. Return checksum

Variables Used:
- Account ID: [HOW IT'S USED]
- Tick Count: [HOW IT'S USED]  
- Packet Data: [HOW IT'S PROCESSED]
- Constants: [LIST ANY CONSTANTS]

C Code Equivalent:
[YOUR CONVERTED ALGORITHM]
```

## 🎯 Part 9: Next Steps After Finding Algorithm

### Step 14: Validate Your Findings
1. **Check both packet types**:
   - Verify 0x0085 and 0x0437 use same algorithm
   - Document any differences

2. **Test with sample data**:
   - Use the provided `test_checksum.pl` script
   - Implement your algorithm and test

3. **Implement in OpenKore**:
   - Replace placeholder in `ROla.pm`
   - Test with actual server

## 🆘 Getting Help

### If You Get Stuck:
1. **Take screenshots** of what you're seeing
2. **Document the exact steps** you've taken
3. **Note any error messages** or unexpected behavior
4. **Share the function address** where you think the checksum is

### Common Success Indicators:
- ✅ Found function that constructs packets with 0x0085/0x0437
- ✅ Found loop that processes packet data
- ✅ Found usage of account ID and/or tick count
- ✅ Found mathematical operations (add, xor, multiply)

Remember: Reverse engineering takes patience! Don't get discouraged if it takes several attempts to find the right function.
