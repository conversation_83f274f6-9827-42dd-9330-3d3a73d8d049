Revision 1.10
+new DB format
+300 new monsters
+ID matching instead of name matching
+event-driven monsterEquip
+Element Level support

v0.07
+fully new-packet-parser compliant
+Case-insensitve monstername matching
*changed $monster->{dmgTo} to $monster->{deltaHp}
 now supports healing


v0.06
+support for changing element

v0.05
fixed Bug that caused empty hashes to be created. thx to Joseph

v0.04
+Stone Curse Support

v0.03
+Frost Diver support
+extended skilluse messages

v0.02
some Bugfixes