# IDA Pro Practical Walkthrough: Finding GNJOY LATAM Checksum

## 🎯 Step-by-Step Practical Guide

This is a hands-on walkthrough assuming you have Ragexe.exe loaded in IDA Pro and auto-analysis is complete.

## 📋 Phase 1: Quick Setup (5 minutes)

### Step 1: Verify IDA Pro is Ready
```
Status Bar Should Show: "AU: idle" (auto-analysis complete)
If not: Wait for analysis to finish before proceeding
```

### Step 2: Open Essential Windows
1. **Press Shift+F4** → Opens Names window (imported functions)
2. **Press Alt+T** → Opens Strings window  
3. **Press Ctrl+S** → Opens Segments window
4. **Arrange windows** side by side for easy viewing

## 🔍 Phase 2: Finding Network Functions (10 minutes)

### Step 3: Locate Network API Calls
1. **In Names window, press Ctrl+F**
2. **Search for: `send`**
3. **Look for these functions:**
   ```
   ✅ send
   ✅ sendto  
   ✅ WSASend
   ✅ WSASendTo
   ```

### Step 4: Find Function References
1. **Double-click on `send` in Names window**
2. **Press Ctrl+X** (cross-references)
3. **You'll see something like:**
   ```
   Cross-references to send:
   sub_401234+15    call    send
   sub_405678+23    call    send
   sub_409ABC+31    call    send
   ```

### Step 5: Examine Each Reference
1. **Double-click first reference** (e.g., `sub_401234+15`)
2. **Press Ctrl+P** to go to function start
3. **Look for packet construction before the send call**

## 🎯 Phase 3: Finding Packet Construction (15 minutes)

### Step 6: Search for Packet IDs
1. **Press Alt+B** (binary search)
2. **Enter: `85 00`** (movement packet in little-endian)
3. **Press F3** to find next occurrence
4. **Repeat for: `37 04`** (action packet)

### Step 7: Identify Packet Building Code
**Look for patterns like this:**
```assembly
; Example of what you're looking for:
00401000  66 C7 44 24 04 85 00    mov word ptr [esp+4], 85h    ; Packet ID
00401007  88 44 24 06             mov [esp+6], al              ; Coordinate 1
0040100B  88 5C 24 07             mov [esp+7], bl              ; Coordinate 2  
0040100F  88 4C 24 08             mov [esp+8], cl              ; Coordinate 3
```

### Step 8: Find the Function Containing This Code
1. **When you find packet ID (85 00 or 37 04)**
2. **Press Ctrl+P** to go to function start
3. **This is your packet sending function!**

## 🔧 Phase 4: Locating Checksum Code (20 minutes)

### Step 9: Trace Backwards from Packet Send
1. **From packet construction, scroll UP**
2. **Look for mathematical operations before send call**
3. **Common patterns to spot:**

**Pattern A: Simple Checksum Loop**
```assembly
; What to look for:
33 C0                xor     eax, eax          ; Clear checksum
8B 4D 08             mov     ecx, [ebp+8]      ; Loop counter
8B 75 0C             mov     esi, [ebp+0Ch]    ; Data pointer
                     loc_loop:
02 04 0E             add     al, [esi+ecx]     ; Add byte
E2 FB                loop    loc_loop          ; Repeat
```

**Pattern B: Account ID + Tick Usage**
```assembly
; What to look for:
A1 XX XX XX XX       mov     eax, ds:account_id ; Load account ID
03 05 XX XX XX XX    add     eax, ds:tick       ; Add tick count
33 C3                xor     eax, ebx           ; XOR with checksum
```

### Step 10: Identify Key Variables
**Look for these memory references:**
- **Account ID**: Usually a global variable (ds:XXXXXXXX)
- **Tick Count**: Often from GetTickCount() API call
- **Packet Data**: Local variables or function parameters

## 📖 Phase 5: Reading the Assembly (15 minutes)

### Step 11: Understanding the Instructions
**Common instruction meanings:**
```assembly
mov eax, ebx     ; Copy ebx to eax
add eax, ebx     ; eax = eax + ebx  
xor eax, ebx     ; eax = eax XOR ebx
imul eax, 5      ; eax = eax * 5
and eax, 0FFFFh  ; Keep only lower 16 bits
loop label       ; Decrement ecx, jump if not zero
```

### Step 12: Document What You Find
**Create a text file with:**
```
Function Address: 0x[ADDRESS]
Checksum starts at: 0x[ADDRESS]
Variables used:
- Account ID: [HOW LOADED]
- Tick: [HOW LOADED]  
- Packet data: [HOW PROCESSED]
Algorithm steps:
1. [STEP 1]
2. [STEP 2]
...
```

## 🧪 Phase 6: Converting to Code (10 minutes)

### Step 13: Assembly to C Translation
**Example conversion:**
```assembly
; Assembly found:
xor     eax, eax          ; checksum = 0
mov     ecx, 3            ; i = 3 (packet length)
mov     esi, packet_data  ; ptr = packet_data
loop_start:
add     al, [esi+ecx-1]   ; checksum += packet_data[i-1]  
loop    loop_start        ; i--, continue if i > 0
add     eax, [account_id] ; checksum += account_id
```

**Becomes this C code:**
```c
uint32_t checksum = 0;
for (int i = 3; i > 0; i--) {
    checksum += packet_data[i-1];
}
checksum += account_id;
```

### Step 14: Convert to Perl for OpenKore
```perl
sub calculateChecksum {
    my ($self, $packet_data, $packet_id) = @_;
    
    my $checksum = 0;
    my $account_id = unpack("V", $accountID) if $accountID;
    
    # Your extracted algorithm here:
    for my $i (reverse 0..length($packet_data)-1) {
        $checksum += ord(substr($packet_data, $i, 1));
    }
    $checksum += $account_id;
    $checksum &= 0xFFFFFFFF;
    
    return pack("V", $checksum);
}
```

## 🚨 Common Beginner Mistakes

### Mistake 1: Wrong Search Format
❌ **Wrong**: Searching for `0085` 
✅ **Correct**: Search for `85 00` (little-endian)

### Mistake 2: Missing Function Context  
❌ **Wrong**: Looking at random instructions
✅ **Correct**: Always press Ctrl+P to see full function

### Mistake 3: Ignoring Cross-References
❌ **Wrong**: Giving up when you can't find something
✅ **Correct**: Use Ctrl+X to find all uses

### Mistake 4: Not Following Data Flow
❌ **Wrong**: Assuming what variables contain
✅ **Correct**: Trace where data comes from

## 🎯 Success Indicators

**You're on the right track when you find:**
- ✅ Function that builds packets with 0x0085 or 0x0437
- ✅ Loop that processes packet data byte by byte
- ✅ Usage of account ID (global variable)
- ✅ Usage of tick count (from GetTickCount)
- ✅ Mathematical operations (add, xor, multiply)

## 🆘 If You Get Stuck

### Quick Troubleshooting:
1. **Can't find packet IDs?**
   - Try searching in Hex view instead
   - Look for string references to "packet" or "send"

2. **Can't find checksum code?**
   - Look for GetTickCount API calls
   - Search for mathematical constants (343FD, etc.)

3. **Assembly looks confusing?**
   - Focus on the pattern, not individual instructions
   - Look for loops and repeated operations

### Alternative Approaches:
1. **Search for strings**: "checksum", "hash", "crc"
2. **Search for constants**: 0x343FD, 0x41C64E6D
3. **Look for GetTickCount calls**: Often used in checksums

## 🎉 Next Steps After Success

1. **Test your algorithm** with the provided test script
2. **Implement in ROla.pm** 
3. **Test with GNJOY LATAM server**
4. **Verify no disconnections on movement/actions**

Remember: Take your time and be methodical. Reverse engineering requires patience, but the framework is already built - you just need to find that one algorithm!
