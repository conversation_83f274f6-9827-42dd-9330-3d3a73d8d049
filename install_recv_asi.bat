@echo off
echo ========================================
echo  INSTALADOR recv.asi - GNJOY LATAM
echo ========================================
echo.

REM Verificar se recv.asi existe na pasta atual
if not exist "recv.asi" (
    echo ❌ ERRO: recv.asi não encontrado na pasta atual!
    echo.
    echo 📁 Coloque recv.asi na pasta do OpenKore primeiro
    echo    Exemplo: C:\openkore2\openkore\recv.asi
    echo.
    pause
    exit /b 1
)

echo ✅ recv.asi encontrado na pasta atual
echo.

REM Solicitar caminho do cliente Ragnarok
echo 📂 Digite o caminho COMPLETO da pasta do cliente Ragnarok:
echo    Exemplo: C:\Ragnarok
echo    (pasta onde está o Ragexe.exe)
echo.
set /p RAGNAROK_PATH="Caminho: "

REM Verificar se o caminho existe
if not exist "%RAGNAROK_PATH%" (
    echo ❌ ERRO: Pasta não encontrada: %RAGNAROK_PATH%
    echo.
    pause
    exit /b 1
)

REM Verificar se Ragexe.exe existe
if not exist "%RAGNAROK_PATH%\Ragexe.exe" (
    echo ❌ ERRO: Ragexe.exe não encontrado em: %RAGNAROK_PATH%
    echo.
    echo Verifique se o caminho está correto
    pause
    exit /b 1
)

echo ✅ Ragexe.exe encontrado em: %RAGNAROK_PATH%
echo.

REM Copiar recv.asi
echo 📋 Copiando recv.asi para pasta do cliente...
copy "recv.asi" "%RAGNAROK_PATH%\recv.asi" >nul

if errorlevel 1 (
    echo ❌ ERRO: Falha ao copiar recv.asi
    echo.
    echo Possíveis causas:
    echo - Permissões insuficientes (execute como administrador)
    echo - Pasta protegida por antivírus
    echo - Espaço insuficiente em disco
    echo.
    pause
    exit /b 1
)

echo ✅ recv.asi copiado com sucesso!
echo.

REM Verificar se arquivo foi copiado
if exist "%RAGNAROK_PATH%\recv.asi" (
    echo ✅ Verificação: recv.asi está na pasta do cliente
) else (
    echo ❌ ERRO: recv.asi não foi copiado corretamente
    pause
    exit /b 1
)

echo.
echo ========================================
echo  INSTALAÇÃO CONCLUÍDA COM SUCESSO! 🎉
echo ========================================
echo.
echo 📁 Arquivos instalados:
echo    %RAGNAROK_PATH%\Ragexe.exe
echo    %RAGNAROK_PATH%\recv.asi ← NOVO
echo.
echo 🚀 PRÓXIMOS PASSOS:
echo.
echo 1. ✅ recv.asi já está instalado
echo 2. ✅ config.txt já está configurado para XKore 1
echo 3. 🎯 Execute: perl openkore.pl
echo 4. 🎮 OpenKore vai iniciar o cliente automaticamente
echo 5. 🔧 recv.asi vai resolver o checksum automaticamente
echo.
echo ⚠️  IMPORTANTE:
echo - Execute OpenKore como administrador se necessário
echo - Adicione exceção no antivírus para recv.asi
echo - Feche qualquer instância do Ragexe.exe antes de testar
echo.
echo ========================================
pause
