# Teste XKore 0 com Injeção DLL - GNJOY LATAM
# Cliente oficial faz login, OpenKore injeta packets com checksum

######## XKore 0 Settings ########
XKore 0
XKore_dll NetRedirect.dll
XKore_injectDLL 1
XKore_autoAttachIfOneExe 1
XKore_silent 0
XKore_bypassBotDetection 1
XKore_exeName ragexe.exe

######## Server Settings ########
master Latam - ROla: Freya/Nidhogg/Yggdrasil
server 1

# Não precisa de credenciais - cliente oficial faz login
# username 
# password 
# loginPinCode 
# char 

######## Debug Settings ########
debug 2
verboseLog 1
debugPacket_sent 2
debugPacket_received 1
debugPacket_include_dumpMethod 2

# Focar nos packets de movimento e ação
debugPacket_include 0085,0437,0360

# Log to files
logToFile 1
logToFile_Packet 1
logToFile_Debug 1

######## AI Settings ########
ai_manual 1
attackAuto 0
route_randomWalk 0
moveStepDelay 2000
attackDelay 2000

# Timeouts
timeout 30
timeout_ex 60

######## INSTRUÇÕES DE TESTE ########
# 1. Feche qualquer instância do Ragexe.exe
# 2. Inicie OpenKore com esta configuração
# 3. OpenKore vai iniciar o cliente automaticamente
# 4. Faça login MANUALMENTE no cliente oficial
# 5. Selecione personagem e entre no jogo
# 6. Teste comandos no console do OpenKore:
#    - move 100 150
#    - move 120 130
#    - sit
#    - stand

######## LOGS ESPERADOS ########
# ✅ "Attached to Ragnarok Online client"
# ✅ "You are now in the game"
# ✅ [ROla] REAL XOR: seed_1=0x1F... (ao usar move)
# ✅ [ROla] REAL XOR: seed_2=0x2C... (segundo move)
# ✅ Movimento funcionando sem desconexão
# ✅ Ações funcionando sem desconexão

######## VANTAGENS DESTA ABORDAGEM ########
# ✅ Cliente oficial resolve autenticação OTP
# ✅ Algoritmo de checksum já implementado
# ✅ Teste rápido para validar checksum
# ✅ Menos problemas de compatibilidade
# ✅ Debugging focado apenas no checksum

######## SE XKORE 0 FUNCIONAR ########
# ✅ Confirma que algoritmo de checksum está correto
# ✅ Problema é apenas na autenticação XKore 2
# ✅ Pode continuar usando XKore 0 ou
# ✅ Pode focar em resolver autenticação XKore 2

######## SE XKORE 0 NÃO FUNCIONAR ########
# ❌ Problema pode ser no algoritmo de checksum
# ❌ Precisa revisar implementação do checksum
# ❌ Pode precisar de mais engenharia reversa

######## PRÓXIMOS PASSOS ########
# 1. Teste XKore 0 primeiro (mais rápido)
# 2. Se funcionar: algoritmo OK, problema é autenticação
# 3. Se não funcionar: revisar algoritmo de checksum
# 4. Baseado no resultado, decidir estratégia final
