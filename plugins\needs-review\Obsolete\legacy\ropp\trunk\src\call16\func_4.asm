; OpenKore - Padded Packet Emulator.
;
; This program is free software; you can redistribute it and/or
; modify it under the terms of the GNU General Public License
; as published by the Free Software Foundation; either version 2
; of the License, or (at your option) any later version.
;
; This program is distributed in the hope that it will be useful,
; but WITHOUT ANY WARRANTY; without even the implied warranty of
; MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.
; See http://www.gnu.org/licenses/gpl.html for the full license.

section .data use32 CLASS=data
  d06A253Ch dd 0E1A833ADh
  d06A2540h dd 0FC74C963h, 097F47AE2h, 0DA204122h, 0BF32463Ch, 05E22924Ah, 0D10B3E43h, 020E43242h, 054D7B96Fh, 097FCAAB3h, 0D23D4038h, 04DB37205h, 0D1976B25h, 0A8C112B0h, 0BA7BA2CFh, 031AE125Ch
            dd 015527C7Eh, 08FB1D950h, 079C452EAh, 00C94F136h, 03039DD6Bh, 03EBDF084h, 0F0396399h, 09EA53EA3h, 0222B8CEBh, 0B4D4554Ah, 05EA8C2DAh, 008C1DCB3h, 0FC389BFEh, 012F61A94h, 0833371BFh, 059F9B856h
            dd 08D41FA94h, 046332E71h, 080E98E27h, 0614CF48Dh, 0F574A7CEh, 0429B91F2h, 0639ADC22h, 051AA8F57h, 034B3A49Bh, 025FF4405h, 01E6788B0h, 0181369A4h, 05B1D00FBh, 0C07E56BCh, 0914F84E2h, 0B698A295h
            dd 04873ABFEh, 04208B7E6h, 0EA32EEA7h, 01B282B29h, 0FEF3A675h, 09AAE77B4h, 00B3089FFh, 058D30330h, 09A8F0F9Fh, 0CB5F7715h, 0035982DBh, 06B994AEAh, 0FD26A84Ch, 0C22BD508h, 0D28EEB59h, 0467BC128h
            dd 038E9A18Ch, 091028490h, 078CE826Bh, 00958A6F8h, 04AB7F860h, 037F491BAh, 0F62A8A01h, 09250DB5Ch, 024909FC6h, 0A4F2ED68h, 04B90D039h, 0F3635F54h, 0D48495C0h, 0F81B99A8h, 067127605h, 02B8233EEh
            dd 06C93EA6Eh, 0144F948Dh, 05BAF6A64h, 03ACC560Ch, 0CBBE8F8Eh, 0078FFFF4h, 02647BF56h, 01111D8CDh, 0F2E47242h, 0D1D998FFh, 0B80B52DBh, 0CF71B901h, 0FF25C599h, 0624F918Dh, 021DA45F4h, 044EDD9E9h
            dd 0D4816874h, 0DBC1E9BEh, 071B486A0h, 0A0635953h, 080F959F1h, 02B5D9062h, 089B928EFh, 0D3062861h, 0047C9AF2h, 0320587CAh, 078BA18B2h, 0DEB34603h, 05E0A2A96h, 020B8DDA5h, 03ED55827h, 0A08CC327h
            dd 080B319DDh, 0E6867213h, 0CC1CF620h, 05A5F80FFh, 089785898h, 073607624h, 0305FD5BCh, 0CA4FAC4Ah, 05948F5F6h, 0D764BAC9h, 07CBC12DCh, 022492738h, 0F123D2E7h, 013645D01h, 08025AF9Fh, 0415FE2BAh
            dd 0702A1F6Bh, 025804FCCh, 06AA99AE5h, 0378F0BCFh, 0C63BAA83h, 00FB6902Ah, 01C38D6CEh, 005CB7467h, 0E358852Eh, 0C008201Ch, 0A5E3503Ah, 0AA133CA2h, 0D880CF6Ch, 049651091h, 005A94A4Ah, 026754470h
            dd 0A4D4584Dh, 0A8CD5FB9h, 03C7A72EDh, 069E3BBD2h, 0472331A2h, 0EF50FE64h, 04B660B13h, 0837C71D7h, 0B1AC68A9h, 0EDEFDBA3h, 0216EE2DDh, 07511865Fh, 00322FF35h, 0C3991875h, 0DE701939h, 04ED0FA7Bh
            dd 01CC2C653h, 07F5EA4EAh, 0539E9E29h, 0DE9BBE3Ah, 00C6EFB14h, 0F32F9FE2h, 0BEC874ACh, 04671C17Ch, 0C3258069h, 04E0BCA7Eh, 0D11CA8B3h, 085632351h, 062075441h, 07102549Dh, 0DC7C2C6Dh, 09B6FD4CAh
            dd 0C7E487BDh, 07B143D5Fh, 0BDE70EAAh, 08887E5C5h, 005ED0ABBh, 04B317594h, 0567D217Ah, 04DBA4554h, 019F1DB5Dh, 0F46AFD7Dh, 0D60FA2DDh, 0D8F9F487h, 005200C82h, 063BED4F9h, 02DBC73D4h, 03B42034Ch
            dd 0B75A8D4Ah, 0BA1D0AF8h, 04B84A26Eh, 076A76095h, 041A05CA6h, 0E8888F9Ah, 04157128Bh, 087271D81h, 0A3017B95h, 0DD1E63C0h, 00E46E03Ch, 060B319F0h, 0EB8DF917h, 0A9AF9799h, 0B2401E8Fh, 010596503h
            dd 0FB05C62Ch, 04D5B1AE5h, 02E558A66h, 0A71B10A8h, 0C2A7E3C5h, 0B812EDD5h, 0618557D0h, 006E81AE1h, 081555E01h, 0FAF51E57h, 08BB072DEh, 03CC163BEh, 0062E19D0h, 013D39F6Dh, 07C07ED7Fh, 039B41B0Dh
            dd 063F33432h, 004DC6F16h, 03469A6A2h, 01DC31300h, 097E2AD38h, 0CCE18E53h, 0D4E7C06Ah, 0B9DD6A86h, 093ED76C1h, 06B100D22h, 04B7F38C4h, 03B1200AFh, 065F48EEDh, 0C15BDB85h, 08913F0A2h, 09553F55Ch
            dd 01F25F59Ch, 00F92F88Bh, 09FC20723h, 0B7AF4A9Ch, 09062BCDFh, 024036415h, 08B8C7D37h, 0BF16EE6Fh, 0E8BAC1B4h, 000703031h, 03F6232EEh, 08F98D1D5h, 0081D362Dh, 0C4085BE1h, 0CB534719h, 0362624DEh
            dd 00F9BFB2Ah, 05EABC524h, 03D5FBBE7h, 0A4DFC56Bh, 0DD250EC9h, 0B05A8E0Bh, 067766E48h, 0FA93B69Bh, 072CA61FCh, 0E913A674h, 07898704Dh, 01762F64Eh, 0EF7A23B2h, 0FAF81E81h, 050D6D2C4h, 00B4D8695h
            dd 03345240Ch, 0D1E9D511h, 00F3F93EFh, 0D643758Fh, 05E2C95F8h, 080E4EC45h, 09694A39Eh, 07844B3FCh, 0401E4468h, 016FB510Bh, 0E413F2EFh, 0E270400Ch, 00A1B437Bh, 0742D1655h, 029AEB1B4h, 023A83CAFh
            dd 0AB34A221h, 0986A2A42h, 02644BF2Ch, 04CEB68D6h, 013675F5Bh, 0A4B28DC3h, 0F9051C27h, 03B49F390h, 052A66C28h, 0781640D6h, 0A4C2C8C5h, 0F2B2EDFEh, 069F0B888h, 02295527Dh, 027AAC4E7h, 0803716EEh
            dd 05666637Bh, 0A42FB3B8h, 0709D1FACh, 0F5D7AF62h, 01CE66E02h, 0FCC56385h, 0A1ABC9F4h, 032828789h, 0A872B71Ch, 01D8673E6h, 0A9C5C2E0h, 04548BE23h, 01C1A60C9h, 01442D2D9h, 078F91B5Eh, 0201A4561h
            dd 046DC6919h, 0E3298061h, 01F39C360h, 0E3071A31h, 0589AB0EDh, 0891B8D7Bh, 09C85BA06h, 06CFF4FA5h, 032835753h, 00629EA39h, 0D1FB004Eh, 0DD02D4ADh, 0F2775D5Dh, 05A32957Ah, 0FD7DA60Ah, 00531A727h
            dd 07A8692EBh, 07666904Eh, 0F10AAB69h, 0046BDA55h, 0D991371Bh, 069B6EBB5h, 0BCB2EF5Bh, 0EBB05C06h, 000C73ACFh, 0230194B0h, 05E7682E0h, 099102D5Ah, 01D187D16h, 0C4769D5Eh, 0C73585F9h, 02E8C5D32h
            dd 0E2641001h, 03DF8E57Fh, 0072FC7A5h, 07A13CDADh, 09EDC017Eh, 07D848C33h, 02F1468E4h, 0AEB59CABh, 0125F428Fh, 0942C838Bh, 00E2558C7h, 0A862CA4Ch, 07CFDE223h, 072EFD966h, 0D440982Ch, 08A2B3770h
            dd 09E97D15Ah, 038BD7EE4h, 062772725h, 0240E0438h, 0975B1025h, 0C59762F5h, 0C6CA15B2h, 0A4EE2093h, 0772BAD73h, 0398BB69Ah, 0021752E1h, 0FCE89B71h, 01F169A64h, 0759B59C2h, 01680EF94h, 01B0E66F3h
            dd 09E0DD7F8h, 087B74B9Dh, 0F014CBEAh, 0112E7F18h, 0D41E6200h, 061ED8CFBh, 0B2A306C3h, 0EF6BE8B0h, 0013B4DBBh, 0122F2DEDh, 04B5E904Fh, 084B2B1FBh, 0F67477F9h, 0AB8C1C62h, 0AB147A4Fh, 0F015C8C9h
            dd 0C2B700CAh, 00AF45B8Ah, 0D2E5B3E2h, 043923F2Bh, 06516E93Fh, 03178EA25h, 0D1C13B28h, 06D2BF510h, 0DF802036h, 04F16D764h, 0B7C912E2h, 05FC00AA9h, 01105A7C2h, 015B01446h, 064DC494Eh, 0187F7EC4h
            dd 03AA58EE0h, 0C175A0ABh, 0F9FADF2Eh, 0B94A2273h, 02A51B391h, 045468BA3h, 04533B4B1h, 0201035C5h, 0E11838E6h, 0A132C63Fh, 07787D8C8h, 06F02A79Ah, 070EA1CCEh, 0D329506Eh, 072E76C72h, 0751E5803h
            dd 0E6D74F3Ah, 0DD3B3910h, 043523FAFh, 06226690Eh, 023D0C248h, 0AE686165h, 0FCD9618Fh, 01759C9ADh, 037E493EAh, 04691F94Eh, 06C7AE2E2h, 0A29878D0h, 01303C41Fh, 0C5E5D0BAh, 0B327B3E9h, 016E17785h
            dd 0D54E35D8h, 01C4406C9h, 0E2EFD363h, 04056D4EEh, 060930423h, 03ABF8B6Ch, 0D7C25290h, 051D681CAh, 0C1F43312h, 03F356091h, 0A4A12041h, 03A628E49h, 0F960A194h, 0FBD6935Ah, 048AB4E94h, 0FA08E95Ch
            dd 009F87EA9h, 09F8216B6h, 0B4C0CB7Bh, 071CA94F1h, 0E09A9B52h, 01A49E9A6h, 007E087E5h, 0D0879E2Ah, 09F48169Eh, 05C1C1A28h, 0212BA2E3h, 00660E7F7h, 02401D16Dh, 0750A9B3Eh, 012721D84h, 013638F56h
            dd 071E6FCCFh, 066036BD7h, 0DAD4E7B8h, 0E7628749h, 0A5D665B5h, 02E188A14h, 06A42F07Fh, 0938CDEDFh, 0A1D02E4Eh, 0BD3709E3h, 0D1DA68C9h, 015B274E8h, 083E7466Ah, 02372C756h, 01F7E30B7h, 06FF27995h
            dd 02D18BD19h, 061C9F44Ch, 0352D4738h, 0915ECEE5h, 0AE45746Ch, 0762A60D6h, 011F7BD4Ch, 099C562B8h, 0F6AD8941h, 062A73CF2h, 0D5DD72E4h, 06948552Eh, 02600FEBAh, 0162F47A2h, 061CE872Eh, 000D59827h
  d06A2D3Ch dd 01D8EA3B7h
  d06A2D40h dd 0A0C2C1F5h, 0C3CAEBFCh, 08E8E39B4h, 0EB08B656h, 012718ADCh, 00DD1AE5Dh, 0D4322AD4h, 080BD2979h, 04B4AA345h, 00E04B042h, 0F1016B97h, 00D6DDB3Fh, 05C1F0A42h, 0E64112DAh, 0E5FC0AEEh
            dd 04128EC99h, 03300D1E2h, 0A59AC3F5h, 0B0E2F9C8h, 06C0F4D75h, 0E20BE816h, 02C0FD3A3h, 042F33745h, 06EF1FCF5h, 068225DDCh, 08A7E32E4h, 0CC1FC445h, 0280E0B08h, 0C6540226h, 0BF09E1C9h, 00D47A0E9h
            dd 0C9176AAEh, 0FB812614h, 0BCBFFF31h, 0169AEC1Fh, 0214A17E8h, 0F6EA8984h, 090604C3Ch, 005F877E9h, 0708914A5h, 0DA4D4CA8h, 04B3DF8CAh, 0CC615147h, 087E37005h, 074CC4E4Eh, 0CD15F4FCh, 06AE69A27h
            dd 075491B08h, 0F656BF79h, 016085FB1h, 0CF8623BBh, 03ACA268Fh, 05FFC6F46h, 037160909h, 00C210BC2h, 0C6557FA9h, 07FAD6FA7h, 03F20F2E5h, 010E7327Ch, 02A0D1856h, 07679CEABh, 00E545B63h, 00BC9B9BAh
            dd 064BF1196h, 045507C22h, 0B594F375h, 0BDA69E8Ah, 0778D687Ah, 0EB42894Ch, 022F0FA1Bh, 046AEC3EEh, 050661FD0h, 05840D6FAh, 077664043h, 0A7B148E6h, 0005A05DBh, 0AC69813Bh, 093E8E61Fh, 0DFD02B70h
            dd 098695A78h, 0C89D8C1Fh, 08775DB7Eh, 0EE1A4E9Eh, 0F784FF98h, 0BBDDE786h, 0521D2F60h, 0C56FC05Fh, 02EBAE25Ch, 085388081h, 0E4D1C2E5h, 073CFA193h, 02BFB35A3h, 0169E891Fh, 05DA0B50Eh, 0F83BC17Bh
            dd 00057D88Eh, 08F1FD140h, 0AD8A07BAh, 054C241F6h, 0BCCFC90Bh, 0DFAB8904h, 0B58F98F9h, 0885410F4h, 030420A1Ch, 0E6537F5Ch, 0A48088CCh, 082013E95h, 08AD0AAB0h, 0D506C537h, 06AABC831h, 054DABCC9h
            dd 0BC8A89E8h, 09AD46AB5h, 0F8E3673Ah, 00EAE7881h, 0B54EC8A2h, 027BE6EB6h, 06C2545C7h, 08E9D94DCh, 0861E6600h, 08BB2A25Bh, 0A88282E6h, 0D6971FCAh, 02DF943F1h, 0C7C24593h, 0BCFB2FA9h, 0F5ADDA4Ch
            dd 0ACF08F75h, 0D9DE375Eh, 0967F0BFFh, 0FBDEF351h, 0F2011A9Dh, 0B31488BCh, 0481F46D8h, 0B91A6C09h, 01F2EF548h, 0745619AEh, 0D1B9D044h, 05E612534h, 014573F86h, 00DB30833h, 0317FBA54h, 0DAC44C03h
            dd 0D0AAC857h, 05C2B474Bh, 07850E3F7h, 01D41A364h, 07309A1BCh, 093AFE6F6h, 0773C7B2Dh, 038CB6969h, 0ED73D9B4h, 0913DC335h, 05D3452E7h, 0296F8EF2h, 03FF8604Fh, 077E70007h, 00A478943h, 0F22FE20Dh
            dd 04898466Dh, 024AD9C7Ch, 08F650F33h, 083E9A6CCh, 038447B2Fh, 0A77D8874h, 0EB9EE4B7h, 0FACFB90Eh, 0FF0BF073h, 0F359B200h, 00EE218CDh, 039B11BE3h, 09EDDC45Bh, 025504C2Fh, 008429C77h, 05FBDDD5Ch
            dd 0F4CAF7C7h, 02F6225E1h, 0E9BD7FB4h, 03CD5DD57h, 031B37AC5h, 0F08F6D27h, 082449184h, 0F1083DE6h, 045D75C67h, 0A8B8E51Fh, 002E512F7h, 08D47FC19h, 031F67D9Ch, 0270CCC8Bh, 05992E3EEh, 0E090FBDEh
            dd 0F321FD55h, 07E6BF28Ah, 0785A1378h, 02AF55827h, 07E76CCB0h, 09CD6872Dh, 07D2D9295h, 03B750513h, 0EFD7EBAFh, 0816C5C52h, 04A1C5056h, 014010282h, 018536921h, 05DFD8F2Bh, 0EF168E99h, 0C4B76D95h
            dd 027DB3636h, 0F1A90277h, 05A2BFB70h, 05B69083Bh, 00E7D53DFh, 06C60E567h, 09D5BC7EBh, 0BA360274h, 0BD2BCF1Bh, 0BE4306E9h, 0B787E2F8h, 0E01F5B40h, 032F59AEAh, 0C72187FFh, 0A8DD5D89h, 0ED0203AFh
            dd 09FC9A45Ch, 0B82A57A8h, 0704F17BDh, 0C1110B92h, 0C3B91D42h, 0704F87E5h, 000BD3074h, 06D3B5218h, 0CFB4E6DBh, 01F6FF5B4h, 07745A8DEh, 0FF61F831h, 091DAFEF7h, 086AAC317h, 0B5E960BCh, 049A1EEEEh
            dd 04BFB65A6h, 0C3F0E01Dh, 0CB98873Dh, 07BFD322Eh, 0CC382CF9h, 0D8516CA7h, 0B752ED41h, 06364D601h, 0158042CEh, 0B4CE28C3h, 06B38A2F9h, 033E7C967h, 034F3A738h, 078564373h, 0F729B723h, 0EA841C60h
            dd 03B616B44h, 002F9BDC6h, 069252BF1h, 0582DBDFDh, 009FB7ED3h, 064A8869Dh, 0934CDE52h, 0AEE1AE2Dh, 0AE90D106h, 0AD619F06h, 0A47FE057h, 0CBB1EFD1h, 01B4093CCh, 0AE460613h, 08CBC52DFh, 0CF9B7E27h
            dd 06F1CA416h, 08537CDA4h, 03C0503FAh, 08A916D11h, 08AF20502h, 03432D4D7h, 0C26A13A8h, 02DA2AB8Eh, 07CE4B572h, 0CB49499Eh, 020E972F9h, 096CE389Eh, 036E1C485h, 0287B0EF7h, 0557421CEh, 0D7F62432h
            dd 0D70A123Bh, 05DB812D5h, 0521A2F36h, 0F0396068h, 04F3DCF65h, 058017655h, 026DB8C31h, 0EF97FB22h, 08E7CDC32h, 02C743869h, 0D19838D0h, 0A600D580h, 0A5C62892h, 0D6E34A00h, 0537034F1h, 034950F70h
            dd 0833CD385h, 0587EAB4Ah, 0BC738FB6h, 0A92597F4h, 048BDDE1Ch, 0A1135B17h, 0DD71390Eh, 0E6D07F1Bh, 0D4492826h, 0D1D46B78h, 0D59B32FAh, 0FA96A6B5h, 048E0D1D3h, 0C890CA6Ch, 0A4CF8B69h, 0D5683DF3h
            dd 073A2D923h, 0977778F3h, 04B00337Bh, 0975513C4h, 0857020F7h, 04D79751Dh, 0C86B2A10h, 0205D4738h, 06E59C76Eh, 0BA77D2CBh, 00DC17058h, 08160CC3Fh, 02F4DCD68h, 00E908D0Ch, 02A431614h, 0B98F9FC9h
            dd 0A65C02F5h, 02AC489E0h, 02DD01B73h, 0C9B9C2E7h, 00577B726h, 01D04D347h, 0E8896F65h, 09F0E4498h, 03CADABE9h, 0D75F8C42h, 08A4CF2FAh, 05D6E15ECh, 049EEEE30h, 078C585E0h, 0F31BF513h, 0D2EA45C4h
            dd 02E4A801Bh, 0E146DD01h, 033F537BFh, 02E61C53Fh, 0CAA27188h, 021D275C5h, 05BEAD80Eh, 05203944Dh, 05E35C299h, 0487A7B1Dh, 04AFBC8D1h, 05CB0B2DEh, 0A8C3523Dh, 0273DC108h, 000170847h, 03E792002h
            dd 0DA6D4165h, 0EC0B6676h, 09E4E973Fh, 0D85DFCCAh, 0D321803Fh, 089E55A87h, 0F29085DCh, 0584C1825h, 0A4F11E9Dh, 0EDDAAE2Ch, 03EEDC2FBh, 0A0468303h, 04BEC0B8Eh, 029E94154h, 042565FBEh, 0DF5C5E85h
            dd 0CAD34702h, 03B05332Fh, 02CEA4BF4h, 0C57D68AAh, 010E5D21Ah, 0153B748Dh, 0EE7A76EDh, 093B9D042h, 03D01BDC5h, 0C68D157Fh, 077240059h, 03800A98Dh, 0224AF703h, 05FDA04F4h, 0D7EAEA69h, 0A473B05Ch
            dd 0FE8D70D4h, 0BE42441Ch, 00FBB230Ch, 0F7E127BDh, 091EC5949h, 0E5C6D2B7h, 01DA7AB32h, 0127AEDA2h, 00B569141h, 0F464CF06h, 0E39F82FCh, 0031EF23Bh, 04DEB18DCh, 0C90E0CD8h, 090A2C958h, 0CCCE6656h
            dd 0667BFEFAh, 076C4983Dh, 025C04F48h, 06D981A05h, 0562723ACh, 00A947436h, 0710924CCh, 0D47F2D57h, 01DEEA8F0h, 05580BEC1h, 0A45E58D2h, 013609F2Ch, 0ACC08CD9h, 0878748F0h, 0AEBDDC8Ch, 0296D4195h
            dd 012ADBF44h, 0818921A2h, 07F28AFC9h, 016745190h, 05FA63252h, 052B759F8h, 028AFD199h, 0DBA7B13Fh, 063BA04F4h, 0FAEFE1D0h, 0A84052FCh, 067E66062h, 04FD93529h, 07933C84Ch, 0FFFD23F3h, 0CA4F7F27h
            dd 00214B5E2h, 0C092FE5Bh, 01EB5438Dh, 004A4CD70h, 09C69844Dh, 0EE0D73FEh, 00398C2AAh, 00524795Ch, 0FDCAA32Ch, 0E3835823h, 0D087905Bh, 0EEB086DBh, 0363611AEh, 0AF248BECh, 07571BEAEh, 0AE56D1EEh
            dd 035CEEEC3h, 043D00F48h, 0E0963B85h, 026188C84h, 01D600B6Ch, 0CE97D138h, 033B6F7F0h, 084E586CDh, 0CB1E87A8h, 0006A02BAh, 05DF212FDh, 0BACEDF89h, 050D84277h, 0295883C0h, 04E488D9Eh, 0C7B187E8h
            dd 0ADBC6CD9h, 01A515379h, 006AA57C2h, 09BB07FDBh, 0D1ACD5DFh, 0D26673A6h, 0A6287089h, 047DAC661h, 0DDA79E58h, 06196F175h, 00DA0D8D3h, 0C9007C7Ah, 0BFBDB674h, 0D8C1CFE8h, 04B44A0C1h, 014406227h
            dd 059EE2D33h, 01517ECDEh, 06103B742h, 045ACB677h, 0DA2BE476h, 02A785868h, 04ECD2D56h, 04D135A4Ah, 02373FA5Ch, 016F52484h, 001A3E2FEh, 01D964DB0h, 052D66FC5h, 0DA7D4F44h, 09D94F738h, 0B42380BAh
  d06A353Ch dd 0495413C1h
  d06A3540h dd 05410B988h, 0FF905B06h, 032DC2246h, 017EE2661h, 0C6CF726Eh, 039A71E68h, 088801266h, 0CC839983h, 0FF989BD8h, 04ADA205Ch, 0A560532Ah, 039334B49h, 0007D02D4h, 0221782E4h, 0994AF270h
            dd 08D0F5CA3h, 0E85ECA75h, 0D261430Fh, 06430E15Ah, 098D5BD8Fh, 09669D0A8h, 058D543BDh, 007412FD7h, 09AD77D0Fh, 01D70456Fh, 0B654A2FEh, 0706EBCD7h, 054D47C12h, 07AA20AB9h, 0EBEF51D3h, 0B195987Bh
            dd 0F5EDDAB9h, 0AFEF1EA6h, 0E8856F4Bh, 0DAE8D4A1h, 06D1087F2h, 0AB387226h, 0CC37BC46h, 0B9466F7Bh, 0AC5F84BFh, 08E9B343Ah, 0770368D5h, 080BF49D9h, 0B3BAE01Fh, 0381A46D1h, 0F9EB7406h, 01E3483C9h
            dd 0A11F9B12h, 0BAA5A71Bh, 042DECFCBh, 073D41B4Dh, 066909699h, 0034A57D8h, 073EC7913h, 0B07FF354h, 0F22CE0B3h, 023FB6739h, 06BF672FFh, 0C4452A0Eh, 056D39960h, 02AC7B63Dh, 03A2BCB7Eh, 0BF17A14Ch
            dd 0918581A0h, 0F9AE74B4h, 0E17B6380h, 061F4871Dh, 0A353D884h, 09F9171DEh, 05EC66A25h, 0FAFCBC81h, 08C3C8FFBh, 01C9EDE8Ch, 0A33CB05Dh, 05B0F3078h, 03D3075F5h, 050B77ADDh, 0C0BE5629h, 0932E1302h
            dd 0C43FCA82h, 07CEB85A1h, 0B34B4B88h, 093684620h, 0245A6FA3h, 06F2BDF18h, 08EE49F7Ah, 079BDC8E1h, 05A805366h, 039868813h, 020A732F0h, 0271D9925h, 057D1A6BDh, 0DBEC71A1h, 099762518h, 0AC89B90Dh
            dd 03C2E4898h, 0436DD9D2h, 0D95077C4h, 008103988h, 0E8953915h, 083F97196h, 0E1550803h, 03CA20886h, 06C187A26h, 09AA177EEh, 0D066F8D6h, 0375F2627h, 0C6A61ACAh, 08954BDC9h, 09682384Ch, 00928A45Ch
            dd 0E850F9F2h, 04E226247h, 024B9D745h, 0B2FC7013h, 0E11448BCh, 0DB1C5648h, 099FBC5D1h, 032EB9C6Eh, 0B2E4D61Ah, 04F01AAFDh, 0D559F200h, 08AE5075Dh, 069C0B30Bh, 07B113D25h, 0E8C19FB3h, 0A9FBC2DEh
            dd 0D8C6FF8Fh, 08D3C2FE0h, 0C2457B09h, 0AF2CECE3h, 02ED88AA7h, 06762704Eh, 084E5B6E2h, 07D68559Bh, 04CF57552h, 028A40130h, 00D8F406Fh, 012BF1DC6h, 0402DAF90h, 0B101F1C5h, 06D452A6Eh, 08E123495h
            dd 00C703861h, 0117930DDh, 0A5265301h, 0D1909BF6h, 0AFDF11C6h, 047FDDE89h, 0A302EB37h, 0EC1951FBh, 0194949CEh, 0468BBBC7h, 0890AC201h, 0DDBD7684h, 06BCED059h, 02B35F899h, 0371DF95Eh, 0A67DDAAFh
            dd 0746EB687h, 0D8FB840Eh, 0BB3B8F4Dh, 037389E5Eh, 0641AEB39h, 05CCB7007h, 0177454C1h, 0AE1EA190h, 03BD1608Dh, 0B7A7AA92h, 04AB988E7h, 0ED0F0375h, 0CAA34466h, 0D9AE34B1h, 044180C81h, 0030CC5EEh
            dd 0309167D1h, 0D3B01D73h, 01583EFCEh, 0E023C5EAh, 06D99EAEFh, 0B4DE55C9h, 0BE1A019Eh, 0A5572579h, 081ADCC71h, 05C16DDA1h, 03EBB8201h, 03195E4ABh, 06DCCEDA6h, 0DB5AB41Dh, 0856863F8h, 0A4EFE360h
            dd 020076D7Fh, 022BAEA2Ch, 0A4208382h, 0DE5341B9h, 0BA4C3CCAh, 040247FBFh, 0A9F302AFh, 0E0C4FEA5h, 01BBD5BB9h, 035BA44E4h, 076E2D060h, 0C85FFA14h, 04429D93Bh, 0014B78BDh, 01BECFEA3h, 078055527h
            dd 053B1A641h, 0A5F7FB09h, 086F16B8Ah, 000B7F0DDh, 03B44C3F9h, 010CEDDF9h, 0D92137F5h, 06E84FA06h, 0E9F13F25h, 06291FE7Bh, 0E35D5202h, 0946D53D2h, 06FCB0AF4h, 07C8F7F91h, 0D4A3CD93h, 09150FB31h
            dd 0CB9F2466h, 06C884F3Ah, 0AC1687C7h, 0756FF324h, 0FF8F9D5Ch, 0349D7F77h, 03C83A08Eh, 021894AAAh, 0FB8A56E5h, 0C3BDED46h, 0A31B18E8h, 0A4BFE0C4h, 0CDA06E01h, 03AF8BBA9h, 0E1BFD0C6h, 0FEFFE670h
            dd 077C1D5B0h, 0774ED8A0h, 0F76EF747h, 02F5B2AB0h, 0F80E9C03h, 08CAF5439h, 0E4385D5Bh, 017B2CE93h, 04156B2D9h, 0681C1056h, 0A80E1203h, 0F735C1F9h, 060C91742h, 02CA43B05h, 033FF273Dh, 09ED204F3h
            dd 06737DB5Eh, 0B647A559h, 0950B9B0Bh, 00D7BA68Fh, 035C1EEEEh, 018F67E3Fh, 0CF125E6Dh, 0523F97BFh, 0DB664110h, 052B087A9h, 0D0455061h, 07F0FD773h, 0472603D6h, 05295FFA6h, 0B883C2E9h, 073E976C9h
            dd 09BE21420h, 03A85B646h, 068DC7304h, 03EEF55A3h, 0B6C8751Ch, 0F880DC69h, 0FE3083B2h, 0D1F09310h, 0A8BA259Ch, 07FA73120h, 05CBFE203h, 04A1D3020h, 072B734AFh, 0DCC9F68Ah, 0824A91D8h, 09B441CC4h
            dd 003D08246h, 001060A67h, 08EE09F40h, 0A49758FBh, 07B043F7Fh, 00D5F7EE7h, 062A2FC5Bh, 093E5E3B5h, 0BA424C4Ch, 0D0C220FBh, 00D7EA8EAh, 05A5FCD12h, 0D19D98ACh, 08A3132A2h, 08F56A41Bh, 0E8E3F702h
            dd 0BF02439Fh, 00CCC93DCh, 0E8490FC0h, 05D738F86h, 074834E26h, 0556153A9h, 00947A929h, 09A2E67ADh, 0001FA840h, 08522530Ah, 00161A204h, 0AEE49E47h, 074B641FDh, 07CFEB2FEh, 0D096FB83h, 089B62585h
            dd 0AF78493Dh, 04BC56085h, 077D6A385h, 04BA30B66h, 0B1469001h, 0F1C87DAFh, 0F4319A3Ah, 0D5AB30CAh, 09A2F3778h, 06EC5CA5Dh, 04998E062h, 035BEB4C1h, 05B133D72h, 0B2EE769Eh, 06619963Eh, 06DDD875Bh
            dd 0E322820Fh, 0DE137172h, 059B68B8Dh, 07D17BA79h, 0324D2730h, 0C152DBD9h, 0145FDF8Fh, 0535C3C2Ah, 078731BF3h, 08BAD75E4h, 0B6127205h, 001BC0D7Eh, 076B46E4Bh, 02D137D72h, 02FE1652Dh, 086383D56h
            dd 05A11F025h, 09594C593h, 060CBA7D9h, 0E2BFBDC1h, 00688E192h, 0D5206D57h, 087B04818h, 006518CDFh, 08A0B32A3h, 0FCC863AFh, 076C138EBh, 0010EAA60h, 0D499C247h, 0EB8BB99Ah, 03CED7851h, 0E3C71895h
            dd 00643B17Fh, 090595E08h, 0CA14075Ah, 08CABE45Ch, 00FF7F049h, 03D334219h, 03F66F5E6h, 00C9A00B7h, 0D0D78EA7h, 0913896BEh, 06BC43205h, 054947CA6h, 077B37B98h, 0DD3839E6h, 07E3CCFC8h, 083AA4627h
            dd 0F6B9B71Dh, 0EF532BB1h, 068B0BB1Eh, 07ADB603Ch, 04CBB4234h, 0C9896C10h, 01A40E6F7h, 04707D9D4h, 06AD82DDFh, 07BDB0D01h, 0A3FA7064h, 0EC5E911Fh, 05E10671Dh, 00328FD86h, 003B05A73h, 068C1A8EEh
            dd 02A53F0FEh, 063903CAEh, 03B819316h, 0AB3F1F4Fh, 0CDB2D953h, 09A14CA5Ah, 0497D2B4Ch, 0C6C8D534h, 0372C015Bh, 0A8B3B898h, 02F65F206h, 0B76CEACDh, 079B188E6h, 08D6DF46Ah, 0DD783963h, 0711C5EE8h
            dd 092416E04h, 03A1280DFh, 051A6BF53h, 011E71297h, 082FD93C6h, 0BEF26CC8h, 0ADDF94D6h, 088CD25E9h, 049B4280Bh, 009DEA653h, 0D024C8ECh, 0C7BE87BEh, 0D896FCE3h, 03BD53082h, 0DA834C96h, 0DDBB3927h
            dd 04E742F6Eh, 035E71934h, 0ABFE1FD3h, 0CAD34933h, 08B7CA26Dh, 00605418Ah, 0547541A3h, 08FF6A9C2h, 09F80740Eh, 0BE3DD962h, 0D416C207h, 01B3459F4h, 08BBFA533h, 02D81B0EEh, 02BC3930Dh, 07E9E67B9h
            dd 03EEA25FCh, 074E1E6EDh, 04A8BC397h, 0B8F2B502h, 0C83FF457h, 0925B6B80h, 0406E32B4h, 0BA7362EEh, 029901336h, 097D140B5h, 00C5D0065h, 0A20E7E6Dh, 0620D91B8h, 06372747Eh, 0A1572EB9h, 052A5C970h
            dd 062945EDEh, 0072EF7DAh, 02C6CAB9Fh, 0DA667416h, 049377B76h, 072E6C9CAh, 06F8C670Ah, 038347E5Fh, 0F7E5F7B2h, 0B4B8FB4Ch, 089C88207h, 06E1CC71Bh, 08DAEB281h, 0DEA67B52h, 07A1F0DA8h, 07B0F7F7Bh
            dd 0D982DCE3h, 0CEA04B0Ch, 03371C7DCh, 04F1E676Dh, 00E7245E9h, 086B46B48h, 0D2FEE093h, 0FB28BEF3h, 0097D0E62h, 025E4E918h, 0497658EDh, 07E5E641Dh, 0EB83268Eh, 08C1FB77Ah, 0871A10DBh, 0D8AE5AC9h
            dd 095B49D4Dh, 0C965D471h, 09DD9275Ch, 0F9FAAE09h, 006F15480h, 0DEC640FAh, 07A949D60h, 0F16142DCh, 06F496A66h, 0CA431C17h, 03E795208h, 0C1E43642h, 08EACDFDFh, 08ECB37D7h, 0C96A6742h, 06871884Ch
  d06A3D3Ch dd 0852B93DBh
  d06A3D40h dd 0196EA11Ah, 02B66CB10h, 0E72A1AD8h, 053B4A67Bh, 07A2D6AF0h, 0658D8E72h, 03CEE0BF8h, 0F95909ADh, 0A4E7836Ah, 076A09066h, 059BE5BBCh, 06509BB64h, 0B4CCFB77h, 05EEEF2FEh, 04D98EA02h
            dd 0B9D5CCBDh, 09CBCB207h, 00E37B319h, 0188ED9ECh, 0D4BB2D99h, 04BB7C83Ah, 094ABB3C7h, 0BB9F1769h, 0C6ADED19h, 0D1CE3EF1h, 0E22A1208h, 025BCA469h, 080AAEC2Ch, 02EF0F24Bh, 018B5C1EDh, 066F3900Dh
            dd 021C34AC3h, 0533D0638h, 0245BDF55h, 08E36CC44h, 099E7F70Ch, 06F866AB8h, 0F80D3C50h, 07D94570Eh, 0D835F4D9h, 032E92CCCh, 0A3D9D8EFh, 0340E416Bh, 0EF805039h, 0EC683E63h, 025B1E410h, 0C2827B5Bh
            dd 0DDF50B2Dh, 06EF39FADh, 07EA43FD5h, 0372203DFh, 0926606A3h, 0B7984F6Ah, 0AFB2E92Eh, 064DDEBE6h, 02EF250CDh, 0D7495FDBh, 097DCE209h, 088941390h, 082A9097Ah, 0DE15BECFh, 076F13B88h, 06365A9DEh
            dd 0CD5BF1BAh, 0ADFC6C46h, 01D41D39Ah, 015427FAFh, 0DF29589Eh, 043EF6960h, 08B9CDA3Fh, 0AF4AA413h, 0B802FF05h, 0C0FCC61Eh, 0D0032077h, 00F5D280Ah, 06906E50Fh, 00505726Fh, 0FC84C633h, 0478C0B94h
            dd 0F1053A9Ch, 0203A7D33h, 0EF11CB92h, 047B63EB2h, 05020DFBDh, 02379C7AAh, 0BACA0F84h, 02D0BB073h, 08656D371h, 0EDD471A5h, 05C7DA21Ah, 0DB6B81C8h, 094A716C8h, 08F3A7933h, 0C54C9522h, 060D7A2AFh
            dd 068F4B8A2h, 0F7BBC164h, 00636E7DEh, 0BC6E211Ah, 0156BAA20h, 037586928h, 02D2B781Eh, 0E000F018h, 098EEEA30h, 04EFF6F70h, 00C3C68F0h, 0EBBD1EB9h, 0F28C8AD4h, 03DB2B55Bh, 0C258B856h, 0BD769CEEh
            dd 014267A0Ch, 0F2805AD9h, 0608F475Fh, 0664A68A5h, 01DEBB8D6h, 08F6A4FEAh, 0C5C135EBh, 0E63984F0h, 0EEBA4624h, 0F35F928Fh, 0012F721Ah, 03E33F0EFh, 095963315h, 02F6F25B7h, 014970FCDh, 05D59BA60h
            dd 0049C6FAAh, 0328A2782h, 0FE1BEB13h, 0547AD475h, 05AAEFBB1h, 02BB068D1h, 0B0BB26FCh, 021B64D2Dh, 078CBE56Ch, 0DDF209C2h, 03965B079h, 0C60D0558h, 07CF31FAAh, 0655FE957h, 0A92B9A78h, 032602C27h
            dd 03846A97Bh, 0C5C7386Fh, 0D1FCC31Bh, 085EE9399h, 0DBA581E0h, 0FC4BC61Bh, 0DFD85B41h, 0A077598Eh, 0561FB9D8h, 0FADAB459h, 0B5D0321Bh, 0921B6E16h, 097944063h, 0DF84E03Bh, 063E36968h, 05BCBC331h
            dd 0B0342691h, 08C498C90h, 0E701FF58h, 0FB8686E0h, 090E05C43h, 000196899h, 0434AC4DBh, 0526C9922h, 067A7D098h, 06B05A224h, 0768FF8F1h, 0915DFB07h, 0F679B470h, 08DFC2C43h, 070EF7C9Bh, 0B76ABD70h
            dd 06C67E8EBh, 0870E1505h, 0416A5FD8h, 09472CD7Ch, 0A96F6AFAh, 0682C4E5Bh, 0FAE081A8h, 059A52D1Bh, 0BD733C8Bh, 00064D533h, 06A81F21Ch, 0E5E3DD3Dh, 099925DB1h, 08FA8ACAFh, 0B13ED302h, 0583DDBF2h
            dd 05CDDDD89h, 0D608E2BEh, 0E0F6F39Ch, 082A2394Bh, 0E622ADD4h, 0F4726751h, 0D6CA72B9h, 09422E637h, 04783DBC3h, 0E9083C86h, 0A3B8407Ah, 07CBDE2B7h, 070F04945h, 0B6A9604Fh, 047B26EBEh, 02C544DC9h
            dd 08087175Bh, 05945E39Ch, 0B2C7DB95h, 0B405F86Fh, 0671A3303h, 0C41DC58Bh, 005F7A70Fh, 012D3E298h, 015D8AF4Fh, 016EFE70Eh, 01F33C21Ch, 048BB4B64h, 09B917A1Eh, 020DD6723h, 0008A3DADh, 045BFE4C4h
            dd 0F7759470h, 010D737CDh, 0D9EC07D1h, 029BDEBB6h, 02C550E66h, 0E8EB6709h, 0685910A8h, 0D5D8323Ch, 02750C6FFh, 0770BD5D9h, 0DFE188F3h, 0580DD856h, 0FA76EE1Bh, 0EE46A33Bh, 01E8540E0h, 0A24DDE02h
            dd 0A39746CAh, 02B9CC032h, 023346751h, 0D3A91242h, 024D40C1Dh, 030FD4DCBh, 0100FCD75h, 0DC10B625h, 07D2C22F3h, 01D6A08E8h, 0D4D4821Dh, 0AB93BA8Bh, 0AC9F876Ch, 0D0F22398h, 06FC5A758h, 04220FC85h
            dd 0930E4B68h, 06B959DEBh, 0C1D10B15h, 0C1C99E21h, 061975FF8h, 0CC4466C1h, 0FBE8CE87h, 0068D8F42h, 0073CB12Bh, 0061E7F3Bh, 00C1BD07Bh, 0335DCF05h, 083FC73E1h, 016E3E738h, 0E4593203h, 027476E5Bh
            dd 0C7B8853Ah, 0FED3AED8h, 0A4A2E31Eh, 0E23D4D35h, 0E29EE527h, 0ADDEC4FBh, 02A16F3DCh, 0854E8BA2h, 0D58095A6h, 023F52AC2h, 08886521Dh, 0FF6B28B2h, 0AE9EA4B9h, 08017EE1Ch, 0BE1001F2h, 040920556h
            dd 03FA60250h, 0B554F2F9h, 0BAB60F6Ah, 058E5408Dh, 0A7DAB089h, 0B1AD6679h, 09E786C65h, 04733DB57h, 0E619BC56h, 09411188Dh, 0394418F4h, 00EBDB5A4h, 00D6308B6h, 03E902A34h, 0BB2C1425h, 0AC31FF94h
            dd 0EBD8B4AAh, 0B01A8B6Eh, 0151F7FEAh, 002C17718h, 0A059BE30h, 009CF4C3Bh, 0351D1933h, 04E7C5F3Fh, 04CE5185Ah, 039704B9Ch, 03D37121Eh, 0523397E9h, 0A08CB107h, 0304CAA80h, 00C6C6B9Dh, 03D041D27h
            dd 0EB4EB947h, 00F235817h, 0A3AC13AFh, 0FFF1F3F8h, 0ED1C011Bh, 0A5166531h, 021071A44h, 089F9285Ch, 0D6F5A782h, 01213B2EFh, 0766E507Ch, 0E90DAC53h, 087E9AD8Ch, 0673C6E20h, 092EF0648h, 0112B8FEEh
            dd 01FF9F329h, 082616904h, 0858CFBA7h, 02165A20Bh, 07E13974Ah, 075A0C37Bh, 040254F99h, 007AA24BCh, 0A4498B0Eh, 04FFB6D76h, 0E2E8E21Fh, 0B51A0501h, 0A28ADE55h, 0D1616504h, 05BB7D537h, 03A8626E8h
            dd 087E7603Fh, 049E2BE35h, 0AC9117E3h, 0960DA553h, 0335E62BDh, 0897F65E9h, 0B396B823h, 0CAAF7461h, 0B6D1A2BDh, 0A0265B31h, 0A297A8F5h, 0C55C92F2h, 001603252h, 09FD9A12Ch, 069B3E86Bh, 097150027h
            dd 032192299h, 054A8469Ah, 0F6FA8764h, 040F9DDFEh, 03BDE6053h, 0E1814BACh, 06B3C65F0h, 0B1E8F849h, 00CAE0EB1h, 046868E40h, 0979AA21Fh, 008E26438h, 0A389EBA2h, 081862188h, 0AA023FD2h, 037F83EB9h
            dd 0228F2727h, 094A11343h, 094872B28h, 02E2958CEh, 07881B34Eh, 07ED764A2h, 046165601h, 0FB55C166h, 096BE9DE9h, 03F29F593h, 0DFC0E07Eh, 090AC89A1h, 08AE6D727h, 0B776E518h, 03F86DA8Dh, 01C1F9070h
            dd 056396108h, 017EE2430h, 067570320h, 05F8D07D2h, 0F988496Dh, 04E72C2ECh, 076449B57h, 07A16CDD7h, 064027165h, 05C01A02Ah, 05B4B7210h, 06CBAD25Fh, 0A58708F0h, 031BBECFCh, 0095EA97Dh, 0256A477Ah
            dd 0CE17DE1Eh, 0EE707961h, 08D7C2F6Dh, 0C5350A29h, 0BEC304D0h, 06240546Ah, 0D9A504E0h, 03C1B1D7Bh, 0758A9815h, 0BD2C9EF5h, 00CFA38F6h, 07B0C7F50h, 0146C6CFDh, 0EF232814h, 0065ABCA0h, 0810921C9h
            dd 07A4A9078h, 0E93501C6h, 0E8C58FEDh, 07F2132C5h, 0B7421277h, 0BA533A1Ch, 0805BB1BDh, 033449154h, 0CB56E419h, 0628CC1F4h, 000EC3211h, 0CF824186h, 0B785154Eh, 0D1D0A870h, 057990317h, 022EC5F4Ch
            dd 06AB09506h, 0283FDE70h, 0766133A1h, 06C41BD94h, 0F4066561h, 056A95312h, 07C35A2CEh, 07EC16A70h, 055668350h, 04B2F3848h, 03923707Fh, 0565C66F0h, 09EE301D2h, 018C06C00h, 0DD2D9EC3h, 007F3B102h
            dd 09E6ADFE8h, 0BB7CEF6Dh, 058321BAAh, 09EB56CA8h, 0750DFB80h, 02634B15Ch, 09B52E714h, 0EC8266E1h, 023BB67CCh, 06816E3DFh, 0B59E0211h, 0226ABFADh, 0B984229Bh, 082F463E4h, 0A6E57DB2h, 02F5E680Dh
            dd 016584CFEh, 072FE349Eh, 06F4737E6h, 0F36C6F0Fh, 03A48B6F3h, 03A0253DAh, 00EC450ADh, 0AF77B685h, 045438E8Ch, 0D932D1AAh, 0755DC8F8h, 022AC5CAFh, 0185996A8h, 0306DAF0Dh, 0B4F080E5h, 08CFD425Bh
            dd 0C18A0E57h, 07EB3CC03h, 0C9AF9766h, 0AD48979Bh, 043C7C49Ah, 09225398Ch, 0A66A0D7Ah, 0A6B03A6Eh, 09B1FDA70h, 07F9104B9h, 06A4FC212h, 075322ED4h, 0BA724FE9h, 032292F69h, 0F530D75Dh, 02CCF70DEh
section .code use32 CLASS=code
;############################### sub_420620 ####################################
global __func4
__func4:
    PUSH    EBP
    MOV     EBP,ESP
    SUB     ESP,058h
    PUSH    EDI
    MOV     ECX,0Fh
    XOR     EAX,EAX
    LEA     EDI,[EBP-054h]
    MOV     dword [EBP-058h],0
    LEA     EDX,[EBP-058h]
    REP STOSD
    MOV     EAX,dword [EBP+8]
    LEA     ECX,[EBP-018h]
    PUSH    ECX
    PUSH    040h
    PUSH    EDX
    MOV     dword [EBP-054h],EAX
    CALL    .sub_509330
    MOV     EAX,dword [EBP-010h]
    ADD     ESP,0Ch
    POP     EDI
    MOV     ESP,EBP
    POP     EBP
    RET

;############################### sub_508310 ####################################
.sub_508310:
    PUSH    EBP
    MOV     EBP,ESP
    SUB     ESP,074h
    MOV     EDX,dword [EBP+0Ch]
    PUSH    EBX
    PUSH    ESI
    PUSH    EDI
    MOV     EAX,dword [EDX]
    MOV     ECX,dword [EDX+4]
    MOV     EBX,dword [EDX+014h]
    MOV     dword [EBP-8],EAX
    MOV     EAX,dword [EDX+0Ch]
    MOV     dword [EBP-4],ECX
    MOV     ECX,dword [EDX+8]
    MOV     dword [EBP-0Ch],EAX
    MOV     EAX,dword [EDX+010h]
    MOV     EDX,dword [EBP+8]
    MOV     dword [EBP-010h],EBX
    MOV     dword [EBP-064h],ECX
    MOV     ESI,dword [EDX]
    MOV     EDI,dword [EDX+8]
    MOV     dword [EBP-01Ch],ESI
    MOV     ESI,dword [EDX+4]
    MOV     dword [EBP-044h],ESI
    MOV     ESI,dword [EDX+0Ch]
    MOV     dword [EBP-048h],ESI
    MOV     ESI,dword [EDX+010h]
    MOV     dword [EBP-04Ch],ESI
    MOV     ESI,dword [EDX+014h]
    MOV     dword [EBP-020h],ESI
    MOV     ESI,dword [EDX+018h]
    MOV     dword [EBP-024h],ESI
    MOV     ESI,dword [EDX+01Ch]
    MOV     dword [EBP-030h],ESI
    MOV     ESI,dword [EDX+020h]
    MOV     dword [EBP-034h],ESI
    MOV     ESI,dword [EDX+024h]
    MOV     dword [EBP-028h],ESI
    MOV     ESI,dword [EDX+028h]
    MOV     dword [EBP-050h],ESI
    MOV     ESI,dword [EDX+02Ch]
    MOV     dword [EBP-038h],ESI
    MOV     ESI,dword [EDX+030h]
    MOV     dword [EBP-03Ch],ESI
    MOV     ESI,dword [EDX+034h]
    MOV     dword [EBP-040h],ESI
    MOV     ESI,dword [EDX+038h]
    MOV     EDX,dword [EDX+03Ch]
    MOV     dword [EBP-018h],ESI
    MOV     dword [EBP-02Ch],EDX
    MOV     EDX,dword [EBP-8]
    MOV     dword [EBP-06Ch],EDX
    MOV     EDX,dword [EBP-4]
    MOV     dword [EBP-070h],EDX
    MOV     EDX,dword [EBP-0Ch]
    MOV     dword [EBP-05Ch],EDI
    MOV     dword [EBP-068h],EDX
    MOV     dword [EBP-060h],EAX
    MOV     dword [EBP-074h],EBX
    XOR     ESI,ESI
    JMP     .L5083BD
.L5083BA:
    MOV     EDI,dword [EBP-05Ch]
.L5083BD:
    TEST    ESI,ESI
    JZ      near .L5085F8
    MOV     EBX,dword [EBP-018h]
    MOV     EDX,dword [EBP-01Ch]
    XOR     EBX,0A5A5A5A5h
    CMP     EDX,EBX
    SBB     EDX,EDX
    NEG     EDX
    MOV     dword [EBP+8],EDX
    MOV     EDX,dword [EBP-01Ch]
    SUB     EDX,EBX
    MOV     EBX,dword [EBP-02Ch]
    MOV     dword [EBP-01Ch],EDX
    MOV     EDX,dword [EBP+8]
    XOR     EBX,0A5A5A5A5h
    ADD     EBX,EDX
    MOV     EDX,dword [EBP-044h]
    SUB     EDX,EBX
    MOV     EBX,dword [EBP-01Ch]
    XOR     EDI,EBX
    MOV     EBX,dword [EBP-048h]
    XOR     EBX,EDX
    MOV     dword [EBP-044h],EDX
    MOV     EDX,dword [EBP-04Ch]
    MOV     dword [EBP-048h],EBX
    ADD     EDX,EDI
    CMP     EDX,EDI
    MOV     dword [EBP-04Ch],EDX
    SBB     EDX,EDX
    NEG     EDX
    ADD     EDX,EBX
    MOV     EBX,dword [EBP-020h]
    ADD     EBX,EDX
    MOV     EDX,EDI
    NOT     EDX
    MOV     dword [EBP-020h],EBX
    MOV     EBX,dword [EBP-04Ch]
    SHL     EDX,013h
    XOR     EDX,EBX
    MOV     EBX,dword [EBP-024h]
    CMP     EBX,EDX
    SBB     EBX,EBX
    NEG     EBX
    MOV     dword [EBP+8],EBX
    MOV     EBX,dword [EBP-024h]
    SUB     EBX,EDX
    MOV     EDX,dword [EBP-048h]
    MOV     dword [EBP-024h],EBX
    MOV     EBX,EDI
    SHL     EDX,013h
    SHR     EBX,0Dh
    OR      EDX,EBX
    MOV     EBX,dword [EBP-020h]
    NOT     EBX
    XOR     EDX,EBX
    MOV     EBX,dword [EBP+8]
    ADD     EDX,EBX
    MOV     EBX,dword [EBP-030h]
    SUB     EBX,EDX
    MOV     EDX,dword [EBP-024h]
    MOV     dword [EBP-030h],EBX
    MOV     EBX,dword [EBP-034h]
    XOR     EBX,EDX
    MOV     EDX,dword [EBP-030h]
    MOV     dword [EBP-034h],EBX
    MOV     EBX,dword [EBP-028h]
    XOR     EBX,EDX
    MOV     EDX,dword [EBP-034h]
    MOV     dword [EBP-028h],EBX
    MOV     EBX,dword [EBP-050h]
    ADD     EBX,EDX
    CMP     EBX,EDX
    MOV     dword [EBP-050h],EBX
    MOV     EBX,dword [EBP-028h]
    SBB     EDX,EDX
    NEG     EDX
    ADD     EDX,EBX
    MOV     EBX,dword [EBP-038h]
    ADD     EBX,EDX
    MOV     EDX,dword [EBP-034h]
    MOV     dword [EBP-038h],EBX
    MOV     EBX,dword [EBP-028h]
    SHR     EDX,017h
    SHL     EBX,9
    OR      EDX,EBX
    MOV     EBX,dword [EBP-050h]
    NOT     EBX
    XOR     EDX,EBX
    MOV     EBX,dword [EBP-03Ch]
    CMP     EBX,EDX
    SBB     EBX,EBX
    NEG     EBX
    MOV     dword [EBP+8],EBX
    MOV     EBX,dword [EBP-03Ch]
    SUB     EBX,EDX
    MOV     EDX,dword [EBP-028h]
    NOT     EDX
    MOV     dword [EBP-03Ch],EBX
    MOV     EBX,dword [EBP-038h]
    SHR     EDX,017h
    XOR     EDX,EBX
    MOV     EBX,dword [EBP+8]
    ADD     EDX,EBX
    MOV     EBX,dword [EBP-040h]
    SUB     EBX,EDX
    MOV     EDX,dword [EBP-03Ch]
    MOV     dword [EBP-040h],EBX
    MOV     EBX,dword [EBP-018h]
    XOR     EBX,EDX
    MOV     EDX,dword [EBP-040h]
    MOV     dword [EBP-018h],EBX
    MOV     EBX,dword [EBP-02Ch]
    XOR     EBX,EDX
    MOV     EDX,dword [EBP-01Ch]
    MOV     dword [EBP-02Ch],EBX
    MOV     EBX,dword [EBP-018h]
    ADD     EDX,EBX
    CMP     EDX,EBX
    MOV     EBX,dword [EBP-02Ch]
    MOV     dword [EBP-01Ch],EDX
    SBB     EDX,EDX
    NEG     EDX
    ADD     EDX,EBX
    MOV     EBX,dword [EBP-044h]
    ADD     EBX,EDX
    MOV     EDX,dword [EBP-018h]
    NOT     EDX
    MOV     dword [EBP-044h],EBX
    MOV     EBX,dword [EBP-01Ch]
    SHL     EDX,013h
    XOR     EDX,EBX
    CMP     EDI,EDX
    SBB     EBX,EBX
    SUB     EDI,EDX
    MOV     EDX,dword [EBP-02Ch]
    MOV     dword [EBP-05Ch],EDI
    NEG     EBX
    MOV     dword [EBP+8],EBX
    MOV     EBX,dword [EBP-018h]
    SHL     EDX,013h
    SHR     EBX,0Dh
    OR      EDX,EBX
    MOV     EBX,dword [EBP-044h]
    NOT     EBX
    XOR     EDX,EBX
    MOV     EBX,dword [EBP+8]
    ADD     EDX,EBX
    MOV     EBX,dword [EBP-048h]
    SUB     EBX,EDX
    MOV     EDX,dword [EBP-04Ch]
    XOR     EDX,EDI
    MOV     EDI,dword [EBP-020h]
    MOV     dword [EBP-048h],EBX
    XOR     EDI,EBX
    MOV     EBX,dword [EBP-024h]
    MOV     dword [EBP-020h],EDI
    ADD     EBX,EDX
    MOV     dword [EBP-04Ch],EDX
    CMP     EBX,EDX
    MOV     dword [EBP-024h],EBX
    SBB     EBX,EBX
    NEG     EBX
    ADD     EBX,EDI
    MOV     EDI,dword [EBP-030h]
    ADD     EDI,EBX
    MOV     dword [EBP-030h],EDI
    MOV     EDI,dword [EBP-020h]
    SHR     EDX,017h
    SHL     EDI,9
    OR      EDX,EDI
    MOV     EDI,dword [EBP-024h]
    NOT     EDI
    XOR     EDX,EDI
    MOV     EDI,dword [EBP-034h]
    CMP     EDI,EDX
    SBB     EBX,EBX
    SUB     EDI,EDX
    MOV     EDX,dword [EBP-020h]
    MOV     dword [EBP-034h],EDI
    NOT     EDX
    SHR     EDX,017h
    XOR     EDX,dword [EBP-030h]
    NEG     EBX
    ADD     EDX,EBX
    MOV     EBX,dword [EBP-028h]
    SUB     EBX,EDX
    MOV     EDX,dword [EBP-050h]
    XOR     EDX,EDI
    MOV     EDI,dword [EBP-038h]
    XOR     EDI,EBX
    MOV     dword [EBP-050h],EDX
    MOV     dword [EBP-038h],EDI
    MOV     EDI,dword [EBP-03Ch]
    ADD     EDI,EDX
    MOV     dword [EBP-028h],EBX
    MOV     EBX,dword [EBP-038h]
    CMP     EDI,EDX
    SBB     EDX,EDX
    MOV     dword [EBP-03Ch],EDI
    NEG     EDX
    ADD     EDX,EBX
    MOV     EBX,dword [EBP-040h]
    ADD     EBX,EDX
    XOR     EDI,089ABCDEFh
    MOV     dword [EBP-040h],EBX
    MOV     EBX,dword [EBP-018h]
    CMP     EBX,EDI
    SBB     EDX,EDX
    SUB     EBX,EDI
    MOV     EDI,dword [EBP-040h]
    MOV     dword [EBP-018h],EBX
    MOV     EBX,dword [EBP-010h]
    XOR     EDI,01234567h
    NEG     EDX
    ADD     EDI,EDX
    MOV     EDX,dword [EBP-02Ch]
    SUB     EDX,EDI
    MOV     dword [EBP-02Ch],EDX
.L5085F8:
    MOV     EDX,dword [EBP-044h]
    MOV     EDI,dword [EBP-01Ch]
    XOR     EBX,EDX
    XOR     EDX,EDX
    MOV     dword [EBP-010h],EBX
    XOR     EAX,EDI
    MOV     DL,byte [EBP-0Eh]
    MOV     dword [EBP+8],EAX
    SHL     EDX,3
    MOV     dword [EBP-014h],EDX
    XOR     EDX,EDX
    MOV     DL,byte [EBP+0Ah]
    MOV     EDI,EBX
    SHL     EDX,3
    MOV     dword [EBP-054h],EDX
    MOV     EDX,EAX
    AND     EDI,0FFh
    AND     EDX,0FFh
    SHL     EDI,3
    SHL     EDX,3
    MOV     EBX,dword [EDI+d06A353Ch]
    MOV     dword [EBP-058h],EDX
    MOV     EDX,dword [EDX+d06A253Ch]
    XOR     EDX,EBX
    MOV     EBX,dword [EBP-054h]
    XOR     EDX,dword [EBX+d06A2D3Ch]
    MOV     EBX,dword [EBP-014h]
    XOR     EDX,dword [EBX+d06A3D3Ch]
    MOV     EBX,dword [EBP-8]
    CMP     EBX,EDX
    SBB     EBX,EBX
    NEG     EBX
    MOV     dword [EBP+8],EBX
    MOV     EBX,dword [EBP-8]
    SUB     EBX,EDX
    MOV     EDX,dword [EBP-058h]
    MOV     dword [EBP-8],EBX
    MOV     EBX,dword [EDI+d06A3540h]
    MOV     EDX,dword [EDX+d06A2540h]
    MOV     EDI,dword [EBP-054h]
    XOR     EDX,EBX
    MOV     EBX,dword [EDI+d06A2D40h]
    MOV     EDI,dword [EBP-014h]
    XOR     EDX,EBX
    MOV     EBX,dword [EDI+d06A3D40h]
    MOV     EDI,dword [EBP+8]
    XOR     EDX,EBX
    MOV     EBX,dword [EBP-4]
    ADD     EDX,EDI
    SUB     EBX,EDX
    MOV     EDX,dword [EBP-010h]
    MOV     dword [EBP-4],EBX
    XOR     EBX,EBX
    MOV     BL,DH
    MOV     EDI,EDX
    MOV     EDX,EAX
    SHL     EBX,3
    SHR     EDX,018h
    MOV     dword [EBP+8],EBX
    XOR     EBX,EBX
    SHR     EDI,018h
    SHL     EDX,3
    SHL     EDI,3
    MOV     dword [EBP-058h],EDX
    MOV     EDX,dword [EDX+d06A353Ch]
    XOR     EDX,dword [EDI+d06A253Ch]
    MOV     BL,AH
    SHL     EBX,3
    MOV     dword [EBP-054h],EBX
    XOR     EDX,dword [EBX+d06A3D3Ch]
    MOV     EBX,dword [EBP+8]
    XOR     EDX,dword [EBX+d06A2D3Ch]
    MOV     EBX,dword [EBP-058h]
    MOV     EBX,dword [EBX+d06A3540h]
    ADD     ECX,EDX
    XOR     EBX,dword [EDI+d06A2540h]
    MOV     EDI,dword [EBP-054h]
    XOR     EBX,dword [EDI+d06A3D40h]
    MOV     EDI,dword [EBP+8]
    XOR     EBX,dword [EDI+d06A2D40h]
    CMP     ECX,EDX
    MOV     EDI,dword [EBP-0Ch]
    SBB     EDX,EDX
    NEG     EDX
    ADD     EDX,EDI
    TEST    ESI,ESI
    LEA     EDI,[EDX+EBX]
    JZ      .L50872B
    MOV     EDX,ESI
    DEC     EDX
    NEG     EDX
    SBB     EDX,EDX
    AND     EDX,2
    ADD     EDX,7
    CMP     EDX,5
    JNZ     .L508742
.L50872B:
    MOV     EDX,ECX
    LEA     EBX,[EDI*4]
    SHR     EDX,01Eh
    OR      EDX,EBX
    LEA     EBX,[ECX*4]
    JMP     .L508790
.L508742:
    CMP     EDX,7
    JNZ     .L50877B
    LEA     EDX,[ECX*8]
    LEA     EBX,[EDI*8]
    CMP     EDX,ECX
    MOV     dword [EBP-014h],EDX
    SBB     EDX,EDX
    NEG     EDX
    MOV     dword [EBP+8],EDX
    MOV     EDX,ECX
    SHR     EDX,01Dh
    OR      EDX,EBX
    MOV     EBX,dword [EBP+8]
    SUB     EDX,EBX
    SUB     EDX,EDI
    MOV     EDI,dword [EBP-014h]
    SUB     EDI,ECX
    MOV     dword [EBP-0Ch],EDX
    MOV     ECX,EDI
    JMP     .L50879F
.L50877B:
    MOV     EDX,ECX
    LEA     EBX,[EDI*8]
    SHR     EDX,01Dh
    OR      EDX,EBX
    LEA     EBX,[ECX*8]
.L508790:
    ADD     ECX,EBX
    CMP     ECX,EBX
    SBB     EBX,EBX
    NEG     EBX
    ADD     EBX,EDX
    ADD     EDI,EBX
    MOV     dword [EBP-0Ch],EDI
.L50879F:
    MOV     EDX,dword [EBP-8]
    MOV     EDI,dword [EBP-05Ch]
    MOV     EBX,dword [EBP-048h]
    XOR     EDX,EDI
    MOV     EDI,dword [EBP-4]
    MOV     dword [EBP-8],EDX
    XOR     EDI,EBX
    XOR     EBX,EBX
    MOV     dword [EBP-4],EDI
    AND     EDX,0FFh
    MOV     BL,byte [EBP-2]
    AND     EDI,0FFh
    SHL     EBX,3
    MOV     dword [EBP+8],EBX
    XOR     EBX,EBX
    MOV     BL,byte [EBP-6]
    SHL     EDX,3
    SHL     EDI,3
    MOV     dword [EBP-058h],EDX
    MOV     EDX,dword [EDX+d06A253Ch]
    XOR     EDX,dword [EDI+d06A353Ch]
    SHL     EBX,3
    MOV     dword [EBP-054h],EBX
    XOR     EDX,dword [EBX+d06A2D3Ch]
    MOV     EBX,dword [EBP+8]
    XOR     EDX,dword [EBX+d06A3D3Ch]
    CMP     ECX,EDX
    SBB     EBX,EBX
    SUB     ECX,EDX
    MOV     EDX,dword [EBP-058h]
    NEG     EBX
    MOV     EDX,dword [EDX+d06A2540h]
    XOR     EDX,dword [EDI+d06A3540h]
    MOV     EDI,dword [EBP-054h]
    XOR     EDX,dword [EDI+d06A2D40h]
    MOV     EDI,dword [EBP+8]
    XOR     EDX,dword [EDI+d06A3D40h]
    MOV     EDI,dword [EBP-0Ch]
    ADD     EDX,EBX
    XOR     EBX,EBX
    SUB     EDI,EDX
    XOR     EDX,EDX
    MOV     DL,byte [EBP-3]
    MOV     dword [EBP-0Ch],EDI
    MOV     EDI,dword [EBP-4]
    SHL     EDX,3
    MOV     dword [EBP+8],EDX
    MOV     EDX,dword [EBP-8]
    MOV     BL,DH
    SHR     EDX,018h
    SHR     EDI,018h
    SHL     EDX,3
    SHL     EDI,3
    MOV     dword [EBP-058h],EDX
    MOV     EDX,dword [EDX+d06A353Ch]
    XOR     EDX,dword [EDI+d06A253Ch]
    SHL     EBX,3
    MOV     dword [EBP-054h],EBX
    XOR     EDX,dword [EBX+d06A3D3Ch]
    MOV     EBX,dword [EBP+8]
    XOR     EDX,dword [EBX+d06A2D3Ch]
    MOV     EBX,dword [EBP-058h]
    MOV     EBX,dword [EBX+d06A3540h]
    ADD     EAX,EDX
    XOR     EBX,dword [EDI+d06A2540h]
    MOV     EDI,dword [EBP-054h]
    XOR     EBX,dword [EDI+d06A3D40h]
    MOV     EDI,dword [EBP+8]
    XOR     EBX,dword [EDI+d06A2D40h]
    MOV     EDI,dword [EBP-010h]
    CMP     EAX,EDX
    SBB     EDX,EDX
    NEG     EDX
    ADD     EDX,EDI
    TEST    ESI,ESI
    LEA     EDI,[EDX+EBX]
    JZ      .L5088BA
    MOV     EDX,ESI
    DEC     EDX
    NEG     EDX
    SBB     EDX,EDX
    AND     EDX,2
    ADD     EDX,7
    CMP     EDX,5
    JNZ     .L5088D1
.L5088BA:
    MOV     EDX,EAX
    LEA     EBX,[EDI*4]
    SHR     EDX,01Eh
    OR      EDX,EBX
    LEA     EBX,[EAX*4]
    JMP     .L50891F
.L5088D1:
    CMP     EDX,7
    JNZ     .L50890A
    LEA     EDX,[EAX*8]
    LEA     EBX,[EDI*8]
    CMP     EDX,EAX
    MOV     dword [EBP-014h],EDX
    SBB     EDX,EDX
    NEG     EDX
    MOV     dword [EBP+8],EDX
    MOV     EDX,EAX
    SHR     EDX,01Dh
    OR      EDX,EBX
    MOV     EBX,dword [EBP+8]
    SUB     EDX,EBX
    SUB     EDX,EDI
    MOV     EDI,dword [EBP-014h]
    SUB     EDI,EAX
    MOV     dword [EBP-010h],EDX
    MOV     EAX,EDI
    JMP     .L50892E
.L50890A:
    MOV     EDX,EAX
    LEA     EBX,[EDI*8]
    SHR     EDX,01Dh
    OR      EDX,EBX
    LEA     EBX,[EAX*8]
.L50891F:
    ADD     EAX,EBX
    CMP     EAX,EBX
    SBB     EBX,EBX
    NEG     EBX
    ADD     EBX,EDX
    ADD     EDI,EBX
    MOV     dword [EBP-010h],EDI
.L50892E:
    MOV     EDI,dword [EBP-04Ch]
    MOV     EDX,dword [EBP-020h]
    XOR     ECX,EDI
    MOV     EDI,dword [EBP-0Ch]
    XOR     EDI,EDX
    XOR     EDX,EDX
    MOV     dword [EBP-0Ch],EDI
    MOV     dword [EBP-058h],ECX
    MOV     DL,byte [EBP-0Ah]
    XOR     EBX,EBX
    SHL     EDX,3
    MOV     BL,byte [EBP-056h]
    MOV     dword [EBP+8],EDX
    MOV     EDX,ECX
    AND     EDI,0FFh
    AND     EDX,0FFh
    SHL     EDX,3
    SHL     EDI,3
    MOV     dword [EBP-058h],EDX
    MOV     EDX,dword [EDX+d06A253Ch]
    XOR     EDX,dword [EDI+d06A353Ch]
    SHL     EBX,3
    MOV     dword [EBP-054h],EBX
    XOR     EDX,dword [EBX+d06A2D3Ch]
    MOV     EBX,dword [EBP+8]
    XOR     EDX,dword [EBX+d06A3D3Ch]
    CMP     EAX,EDX
    SBB     EBX,EBX
    SUB     EAX,EDX
    MOV     EDX,dword [EBP-058h]
    NEG     EBX
    MOV     EDX,dword [EDX+d06A2540h]
    XOR     EDX,dword [EDI+d06A3540h]
    MOV     EDI,dword [EBP-054h]
    XOR     EDX,dword [EDI+d06A2D40h]
    MOV     EDI,dword [EBP+8]
    XOR     EDX,dword [EDI+d06A3D40h]
    ADD     EDX,EBX
    MOV     EBX,dword [EBP-010h]
    SUB     EBX,EDX
    MOV     EDX,dword [EBP-0Ch]
    MOV     dword [EBP-010h],EBX
    XOR     EBX,EBX
    MOV     BL,DH
    MOV     EDI,EDX
    MOV     EDX,ECX
    SHL     EBX,3
    SHR     EDX,018h
    MOV     dword [EBP+8],EBX
    XOR     EBX,EBX
    SHR     EDI,018h
    SHL     EDX,3
    SHL     EDI,3
    MOV     dword [EBP-058h],EDX
    MOV     EDX,dword [EDX+d06A353Ch]
    XOR     EDX,dword [EDI+d06A253Ch]
    MOV     BL,CH
    SHL     EBX,3
    MOV     dword [EBP-054h],EBX
    XOR     EDX,dword [EBX+d06A3D3Ch]
    MOV     EBX,dword [EBP+8]
    XOR     EDX,dword [EBX+d06A2D3Ch]
    MOV     EBX,dword [EBP-8]
    ADD     EBX,EDX
    MOV     dword [EBP-8],EBX
    MOV     EBX,dword [EBP-058h]
    MOV     EBX,dword [EBX+d06A3540h]
    XOR     EBX,dword [EDI+d06A2540h]
    MOV     EDI,dword [EBP-054h]
    XOR     EBX,dword [EDI+d06A3D40h]
    MOV     EDI,dword [EBP+8]
    XOR     EBX,dword [EDI+d06A2D40h]
    MOV     EDI,dword [EBP-8]
    CMP     EDI,EDX
    SBB     EDX,EDX
    NEG     EDX
    ADD     EDX,dword [EBP-4]
    ADD     EBX,EDX
    TEST    ESI,ESI
    MOV     dword [EBP-4],EBX
    JZ      .L508A51
    MOV     EDX,ESI
    DEC     EDX
    NEG     EDX
    SBB     EDX,EDX
    AND     EDX,2
    ADD     EDX,7
    CMP     EDX,5
    JNZ     .L508A64
.L508A51:
    MOV     EDX,EDI
    SHR     EDX,01Eh
    SHL     EBX,2
    OR      EDX,EBX
    LEA     EBX,[EDI*4]
    JMP     .L508AAB
.L508A64:
    CMP     EDX,7
    JNZ     .L508A9A
    LEA     EDX,[EDI*8]
    CMP     EDX,EDI
    MOV     dword [EBP-014h],EDX
    SBB     EDX,EDX
    NEG     EDX
    MOV     dword [EBP+8],EDX
    MOV     EDX,EDI
    SHR     EDX,01Dh
    SHL     EBX,3
    OR      EDX,EBX
    MOV     EBX,dword [EBP+8]
    SUB     EDX,EBX
    MOV     EBX,dword [EBP-4]
    SUB     EDX,EBX
    MOV     EBX,dword [EBP-014h]
    SUB     EBX,EDI
    MOV     dword [EBP-8],EBX
    JMP     .L508ABD
.L508A9A:
    MOV     EDX,EDI
    SHR     EDX,01Dh
    SHL     EBX,3
    OR      EDX,EBX
    LEA     EBX,[EDI*8]
.L508AAB:
    ADD     EDI,EBX
    CMP     EDI,EBX
    MOV     dword [EBP-8],EDI
    SBB     EDI,EDI
    NEG     EDI
    ADD     EDI,EDX
    MOV     EDX,dword [EBP-4]
    ADD     EDX,EDI
.L508ABD:
    MOV     EDI,dword [EBP-024h]
    MOV     dword [EBP-4],EDX
    MOV     EDX,dword [EBP-030h]
    XOR     EAX,EDI
    MOV     EDI,dword [EBP-010h]
    MOV     dword [EBP+8],EAX
    XOR     EDI,EDX
    XOR     EDX,EDX
    MOV     dword [EBP-010h],EDI
    AND     EDI,0FFh
    MOV     DL,byte [EBP-0Eh]
    XOR     EBX,EBX
    SHL     EDX,3
    MOV     dword [EBP-014h],EDX
    MOV     BL,byte [EBP+0Ah]
    MOV     EDX,EAX
    AND     EDX,0FFh
    SHL     EDX,3
    SHL     EDI,3
    MOV     dword [EBP-058h],EDX
    MOV     EDX,dword [EDX+d06A253Ch]
    XOR     EDX,dword [EDI+d06A353Ch]
    SHL     EBX,3
    MOV     dword [EBP-054h],EBX
    XOR     EDX,dword [EBX+d06A2D3Ch]
    MOV     EBX,dword [EBP-014h]
    XOR     EDX,dword [EBX+d06A3D3Ch]
    MOV     EBX,dword [EBP-8]
    CMP     EBX,EDX
    SBB     EBX,EBX
    NEG     EBX
    MOV     dword [EBP+8],EBX
    MOV     EBX,dword [EBP-8]
    SUB     EBX,EDX
    MOV     EDX,dword [EBP-058h]
    MOV     dword [EBP-8],EBX
    MOV     EBX,dword [EDI+d06A3540h]
    MOV     EDX,dword [EDX+d06A2540h]
    MOV     EDI,dword [EBP-054h]
    XOR     EDX,EBX
    MOV     EBX,dword [EDI+d06A2D40h]
    MOV     EDI,dword [EBP-014h]
    XOR     EDX,EBX
    MOV     EBX,dword [EDI+d06A3D40h]
    MOV     EDI,dword [EBP+8]
    XOR     EDX,EBX
    MOV     EBX,dword [EBP-4]
    ADD     EDX,EDI
    SUB     EBX,EDX
    MOV     EDX,dword [EBP-010h]
    MOV     dword [EBP-4],EBX
    XOR     EBX,EBX
    MOV     BL,DH
    MOV     EDI,EDX
    MOV     EDX,EAX
    SHL     EBX,3
    SHR     EDX,018h
    MOV     dword [EBP+8],EBX
    XOR     EBX,EBX
    SHR     EDI,018h
    SHL     EDX,3
    SHL     EDI,3
    MOV     dword [EBP-058h],EDX
    MOV     EDX,dword [EDX+d06A353Ch]
    XOR     EDX,dword [EDI+d06A253Ch]
    MOV     BL,AH
    SHL     EBX,3
    MOV     dword [EBP-054h],EBX
    XOR     EDX,dword [EBX+d06A3D3Ch]
    MOV     EBX,dword [EBP+8]
    XOR     EDX,dword [EBX+d06A2D3Ch]
    MOV     EBX,dword [EBP-058h]
    MOV     EBX,dword [EBX+d06A3540h]
    ADD     ECX,EDX
    XOR     EBX,dword [EDI+d06A2540h]
    MOV     EDI,dword [EBP-054h]
    XOR     EBX,dword [EDI+d06A3D40h]
    MOV     EDI,dword [EBP+8]
    XOR     EBX,dword [EDI+d06A2D40h]
    CMP     ECX,EDX
    SBB     EDX,EDX
    MOV     EDI,dword [EBP-0Ch]
    NEG     EDX
    ADD     EDX,EDI
    TEST    ESI,ESI
    LEA     EDI,[EDX+EBX]
    JZ      .L508BEF
    MOV     EDX,ESI
    DEC     EDX
    NEG     EDX
    SBB     EDX,EDX
    AND     EDX,2
    ADD     EDX,7
    CMP     EDX,5
    JNZ     .L508C06
.L508BEF:
    MOV     EDX,ECX
    LEA     EBX,[EDI*4]
    SHR     EDX,01Eh
    OR      EDX,EBX
    LEA     EBX,[ECX*4]
    JMP     .L508C54
.L508C06:
    CMP     EDX,7
    JNZ     .L508C3F
    LEA     EDX,[ECX*8]
    LEA     EBX,[EDI*8]
    CMP     EDX,ECX
    MOV     dword [EBP-014h],EDX
    SBB     EDX,EDX
    NEG     EDX
    MOV     dword [EBP+8],EDX
    MOV     EDX,ECX
    SHR     EDX,01Dh
    OR      EDX,EBX
    MOV     EBX,dword [EBP+8]
    SUB     EDX,EBX
    SUB     EDX,EDI
    MOV     EDI,dword [EBP-014h]
    SUB     EDI,ECX
    MOV     dword [EBP-0Ch],EDX
    MOV     ECX,EDI
    JMP     .L508C63
.L508C3F:
    MOV     EDX,ECX
    LEA     EBX,[EDI*8]
    SHR     EDX,01Dh
    OR      EDX,EBX
    LEA     EBX,[ECX*8]
.L508C54:
    ADD     ECX,EBX
    CMP     ECX,EBX
    SBB     EBX,EBX
    NEG     EBX
    ADD     EBX,EDX
    ADD     EDI,EBX
    MOV     dword [EBP-0Ch],EDI
.L508C63:
    MOV     EDX,dword [EBP-8]
    MOV     EDI,dword [EBP-034h]
    MOV     EBX,dword [EBP-028h]
    XOR     EDX,EDI
    MOV     EDI,dword [EBP-4]
    MOV     dword [EBP-8],EDX
    XOR     EDI,EBX
    XOR     EBX,EBX
    MOV     dword [EBP-4],EDI
    AND     EDX,0FFh
    MOV     BL,byte [EBP-2]
    AND     EDI,0FFh
    SHL     EBX,3
    MOV     dword [EBP+8],EBX
    XOR     EBX,EBX
    MOV     BL,byte [EBP-6]
    SHL     EDX,3
    SHL     EDI,3
    MOV     dword [EBP-058h],EDX
    MOV     EDX,dword [EDX+d06A253Ch]
    XOR     EDX,dword [EDI+d06A353Ch]
    SHL     EBX,3
    MOV     dword [EBP-054h],EBX
    XOR     EDX,dword [EBX+d06A2D3Ch]
    MOV     EBX,dword [EBP+8]
    XOR     EDX,dword [EBX+d06A3D3Ch]
    CMP     ECX,EDX
    SBB     EBX,EBX
    SUB     ECX,EDX
    MOV     EDX,dword [EBP-058h]
    NEG     EBX
    MOV     EDX,dword [EDX+d06A2540h]
    XOR     EDX,dword [EDI+d06A3540h]
    MOV     EDI,dword [EBP-054h]
    XOR     EDX,dword [EDI+d06A2D40h]
    MOV     EDI,dword [EBP+8]
    XOR     EDX,dword [EDI+d06A3D40h]
    MOV     EDI,dword [EBP-0Ch]
    ADD     EDX,EBX
    XOR     EBX,EBX
    SUB     EDI,EDX
    XOR     EDX,EDX
    MOV     DL,byte [EBP-3]
    MOV     dword [EBP-0Ch],EDI
    MOV     EDI,dword [EBP-4]
    SHL     EDX,3
    MOV     dword [EBP+8],EDX
    MOV     EDX,dword [EBP-8]
    MOV     BL,DH
    SHR     EDX,018h
    SHR     EDI,018h
    SHL     EDX,3
    SHL     EDI,3
    MOV     dword [EBP-058h],EDX
    MOV     EDX,dword [EDX+d06A353Ch]
    XOR     EDX,dword [EDI+d06A253Ch]
    SHL     EBX,3
    MOV     dword [EBP-054h],EBX
    XOR     EDX,dword [EBX+d06A3D3Ch]
    MOV     EBX,dword [EBP+8]
    XOR     EDX,dword [EBX+d06A2D3Ch]
    MOV     EBX,dword [EBP-058h]
    MOV     EBX,dword [EBX+d06A3540h]
    ADD     EAX,EDX
    XOR     EBX,dword [EDI+d06A2540h]
    MOV     EDI,dword [EBP-054h]
    XOR     EBX,dword [EDI+d06A3D40h]
    MOV     EDI,dword [EBP+8]
    XOR     EBX,dword [EDI+d06A2D40h]
    MOV     EDI,dword [EBP-010h]
    CMP     EAX,EDX
    SBB     EDX,EDX
    NEG     EDX
    ADD     EDX,EDI
    TEST    ESI,ESI
    LEA     EDI,[EDX+EBX]
    JZ      .L508D7E
    MOV     EDX,ESI
    DEC     EDX
    NEG     EDX
    SBB     EDX,EDX
    AND     EDX,2
    ADD     EDX,7
    CMP     EDX,5
    JNZ     .L508D95
.L508D7E:
    MOV     EDX,EAX
    LEA     EBX,[EDI*4]
    SHR     EDX,01Eh
    OR      EDX,EBX
    LEA     EBX,[EAX*4]
    JMP     .L508DE3
.L508D95:
    CMP     EDX,7
    JNZ     .L508DCE
    LEA     EDX,[EAX*8]
    LEA     EBX,[EDI*8]
    CMP     EDX,EAX
    MOV     dword [EBP-014h],EDX
    SBB     EDX,EDX
    NEG     EDX
    MOV     dword [EBP+8],EDX
    MOV     EDX,EAX
    SHR     EDX,01Dh
    OR      EDX,EBX
    MOV     EBX,dword [EBP+8]
    SUB     EDX,EBX
    SUB     EDX,EDI
    MOV     EDI,dword [EBP-014h]
    SUB     EDI,EAX
    MOV     dword [EBP-010h],EDX
    MOV     EAX,EDI
    JMP     .L508DF2
.L508DCE:
    MOV     EDX,EAX
    LEA     EBX,[EDI*8]
    SHR     EDX,01Dh
    OR      EDX,EBX
    LEA     EBX,[EAX*8]
.L508DE3:
    ADD     EAX,EBX
    CMP     EAX,EBX
    SBB     EBX,EBX
    NEG     EBX
    ADD     EBX,EDX
    ADD     EDI,EBX
    MOV     dword [EBP-010h],EDI
.L508DF2:
    MOV     EDI,dword [EBP-050h]
    MOV     EDX,dword [EBP-038h]
    XOR     ECX,EDI
    MOV     EDI,dword [EBP-0Ch]
    XOR     EDI,EDX
    XOR     EDX,EDX
    MOV     dword [EBP-0Ch],EDI
    MOV     dword [EBP-058h],ECX
    MOV     DL,byte [EBP-0Ah]
    XOR     EBX,EBX
    SHL     EDX,3
    MOV     BL,byte [EBP-056h]
    MOV     dword [EBP+8],EDX
    MOV     EDX,ECX
    AND     EDI,0FFh
    AND     EDX,0FFh
    SHL     EDX,3
    SHL     EDI,3
    MOV     dword [EBP-058h],EDX
    MOV     EDX,dword [EDX+d06A253Ch]
    XOR     EDX,dword [EDI+d06A353Ch]
    SHL     EBX,3
    MOV     dword [EBP-054h],EBX
    XOR     EDX,dword [EBX+d06A2D3Ch]
    MOV     EBX,dword [EBP+8]
    XOR     EDX,dword [EBX+d06A3D3Ch]
    CMP     EAX,EDX
    SBB     EBX,EBX
    SUB     EAX,EDX
    MOV     EDX,dword [EBP-058h]
    NEG     EBX
    MOV     EDX,dword [EDX+d06A2540h]
    XOR     EDX,dword [EDI+d06A3540h]
    MOV     EDI,dword [EBP-054h]
    XOR     EDX,dword [EDI+d06A2D40h]
    MOV     EDI,dword [EBP+8]
    XOR     EDX,dword [EDI+d06A3D40h]
    ADD     EDX,EBX
    MOV     EBX,dword [EBP-010h]
    SUB     EBX,EDX
    MOV     EDX,dword [EBP-0Ch]
    MOV     dword [EBP-010h],EBX
    XOR     EBX,EBX
    MOV     BL,DH
    MOV     EDI,EDX
    MOV     EDX,ECX
    SHL     EBX,3
    SHR     EDX,018h
    MOV     dword [EBP+8],EBX
    XOR     EBX,EBX
    SHR     EDI,018h
    SHL     EDX,3
    SHL     EDI,3
    MOV     dword [EBP-058h],EDX
    MOV     EDX,dword [EDX+d06A353Ch]
    XOR     EDX,dword [EDI+d06A253Ch]
    MOV     BL,CH
    SHL     EBX,3
    MOV     dword [EBP-054h],EBX
    XOR     EDX,dword [EBX+d06A3D3Ch]
    MOV     EBX,dword [EBP+8]
    XOR     EDX,dword [EBX+d06A2D3Ch]
    MOV     EBX,dword [EBP-8]
    ADD     EBX,EDX
    MOV     dword [EBP-8],EBX
    MOV     EBX,dword [EBP-058h]
    MOV     EBX,dword [EBX+d06A3540h]
    XOR     EBX,dword [EDI+d06A2540h]
    MOV     EDI,dword [EBP-054h]
    XOR     EBX,dword [EDI+d06A3D40h]
    MOV     EDI,dword [EBP+8]
    XOR     EBX,dword [EDI+d06A2D40h]
    MOV     EDI,dword [EBP-8]
    CMP     EDI,EDX
    SBB     EDX,EDX
    NEG     EDX
    ADD     EDX,dword [EBP-4]
    ADD     EBX,EDX
    TEST    ESI,ESI
    MOV     dword [EBP-4],EBX
    JZ      .L508F15
    MOV     EDX,ESI
    DEC     EDX
    NEG     EDX
    SBB     EDX,EDX
    AND     EDX,2
    ADD     EDX,7
    CMP     EDX,5
    JNZ     .L508F28
.L508F15:
    MOV     EDX,EDI
    SHR     EDX,01Eh
    SHL     EBX,2
    OR      EDX,EBX
    LEA     EBX,[EDI*4]
    JMP     .L508F6F
.L508F28:
    CMP     EDX,7
    JNZ     .L508F5E
    LEA     EDX,[EDI*8]
    CMP     EDX,EDI
    MOV     dword [EBP-014h],EDX
    SBB     EDX,EDX
    NEG     EDX
    MOV     dword [EBP+8],EDX
    MOV     EDX,EDI
    SHR     EDX,01Dh
    SHL     EBX,3
    OR      EDX,EBX
    MOV     EBX,dword [EBP+8]
    SUB     EDX,EBX
    MOV     EBX,dword [EBP-4]
    SUB     EDX,EBX
    MOV     EBX,dword [EBP-014h]
    SUB     EBX,EDI
    MOV     dword [EBP-8],EBX
    JMP     .L508F81
.L508F5E:
    MOV     EDX,EDI
    SHR     EDX,01Dh
    SHL     EBX,3
    OR      EDX,EBX
    LEA     EBX,[EDI*8]
.L508F6F:
    ADD     EDI,EBX
    CMP     EDI,EBX
    MOV     dword [EBP-8],EDI
    SBB     EDI,EDI
    NEG     EDI
    ADD     EDI,EDX
    MOV     EDX,dword [EBP-4]
    ADD     EDX,EDI
.L508F81:
    MOV     EDI,dword [EBP-03Ch]
    MOV     dword [EBP-4],EDX
    MOV     EDX,dword [EBP-040h]
    XOR     EAX,EDI
    MOV     EDI,dword [EBP-010h]
    MOV     dword [EBP+8],EAX
    XOR     EDI,EDX
    XOR     EDX,EDX
    MOV     dword [EBP-010h],EDI
    AND     EDI,0FFh
    MOV     DL,byte [EBP-0Eh]
    XOR     EBX,EBX
    SHL     EDX,3
    MOV     dword [EBP-014h],EDX
    MOV     BL,byte [EBP+0Ah]
    MOV     EDX,EAX
    AND     EDX,0FFh
    SHL     EDX,3
    SHL     EDI,3
    MOV     dword [EBP-058h],EDX
    MOV     EDX,dword [EDX+d06A253Ch]
    XOR     EDX,dword [EDI+d06A353Ch]
    SHL     EBX,3
    MOV     dword [EBP-054h],EBX
    XOR     EDX,dword [EBX+d06A2D3Ch]
    MOV     EBX,dword [EBP-014h]
    XOR     EDX,dword [EBX+d06A3D3Ch]
    MOV     EBX,dword [EBP-8]
    CMP     EBX,EDX
    SBB     EBX,EBX
    NEG     EBX
    MOV     dword [EBP+8],EBX
    MOV     EBX,dword [EBP-8]
    SUB     EBX,EDX
    MOV     EDX,dword [EBP-058h]
    MOV     dword [EBP-8],EBX
    MOV     EBX,dword [EDI+d06A3540h]
    MOV     EDX,dword [EDX+d06A2540h]
    MOV     EDI,dword [EBP-054h]
    XOR     EDX,EBX
    MOV     EBX,dword [EDI+d06A2D40h]
    MOV     EDI,dword [EBP-014h]
    XOR     EDX,EBX
    MOV     EBX,dword [EDI+d06A3D40h]
    MOV     EDI,dword [EBP+8]
    XOR     EDX,EBX
    MOV     EBX,dword [EBP-4]
    ADD     EDX,EDI
    SUB     EBX,EDX
    MOV     EDX,dword [EBP-010h]
    MOV     dword [EBP-4],EBX
    XOR     EBX,EBX
    MOV     BL,DH
    MOV     EDI,EDX
    MOV     EDX,EAX
    SHL     EBX,3
    SHR     EDX,018h
    MOV     dword [EBP+8],EBX
    XOR     EBX,EBX
    SHR     EDI,018h
    SHL     EDX,3
    SHL     EDI,3
    MOV     dword [EBP-058h],EDX
    MOV     EDX,dword [EDX+d06A353Ch]
    XOR     EDX,dword [EDI+d06A253Ch]
    MOV     BL,AH
    SHL     EBX,3
    MOV     dword [EBP-054h],EBX
    XOR     EDX,dword [EBX+d06A3D3Ch]
    MOV     EBX,dword [EBP+8]
    XOR     EDX,dword [EBX+d06A2D3Ch]
    MOV     EBX,dword [EBP-058h]
    MOV     EBX,dword [EBX+d06A3540h]
    ADD     ECX,EDX
    XOR     EBX,dword [EDI+d06A2540h]
    MOV     EDI,dword [EBP-054h]
    XOR     EBX,dword [EDI+d06A3D40h]
    MOV     EDI,dword [EBP+8]
    XOR     EBX,dword [EDI+d06A2D40h]
    CMP     ECX,EDX
    SBB     EDX,EDX
    MOV     EDI,dword [EBP-0Ch]
    NEG     EDX
    ADD     EDX,EDI
    TEST    ESI,ESI
    LEA     EDI,[EDX+EBX]
    JZ      .L5090B3
    MOV     EDX,ESI
    DEC     EDX
    NEG     EDX
    SBB     EDX,EDX
    AND     EDX,2
    ADD     EDX,7
    CMP     EDX,5
    JNZ     .L5090CA
.L5090B3:
    MOV     EDX,ECX
    LEA     EBX,[EDI*4]
    SHR     EDX,01Eh
    OR      EDX,EBX
    LEA     EBX,[ECX*4]
    JMP     .L509118
.L5090CA:
    CMP     EDX,7
    JNZ     .L509103
    LEA     EDX,[ECX*8]
    LEA     EBX,[EDI*8]
    CMP     EDX,ECX
    MOV     dword [EBP-014h],EDX
    SBB     EDX,EDX
    NEG     EDX
    MOV     dword [EBP+8],EDX
    MOV     EDX,ECX
    SHR     EDX,01Dh
    OR      EDX,EBX
    MOV     EBX,dword [EBP+8]
    SUB     EDX,EBX
    SUB     EDX,EDI
    MOV     EDI,dword [EBP-014h]
    SUB     EDI,ECX
    MOV     dword [EBP-0Ch],EDX
    MOV     ECX,EDI
    JMP     .L509127
.L509103:
    MOV     EDX,ECX
    LEA     EBX,[EDI*8]
    SHR     EDX,01Dh
    OR      EDX,EBX
    LEA     EBX,[ECX*8]
.L509118:
    ADD     ECX,EBX
    CMP     ECX,EBX
    SBB     EBX,EBX
    NEG     EBX
    ADD     EBX,EDX
    ADD     EDI,EBX
    MOV     dword [EBP-0Ch],EDI
.L509127:
    MOV     EDX,dword [EBP-8]
    MOV     EDI,dword [EBP-018h]
    MOV     EBX,dword [EBP-02Ch]
    XOR     EDX,EDI
    MOV     EDI,dword [EBP-4]
    MOV     dword [EBP-8],EDX
    XOR     EDI,EBX
    XOR     EBX,EBX
    MOV     dword [EBP-4],EDI
    AND     EDX,0FFh
    MOV     BL,byte [EBP-2]
    AND     EDI,0FFh
    SHL     EBX,3
    MOV     dword [EBP+8],EBX
    XOR     EBX,EBX
    MOV     BL,byte [EBP-6]
    SHL     EDX,3
    SHL     EDI,3
    MOV     dword [EBP-058h],EDX
    MOV     EDX,dword [EDX+d06A253Ch]
    XOR     EDX,dword [EDI+d06A353Ch]
    SHL     EBX,3
    MOV     dword [EBP-054h],EBX
    XOR     EDX,dword [EBX+d06A2D3Ch]
    MOV     EBX,dword [EBP+8]
    XOR     EDX,dword [EBX+d06A3D3Ch]
    CMP     ECX,EDX
    SBB     EBX,EBX
    SUB     ECX,EDX
    MOV     EDX,dword [EBP-058h]
    NEG     EBX
    MOV     EDX,dword [EDX+d06A2540h]
    XOR     EDX,dword [EDI+d06A3540h]
    MOV     EDI,dword [EBP-054h]
    XOR     EDX,dword [EDI+d06A2D40h]
    MOV     EDI,dword [EBP+8]
    XOR     EDX,dword [EDI+d06A3D40h]
    MOV     EDI,dword [EBP-0Ch]
    ADD     EDX,EBX
    XOR     EBX,EBX
    SUB     EDI,EDX
    XOR     EDX,EDX
    MOV     DL,byte [EBP-3]
    MOV     dword [EBP-0Ch],EDI
    MOV     EDI,dword [EBP-4]
    SHL     EDX,3
    MOV     dword [EBP+8],EDX
    MOV     EDX,dword [EBP-8]
    MOV     BL,DH
    SHR     EDX,018h
    SHR     EDI,018h
    SHL     EDX,3
    SHL     EDI,3
    MOV     dword [EBP-058h],EDX
    MOV     EDX,dword [EDX+d06A353Ch]
    XOR     EDX,dword [EDI+d06A253Ch]
    SHL     EBX,3
    MOV     dword [EBP-054h],EBX
    XOR     EDX,dword [EBX+d06A3D3Ch]
    MOV     EBX,dword [EBP+8]
    XOR     EDX,dword [EBX+d06A2D3Ch]
    MOV     EBX,dword [EBP-058h]
    MOV     EBX,dword [EBX+d06A3540h]
    ADD     EAX,EDX
    XOR     EBX,dword [EDI+d06A2540h]
    MOV     EDI,dword [EBP-054h]
    XOR     EBX,dword [EDI+d06A3D40h]
    MOV     EDI,dword [EBP+8]
    XOR     EBX,dword [EDI+d06A2D40h]
    MOV     EDI,dword [EBP-010h]
    CMP     EAX,EDX
    SBB     EDX,EDX
    NEG     EDX
    ADD     EDX,EDI
    ADD     EDX,EBX
    TEST    ESI,ESI
    JZ      .L509241
    MOV     EDI,ESI
    DEC     EDI
    NEG     EDI
    SBB     EDI,EDI
    AND     EDI,2
    ADD     EDI,7
    CMP     EDI,5
    JNZ     .L509258
.L509241:
    MOV     EDI,EAX
    LEA     EBX,[EDX*4]
    SHR     EDI,01Eh
    OR      EDI,EBX
    LEA     EBX,[EAX*4]
    JMP     .L5092A5
.L509258:
    CMP     EDI,7
    JNZ     .L509290
    LEA     EDI,[EAX*8]
    LEA     EBX,[EDX*8]
    CMP     EDI,EAX
    MOV     dword [EBP-014h],EDI
    SBB     EDI,EDI
    NEG     EDI
    MOV     dword [EBP+8],EDI
    MOV     EDI,EAX
    SHR     EDI,01Dh
    OR      EDI,EBX
    MOV     EBX,dword [EBP+8]
    SUB     EDI,EBX
    SUB     EDI,EDX
    MOV     EDX,dword [EBP-014h]
    SUB     EDX,EAX
    MOV     EAX,EDX
    MOV     EDX,EDI
    JMP     .L5092B1
.L509290:
    MOV     EDI,EAX
    LEA     EBX,[EDX*8]
    SHR     EDI,01Dh
    OR      EDI,EBX
    LEA     EBX,[EAX*8]
.L5092A5:
    ADD     EAX,EBX
    CMP     EAX,EBX
    SBB     EBX,EBX
    NEG     EBX
    ADD     EBX,EDI
    ADD     EDX,EBX
.L5092B1:
    MOV     EDI,dword [EBP-8]
    MOV     EBX,dword [EBP-0Ch]
    MOV     dword [EBP-8],EAX
    MOV     EAX,ECX
    MOV     ECX,EDI
    MOV     EDI,dword [EBP-4]
    INC     ESI
    MOV     dword [EBP-4],EDX
    CMP     ESI,3
    MOV     dword [EBP-010h],EBX
    MOV     dword [EBP-0Ch],EDI
    JL      .L5083BA
    MOV     ESI,dword [EBP-064h]
    CMP     ECX,ESI
    SBB     EDX,EDX
    SUB     ECX,ESI
    MOV     ESI,dword [EBP-068h]
    NEG     EDX
    SUB     EDI,EDX
    MOV     EDX,dword [EBP-060h]
    SUB     EDI,ESI
    MOV     ESI,dword [EBP-8]
    ADD     EAX,EDX
    MOV     EDX,dword [EBP-06Ch]
    XOR     EDX,ESI
    MOV     ESI,dword [EBP+0Ch]
    MOV     dword [ESI],EDX
    MOV     EDX,dword [EBP-070h]
    XOR     EDX,dword [EBP-4]
    MOV     dword [ESI+8],ECX
    MOV     ECX,dword [EBP-060h]
    MOV     dword [ESI+010h],EAX
    CMP     EAX,ECX
    MOV     dword [ESI+4],EDX
    MOV     EDX,dword [EBP-074h]
    MOV     dword [ESI+0Ch],EDI
    SBB     EAX,EAX
    POP     EDI
    NEG     EAX
    ADD     EAX,EDX
    ADD     EAX,EBX
    MOV     dword [ESI+014h],EAX
    POP     ESI
    POP     EBX
    MOV     ESP,EBP
    POP     EBP
    RET

;############################### sub_509330 ####################################
.sub_509330:
    PUSH    EBP
    MOV     EBP,ESP
    SUB     ESP,044h
    MOV     EAX,dword [EBP+0Ch]
    PUSH    EBX
    PUSH    ESI
    MOV     ESI,dword [EBP+010h]
    CMP     EAX,040h
    PUSH    EDI
    MOV     dword [ESI],089ABCDEFh
    MOV     dword [ESI+4],01234567h
    MOV     dword [ESI+8],076543210h
    MOV     dword [ESI+0Ch],0FEDCBA98h
    MOV     dword [ESI+010h],0C3B2E187h
    MOV     dword [ESI+014h],0F096A5B4h
    MOV     EBX,EAX
    JB      .L509393
    MOV     EDI,dword [EBP+8]
    SHR     EAX,6
    MOV     dword [EBP-4],EAX
.L509378:
    PUSH    ESI
    PUSH    EDI
    CALL    .sub_508310
    MOV     EAX,dword [EBP-4]
    ADD     ESP,8
    ADD     EDI,040h
    SUB     EBX,040h
    DEC     EAX
    MOV     dword [EBP-4],EAX
    JNZ     .L509378
    JMP     .L509396
.L509393:
    MOV     EDI,dword [EBP+8]
.L509396:
    XOR     EAX,EAX
    TEST    EBX,EBX
    JBE     .L5093B4
    MOV     ECX,EBX
    MOV     ESI,EDI
    MOV     EAX,ECX
    LEA     EDI,[EBP-044h]
    SHR     ECX,2
    REP     MOVSB
    MOV     ECX,EAX
    AND     ECX,3
    REP     MOVSB
    MOV     ESI,dword [EBP+010h]
.L5093B4:
    MOV     byte [EBP+EAX-044h],1
    INC     EAX
    TEST    AL,7
    JZ      .L5093C8
.L5093BE:
    MOV     byte [EBP+EAX-044h],0
    INC     EAX
    TEST    AL,7
    JNZ     .L5093BE
.L5093C8:
    CMP     EAX,038h
    JBE     .L5093FE
    CMP     EAX,040h
    JNB     .L5093ED
    MOV     ECX,040h
    LEA     EDI,[EBP+EAX-044h]
    SUB     ECX,EAX
    XOR     EAX,EAX
    MOV     EDX,ECX
    SHR     ECX,2
    REP     STOSD
    MOV     ECX,EDX
    AND     ECX,3
    REP     STOSB
.L5093ED:
    LEA     EAX,[EBP-044h]
    PUSH    ESI
    PUSH    EAX
    CALL    .sub_508310
    ADD     ESP,8
    XOR     EAX,EAX
    JMP     .L509400
.L5093FE:
    JNB     .L50941B
.L509400:
    MOV     ECX,038h
    LEA     EDI,[EBP+EAX-044h]
    SUB     ECX,EAX
    XOR     EAX,EAX
    MOV     EDX,ECX
    SHR     ECX,2
    REP     STOSD
    MOV     ECX,EDX
    AND     ECX,3
    REP     STOSB
.L50941B:
    MOV     EAX,dword [EBP+0Ch]
    LEA     EDX,[EBP-044h]
    PUSH    ESI
    PUSH    EDX
    LEA     ECX,[EAX*8]
    MOV     dword [EBP-8],0
    MOV     dword [EBP-0Ch],ECX
    CALL    .sub_508310
    ADD     ESP,8
    POP     EDI
    POP     ESI
    POP     EBX
    MOV     ESP,EBP
    POP     EBP
    RET

end

