# workflow syntax           https://docs.github.com/en/actions/reference/workflow-syntax-for-github-actions
# runs-on                   https://github.com/actions/virtual-environments
# actions checkout          https://github.com/actions/checkout
# actions setup-python      https://github.com/actions/setup-python
# actions setup-perl        https://github.com/marketplace/actions/setup-perl-environment
# strawberry Perl ********  https://strawberryperl.com/download/********/strawberry-perl-********-portable.zip
# strawberry Perl ********  https://strawberryperl.com/download/********/strawberry-perl-********-32bit-portable.zip
# actions upload-artifact   https://github.com/actions/upload-artifact
# actions download-artifact https://github.com/actions/download-artifact
# actions cache             https://github.com/actions/cache
name: Build XSTools
on:
  push:
    branches:
      - master
    tags-ignore:
      - '*'
  pull_request:

jobs:

#####################
## Windows Actions ##
#####################

  build_XSTools_Windows:

    name: XSTools ${{ matrix.os }} (python ${{ matrix.python }}, perl ${{ matrix.perl }}) ${{ matrix.architecture }}
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        include:
        - os: windows-2019
          python: 2.7.18
          architecture: x86
          python_distr: 'https://www.python.org/ftp/python/2.7.18/python-2.7.18.msi'
          perl: 5.12
          strawberry_distr: 'https://strawberryperl.com/download/********/strawberry-perl-********-portable.zip'

        - os: windows-2022
          python: 3
          architecture: x86
          perl: 5.32
          strawberry_distr: 'https://strawberryperl.com/download/********/strawberry-perl-********-32bit-portable.zip'

    steps:
    - name: GIT checkout
      uses: actions/checkout@v4

    # setup matrix:
    # - windows-2019 + python 2.7.18 x86 + strawberry perl 5.12 x86 + strawberry g++ x86
    # - windows-2022 + python 3      x86 + strawberry perl 5.32 x86 + strawberry g++ x86

    ########################
    # preparing Windows OS #
    ########################

    - name: (Windows 2019) Check the Python2 cache
      if: matrix.os == 'windows-2019'
      id: cache-python2
      uses: actions/cache@v4
      with:
        path: c:\python27
        key: ${{ runner.os }}-python-${{ matrix.python }}

    - name: (Windows 2019) Replace python to ${{ matrix.python }} ${{ matrix.architecture }}
      if: matrix.os == 'windows-2019' && steps.cache-python2.outputs.cache-hit != 'true'
      run: |
        echo "::warning ::cache '${{ runner.os }}-python-${{ matrix.python }}' was NOT found, download the Python2"
        echo "== python version check:"
        python -V
        $python_path = python -c "import os, sys; print(os.path.dirname(sys.executable))"
        echo $python_path
        if ( Test-Path $python_path ) {
          echo '== Remove default Python 3:'
          echo "rm -r $python_path\python.exe"
          rm -r $python_path\python.exe
        }
        echo "================================"
        echo "== download python ${{ matrix.python }} (${{ matrix.architecture }})"
        Invoke-WebRequest ${{ matrix.python_distr }} -OutFile python-2.7.18.msi
        echo "== install python-2.7.18.msi"
        cmd /c start /wait msiexec.exe /passive /i python-2.7.18.msi /norestart /L*V "python_install.log" ADDLOCAL=ALL ALLUSERS=1 TARGETDIR=c:\python27
        echo "== add the path to python27 to the PATH variable"
        echo "c:\python27" | Out-File -FilePath $env:GITHUB_PATH -Encoding utf8 -Append

    - name: (Windows 2022) Setup python ${{ matrix.python }} ${{ matrix.architecture }}
      if: matrix.os == 'windows-2022'
      uses: actions/setup-python@v5
      with:
        python-version: ${{ matrix.python }}
        architecture: ${{ matrix.architecture }}

    - name: Remove default Strawberry perl and g++
      run: |
        if ( "${{ matrix.os }}" -eq "windows-2022" ) {
          echo "== add the path to strawberry to the PATH variable"
          echo "C:\Strawberry\c\bin;C:\Strawberry\perl\site\bin;C:\Strawberry\perl\bin" | Out-File -FilePath $env:GITHUB_PATH -Append
        }
        if ( Test-Path "c:/Strawberry/" ) {
          echo "== remove c:/Strawberry/"
          echo 'mv c:/Strawberry/ c:/Strawberry_old'
          mv c:/Strawberry/ c:/Strawberry_old
        }
        if ( Test-Path "c:/ProgramData/Chocolatey/bin/g++.exe" -PathType leaf ) {
          echo "== remove g++ x64"
          echo 'rm c:/ProgramData/Chocolatey/bin/g++.exe'
          rm c:/ProgramData/Chocolatey/bin/g++.exe
        }
        if ( Test-Path "c:/mingw64/bin/g++.exe" -PathType leaf) {
          echo "== remove mingw64 g++"
          echo 'rm c:/mingw64/bin/g++.exe'
          rm c:/mingw64/bin/g++.exe
        }

    - name: Check the Strawberry cache
      id: cache-strawberry
      uses: actions/cache@v4
      with:
        path: c:\Strawberry
        key: ${{ runner.os }}-strawberry-${{ matrix.perl }}

    - name: (Windows) Replace perl to Strawberry ${{ matrix.perl }} ${{ matrix.architecture }}
      if: steps.cache-strawberry.outputs.cache-hit != 'true'
      run: |
        echo "::warning ::cache '${{ runner.os }}-strawberry-${{ matrix.perl }}' was NOT found, download the Strawberry perl"
        echo "===================================="
        echo "== previous version of Perl:"
        perl --version
        echo "===================================="
        echo "== download Strawberry perl ${{ matrix.perl }} ${{ matrix.architecture }}"
        Invoke-WebRequest ${{ matrix.strawberry_distr }} -OutFile strawberry.zip
        echo "===================================="
        echo "== unpack strawberry-perl ${{ matrix.perl }}"
        7z.exe x strawberry.zip -o"C:/Strawberry"

    ###############################
    # final check of all versions #
    ###############################

    - name: Environment check
      run: |
        echo "== python version check (should be '${{ matrix.python }}'):"
        python -V
        echo "======================"
        echo "== perl version check (should be '${{ matrix.perl }}'):"
        perl --version
        echo "======================"
        echo "== Time::HiRes module:"
        perl -e "use Time::HiRes;"
        echo "========================="
        echo "== Compress::Zlib module:"
        perl -e "use Compress::Zlib;"
        echo "====================="
        echo "== g++ version check:"
        g++ --version

    ####################
    # building XSTools #
    ####################

    - name: make XSTools.dll and NetRedirect.dll
      env:
          OS: ${{ matrix.os }}
          PYTHON: ${{ matrix.python }}
          PERL: ${{ matrix.perl }}
          ARCHITECTURE: ${{ matrix.architecture }}
      run: |
        gmake all
        echo "================================================================="
        echo "DONE:"
        dir src\auto\XSTools\ | findstr "dll"
        echo "These XSTools.dll and NetRedirect.dll are built using: $env:OS + python $env:PYTHON $env:ARCHITECTURE + strawberry perl $env:PERL $env:ARCHITECTURE + strawberry g++ x86" > src\auto\XSTools\XSTools_notes.txt

    ####################
    # making artifacts #
    ####################

    - name: Making artifacts
      uses: actions/upload-artifact@v4
      with:
        name: XSTools_${{ matrix.os }}_perl-${{ matrix.perl }}${{ matrix.architecture }}
        path: |
          src\auto\XSTools\XSTools_notes.txt
          src\auto\XSTools\XSTools.dll
          src\auto\XSTools\NetRedirect.dll


  ########################
  # start the second job #
  ########################

  make_Windows_test:

    name: test ${{ matrix.os }} (perl ${{ matrix.perl }}) ${{ matrix.architecture }}
    needs: build_XSTools_Windows
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        include:
        - os: windows-2019
          architecture: x86
          perl: 5.12

        - os: windows-2022
          architecture: x86
          perl: 5.32

    steps:
    - name: GIT checkout
      uses: actions/checkout@v4

    ########################
    # preparing Windows OS #
    ########################

    - name: Remove default Strawberry perl, XSTools.dll
      if: runner.os == 'Windows'
      run: |
        $OS = "${{ matrix.os }}"
        if ( $OS -eq "windows-2022" ) {
          echo "Add the path to strawberry to the PATH variable"
          echo "C:\Strawberry\c\bin;C:\Strawberry\perl\site\bin;C:\Strawberry\perl\bin" | Out-File -FilePath $env:GITHUB_PATH -Append
          echo "====================="
        }
        if ( Test-Path "c:/Strawberry/" ) {
          echo "== remove c:/Strawberry/"
          echo 'mv c:/Strawberry/ c:/Strawberry_old'
          mv c:/Strawberry/ c:/Strawberry_old
          echo "====================="
        }
        echo "rm XSTools.dll"
        rm XSTools.dll

    - name: Check the Strawberry cache
      id: cache-strawberry
      if: runner.os == 'Windows'
      uses: actions/cache@v4
      with:
        path: c:\Strawberry
        key: ${{ runner.os }}-strawberry-${{ matrix.perl }}

    - name: Display an error if the cache is found
      if: steps.cache-strawberry.outputs.cache-hit != 'true'
      run: |
        echo "::error ::cache '${{ runner.os }}-strawberry-${{ matrix.perl }}' was NOT found!!! See the 'Post Check the Strawberry cache' step in the previous job"
        exit 1

    ###############
    # final check #
    ###############

    - name: Environment check
      run: |
        echo "== perl version check:"
        perl --version

    - name: Restoring XTools from artifacts
      uses: actions/download-artifact@v4
      with:
        name: XSTools_${{ matrix.os }}_perl-${{ matrix.perl }}${{ matrix.architecture }}

    - name: Checking for XSTools
      shell: bash
      run: |
        echo -e "\e[34m# ls -lh | grep XSTools | grep -v notes"
        ls -lh | grep XSTools | grep -v notes

    #############
    # make test #
    #############

    - name: Running tests
      run: |
        cd src\test
        perl unittests.pl

    ###################
    # OpenKore launch #
    ###################

    - name: Start OpenKore
      run: |
        perl ./openkore.pl --version
        if ( $? -eq "True" ) {
          echo "OpenKore started successfully"
        }


###################
## Linux Actions ##
###################

  build_XSTools_Linux:

    name: XSTools ${{ matrix.os }} (python ${{ matrix.python }}, perl ${{ matrix.perl }}) ${{ matrix.architecture }}
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        include:
        - os: ubuntu-22.04
          python: 2.7
          architecture: x64
          perl: 5.12

        - os: ubuntu-latest
          python: 3
          architecture: x64
          perl: latest

    steps:
    - name: GIT checkout
      uses: actions/checkout@v4

    # setup matrix:
    # - ubuntu-22.04          + python 2.7.18 x64 + perl 5.12        x64
    # - ubuntu-latest (24.04) + python 3      x64 + perl last (5.40) x64

    ######################
    # preparing Linux OS #
    ######################

    - name: (${{ matrix.os }}) Setup python ${{ matrix.python }} ${{ matrix.architecture }}
      if: matrix.os == 'ubuntu-22.04'
      run: |
        echo "== python version check:"
        python -V
        echo "== sudo apt-get install python2.7:"
        sudo apt-get install python2.7
        echo "== rename symbolic link"
        sudo rm -f /usr/bin/python
        cd /usr/bin/ && sudo ln -rs python2.7 python
        # ls -l /usr/bin/ |grep python
        echo "== new python version check:"
        python -V

    - name: (${{ matrix.os }}) Setup python ${{ matrix.python }} ${{ matrix.architecture }}
      if: matrix.os == 'ubuntu-latest'
      uses: actions/setup-python@v5
      with:
        python-version: ${{ matrix.python }}
        architecture: ${{ matrix.architecture }}

    - name: Setup perl ${{ matrix.perl }} on linux
      uses: shogo82148/actions-setup-perl@v1
      with:
        perl-version: ${{ matrix.perl }}

    ###############################
    # final check of all versions #
    ###############################

    - name: Environment check
      run: |
        echo "== python version check (should be '${{ matrix.python }}'):"
        python -V
        echo "======================"
        echo "== perl version check (should be '${{ matrix.perl }}'):"
        perl --version
        echo "======================"
        echo "== Time::HiRes module:"
        perl -e "use Time::HiRes;"
        echo "========================="
        echo "== Compress::Zlib module:"
        perl -e "use Compress::Zlib;"
        echo "====================="
        echo "== g++ version check:"
        g++ --version

    ####################
    # building XSTools #
    ####################

    - name: make XSTools.so
      env:
          OS: ${{ matrix.os }}
          PYTHON: ${{ matrix.python }}
          PERL: ${{ matrix.perl }}
          ARCHITECTURE: ${{ matrix.architecture }}
      run: |
        sudo apt-get update
        sudo apt-get install -y libreadline6-dev libcurl4-openssl-dev
        make all
        echo "======================================================"
        echo "DONE:"
        ls -lh src/auto/XSTools/ | grep so
        echo "These XSTools.so and NetRedirect.so are built using: $OS + python $PYTHON $ARCHITECTURE + strawberry perl $PERL $ARCHITECTURE + strawberry g++ x86" > src/auto/XSTools/XSTools_notes.txt

    ####################
    # making artifacts #
    ####################

    - name: Making artifacts
      uses: actions/upload-artifact@v4
      with:
        name: XSTools_${{ matrix.os }}_perl-${{ matrix.perl }}${{ matrix.architecture }}
        path: |
          src/auto/XSTools/XSTools_notes.txt
          src/auto/XSTools/XSTools.so


  ########################
  # start the second job #
  ########################

  make_Linux_test:

    name: test ${{ matrix.os }} (perl ${{ matrix.perl }}) ${{ matrix.architecture }}
    needs: build_XSTools_Linux
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        include:
        - os: ubuntu-22.04
          architecture: x64
          perl: 5.12

        - os: ubuntu-latest
          architecture: x64
          perl: latest

    steps:
    - name: GIT checkout
      uses: actions/checkout@v4

    ######################
    # preparing Linux OS #
    ######################

    - name: Setup perl ${{ matrix.perl }}
      uses: shogo82148/actions-setup-perl@v1
      with:
        perl-version: ${{ matrix.perl }}

    ###############
    # final check #
    ###############

    - name: Environment check
      run: |
        echo "== perl version check:"
        perl --version

    - name: Restoring XTools from artifacts
      uses: actions/download-artifact@v4
      with:
        name: XSTools_${{ matrix.os }}_perl-${{ matrix.perl }}${{ matrix.architecture }}

    - name: Checking for XSTools
      shell: bash
      run: |
        echo -e "\e[34m# ls -lh | grep XSTools | grep -v notes"
        ls -lh | grep XSTools | grep -v notes

    #############
    # make test #
    #############

    - name: Running tests
      run: |
        make test

    ###################
    # OpenKore launch #
    ###################

    - name: Start OpenKore
      run: |
        perl ./openkore.pl --version
        if [[ $? -eq 0 ]]; then
          echo "OpenKore started successfully"
        fi

  #######################
  # start the third job #
  #######################

  make_distrib:

    name: checking makedist.sh
    needs: build_XSTools_Linux
    runs-on: ubuntu-latest

    steps:
    - name: GIT checkout
      uses: actions/checkout@v4

    - name: run "makedist.sh --help"
      shell: bash
      run: ./makedist.sh --help || echo -e "\e[34m# done"

    - name: run "makedist.sh --bin"
      shell: bash
      run: |
        echo "::group::script debug"
          ./makedist.sh --bin
        echo "::endgroup::"
        echo -e "\e[34m# ls -lh | grep zip"
        ls -lh | grep zip

    - name: run "makedist.sh --semibin test_semibin"
      shell: bash
      run: |
        mkdir test_semibin
        echo "::group::script debug"
          ./makedist.sh --semibin test_semibin
        echo "::endgroup::"
        echo -e "\e[34m# ls -lh | grep test_semibin"
        ls -lh | grep test_semibin

    - name: run "makedist.sh --unknown"
      shell: bash
      run: ./makedist.sh --unknown || echo -e "\e[34m# done"
