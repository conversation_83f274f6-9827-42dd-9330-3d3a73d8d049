#!/usr/bin/perl
# Quick Checksum Algorithm Test for GNJOY LATAM
use strict;
use warnings;

print "QUICK CHECKSUM TEST - GNJOY LATAM\n";
print "="x40 . "\n";

# Test the exact algorithm from ROla.pm
sub test_checksum {
    my ($packet_data, $seed_number) = @_;
    
    my %real_seed_map = (
        1 => 0x1F,  # ✅ Confirmado: 7D:00 -> checksum 0x62
        2 => 0x2C,  # ✅ Confirmado: 60:03:3D:34:2C:05 -> checksum 0x6F  
        3 => 0xD2,  # ✅ Confirmado: C9:08 -> checksum 0x13
        4 => 0xCB,  # ✅ Confirmado: 47:04 -> checksum 0x88
        5 => 0xDE,  # ✅ Confirmado: 7C:09:01:00 -> checksum 0xAA
    );
    
    my $seed_value = $real_seed_map{$seed_number} || 0x1F;
    
    my $checksum = $seed_value;
    for my $byte (unpack('C*', $packet_data)) {
        $checksum ^= $byte;
    }
    
    return $checksum & 0xFF;
}

# Test known working cases
my @tests = (
    { seed => 1, data => pack("C*", 0x7D, 0x00), expected => 0x62 },
    { seed => 2, data => pack("C*", 0x60, 0x03, 0x3D, 0x34, 0x2C, 0x05), expected => 0x6F },
    { seed => 3, data => pack("C*", 0xC9, 0x08), expected => 0x13 },
    { seed => 4, data => pack("C*", 0x47, 0x04), expected => 0x88 },
    { seed => 5, data => pack("C*", 0x7C, 0x09, 0x01, 0x00), expected => 0xAA },
);

my $all_passed = 1;

foreach my $test (@tests) {
    my $result = test_checksum($test->{data}, $test->{seed});
    my $passed = ($result == $test->{expected});
    
    printf "Seed %d: Expected 0x%02X, Got 0x%02X - %s\n", 
           $test->{seed}, $test->{expected}, $result, 
           $passed ? "✅ PASS" : "❌ FAIL";
    
    $all_passed = 0 unless $passed;
}

print "\n" . "="x40 . "\n";
if ($all_passed) {
    print "✅ ALL TESTS PASSED - Algorithm is working correctly\n";
    print "Issue is likely in packet structure or server communication\n";
} else {
    print "❌ ALGORITHM TESTS FAILED - Check implementation\n";
    print "Fix the calculateChecksum function in ROla.pm\n";
}
print "="x40 . "\n";

# Test with typical movement data
print "\nTesting typical movement packet:\n";
my $move_data = pack("C*", 0x12, 0x34, 0x56);  # Typical coordinates
my $move_checksum = test_checksum($move_data, 1);
printf "Movement data [12 34 56] with seed 1: checksum = 0x%02X\n", $move_checksum;

print "\nRun this test first, then paste OpenKore logs for analysis.\n";
