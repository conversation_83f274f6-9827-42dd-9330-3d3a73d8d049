# Configuração Final - OTP Token Handler Implementado
# Agora com handler completo de OTP token

######## Login Settings ########
master Latam - R<PERSON><PERSON>: <PERSON><PERSON>/Nidhogg/Yggdrasil
server 1
username christiano<PERSON><PERSON><PERSON>@gmail.com
password Chris2006@
loginPinCode 0103
char 0

# XKore 2 mode
XKore 2
XKore_port 6901
XKore_exeName ragexe.exe

######## DEBUG PARA OTP TOKEN E CHECKSUM ########
debug 2
verboseLog 1
debugPacket_sent 2
debugPacket_received 2
debugPacket_include_dumpMethod 2

# Focar nos pacotes importantes
debugPacket_include 0825,0AE3,0081,0C32,0436,0085,0437

# Log to files
logToFile 1
logToFile_Packet 1
logToFile_Debug 1

######## AI SETTINGS ########
ai_manual 1
attackAuto 0
route_randomWalk 0
moveStepDelay 2000
attackDelay 2000

# Timeouts
timeout 30
timeout_ex 60

######## LOGS ESPERADOS AGORA ########
# ✅ "Received OTP token from server 'S1000', processing..."
# ✅ "Token data length: XXX bytes"
# ✅ "OTP token sent back to server successfully"
# ✅ Login completo sem erro 0x0081
# ✅ Conexão ao map server
# ✅ [ROla] REAL XOR debug messages
# ✅ Seeds incrementando (1, 2, 3, 4, 5...)

######## COMANDOS DE TESTE APÓS LOGIN ########
# move 100 150    # Movimento (seed 1)
# move 120 130    # Movimento (seed 2)
# sit             # Ação (seed 3)
# stand           # Ação (seed 4)
# a               # Atacar (seed 5)

######## SINAIS DE SUCESSO TOTAL ########
# ✅ OTP token processado corretamente
# ✅ Login completo sem loops
# ✅ Map server conectado
# ✅ Comandos de movimento funcionando
# ✅ Checksums calculados corretamente
# ✅ Bot totalmente funcional

######## SINAIS DE PROBLEMA ########
# ❌ "Received OTP token..." não aparece
# ❌ Ainda recebendo packet 0x0081
# ❌ Loop infinito de login
# ❌ Desconexão após movimento
