# Configuração de Debug para Login GNJOY LATAM
# Use esta configuração para diagnosticar problemas de login

######## Login Settings ########
master Latam - RO<PERSON>: <PERSON><PERSON>/Nidhogg/Yggdrasil
server 1
username christ<PERSON><PERSON><PERSON><PERSON>@gmail.com
password Chris2006@
loginPinCode 0103
char 0

# XKore 2 mode
XKore 2
XKore_port 6901
XKore_exeName ragexe.exe

######## MAXIMUM DEBUG FOR LOGIN DIAGNOSIS ########
debug 3
verboseLog 1
debugPacket_sent 2
debugPacket_received 2
debugPacket_include_dumpMethod 2

# Log ALL packets during login
debugPacket_include 0825,0081,0069,006A,01DD,01FA,0064,0065,0066,0067

# Log to files
logToFile 1
logToFile_Packet 1
logToFile_Debug 1

######## Connection Settings ########
timeout 60
timeout_ex 120
dcOnMaxReconnections 0
dcOnServerClose 0

# Disable AI during login testing
ai_manual 1
attackAuto 0
route_randomWalk 0

######## INSTRUÇÕES DE TESTE ########
# 1. Salve este arquivo como control/config.txt
# 2. Execute: perl openkore.pl
# 3. Monitore logs para:
#    - Packet 0x0825 sendo enviado
#    - Packet 0x0081 (error) sendo recebido
#    - Mensagens de erro específicas
# 4. Se login funcionar, teste movimento: "move 100 150"

######## SINAIS DE SUCESSO ########
# ✅ Login bem-sucedido sem packet 0x0081
# ✅ Carregamento do mapa
# ✅ Mensagem "You are now in the game"
# ✅ Possibilidade de enviar comandos de movimento

######## SINAIS DE PROBLEMA ########
# ❌ Packet 0x0081 (error) recebido repetidamente
# ❌ Loop infinito de login packets 0x0825
# ❌ Disconnecting (172.65.200.86:6900)...disconnected
# ❌ Mensagens de "Invalid credentials" ou similar
