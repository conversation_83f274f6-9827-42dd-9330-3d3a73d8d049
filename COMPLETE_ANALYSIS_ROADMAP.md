# Complete GNJOY LATAM Checksum Analysis Roadmap

## 🎯 Mission Overview
Extract the exact checksum algorithm from Ragexe.exe (30/05/2025) to enable OpenKore bot functionality on GNJOY LATAM servers without disconnections.

## 📚 Your Complete Toolkit

### 📖 **Analysis Framework** (Your Document)
- ✅ **Comprehensive analysis plan** with specific objectives
- ✅ **Structured approach** for systematic reverse engineering
- ✅ **Clear success criteria** and validation requirements

### 🛠️ **Practical Guides** (My Contributions)
1. **`IDA_PRO_BEGINNER_GUIDE.md`** - Complete beginner tutorial
2. **`IDA_PRO_PRACTICAL_WALKTHROUGH.md`** - Hands-on step-by-step guide
3. **`IDA_PRO_CHEAT_SHEET.md`** - Quick reference for hotkeys and patterns
4. **`IDA_PRO_ANALYSIS_EXECUTION_GUIDE.md`** - Execution of your analysis plan

### 🧪 **Testing Tools**
1. **`checksum_algorithm_tester.pl`** - Test different algorithms as you find them
2. **`test_checksum.pl`** - Original algorithm testing script
3. **`config_test_checksum.txt`** - OpenKore testing configuration

### 🔧 **Implementation Ready**
1. **`src/Network/Send/ROla.pm`** - Enhanced with checksum framework
2. **Placeholder algorithm** ready for your extracted code
3. **Debug logging** enabled for validation

## 🗺️ Step-by-Step Execution Roadmap

### **Phase 1: Preparation (15 minutes)**
```
✅ Load Ragexe.exe in IDA Pro
✅ Wait for auto-analysis completion ("AU: idle")
✅ Set up optimal window layout
✅ Print IDA_PRO_CHEAT_SHEET.md for reference
```

### **Phase 2: Packet Function Discovery (30-60 minutes)**
```
🔍 Search Strategy:
1. Alt + B → Search "85 00" (movement packet)
2. Alt + B → Search "37 04" (action packet)  
3. Shift + F4 → Search "send" in Names window
4. Ctrl + X → Find cross-references to send functions

📝 Document:
- Function addresses where packets are built
- Assembly code for packet construction
- Location of send/WSASend calls
```

### **Phase 3: Checksum Algorithm Extraction (60-120 minutes)**
```
🔍 Search Strategy:
1. Look for loops near packet construction
2. Search for GetTickCount API calls
3. Find account ID global variables
4. Identify mathematical operations

📝 Document:
- Complete assembly code of checksum function
- All variables: account ID, tick, packet data
- Constants and magic numbers used
- Step-by-step algorithm flow
```

### **Phase 4: Algorithm Testing (30 minutes)**
```
🧪 Testing Process:
1. Implement algorithm in checksum_algorithm_tester.pl
2. Test with sample packet data
3. Compare different algorithm variations
4. Validate output format (32-bit checksum)
```

### **Phase 5: OpenKore Integration (15 minutes)**
```
🔧 Implementation:
1. Replace calculateChecksum() in ROla.pm
2. Test with config_test_checksum.txt
3. Monitor debug logs for checksum values
4. Verify no server disconnections
```

## 🎯 Critical Success Patterns

### **What You're Looking For:**

**Packet Construction Pattern:**
```assembly
mov word ptr [esp+4], 85h     ; Packet ID (0x0085)
mov [esp+6], al               ; Coordinate data
; ... more packet data ...
call checksum_function        ; Calculate checksum
mov [esp+N], eax             ; Append checksum
call send                    ; Send packet
```

**Checksum Calculation Pattern:**
```assembly
xor eax, eax                 ; checksum = 0
mov ecx, packet_length       ; loop counter
add al, [packet_data+ecx]    ; accumulate bytes
loop previous_instruction    ; repeat
add eax, [account_id]        ; add account ID
add eax, [tick_count]        ; add tick count
```

## 🚨 Common Challenges & Solutions

### **Challenge 1: Can't Find Packet IDs**
**Solutions:**
- Try searching in different memory segments
- Look for decimal values (133 for 0x85, 1079 for 0x437)
- Search for string references to packet numbers

### **Challenge 2: Multiple Similar Functions**
**Solutions:**
- Look for the most recent/complex implementation
- Check which function handles both packet types
- Trace from actual gameplay triggers

### **Challenge 3: Complex Assembly Code**
**Solutions:**
- Focus on overall patterns, not individual instructions
- Use the provided assembly instruction reference
- Document step-by-step, don't try to understand everything at once

## 📊 Progress Tracking Checklist

### **Discovery Phase:**
- [ ] Found movement packet (0x0085) construction function
- [ ] Found action packet (0x0437) construction function  
- [ ] Located send/WSASend calls
- [ ] Identified packet buffer locations

### **Algorithm Phase:**
- [ ] Found checksum calculation function
- [ ] Identified account ID usage
- [ ] Found tick count source (GetTickCount)
- [ ] Documented all mathematical operations
- [ ] Extracted complete algorithm

### **Implementation Phase:**
- [ ] Converted assembly to pseudocode
- [ ] Tested algorithm with sample data
- [ ] Implemented in ROla.pm
- [ ] Tested with GNJOY LATAM server
- [ ] Verified no disconnections

## 🎉 Success Validation

### **You've Succeeded When:**
1. ✅ **Bot connects** to GNJOY LATAM server
2. ✅ **Movement works** without disconnection (`move X Y` command)
3. ✅ **Actions work** without disconnection (`a` to attack)
4. ✅ **Full bot functionality** restored
5. ✅ **Algorithm documented** for future reference

### **Expected Timeline:**
- **Beginner**: 4-6 hours total
- **Experienced**: 2-3 hours total
- **Expert**: 1-2 hours total

## 🆘 Getting Help

### **If You Get Stuck:**
1. **Take screenshots** of your IDA Pro findings
2. **Document exact steps** you've taken
3. **Share function addresses** and assembly code
4. **Describe specific error messages** or unexpected behavior

### **Support Resources:**
- All provided guides and cheat sheets
- Testing tools for algorithm validation
- Pre-built OpenKore framework ready for integration

## 🚀 Final Notes

**You Have Everything Needed:**
- ✅ Comprehensive analysis framework
- ✅ Step-by-step practical guides  
- ✅ Testing and validation tools
- ✅ Ready-to-use OpenKore implementation

**The Path to Success:**
```
IDA Pro Analysis → Algorithm Extraction → Testing → OpenKore Integration → Working Bot
```

**Remember:** This is a systematic process. Follow the guides step-by-step, document your findings, and test thoroughly. The framework is complete - you just need to extract that one algorithm!

**Good luck with your reverse engineering! You're very close to success! 🔍🎯**
