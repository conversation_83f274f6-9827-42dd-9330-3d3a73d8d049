# Debug Configuration for Error 5011 Analysis
# Focus on credential validation and account status

######## Login Settings ########
master Latam - RO<PERSON>: <PERSON><PERSON>/Nidhogg/Yggdrasil
server 1
username christiano<PERSON><PERSON><EMAIL>
password Chris2006@
loginPinCode 0103
char 0

# XKore 2 mode
XKore 2
XKore_port 6901
XKore_exeName ragexe.exe

######## MAXIMUM DEBUG FOR ERROR 5011 ########
debug 3
verboseLog 1
debugPacket_sent 2
debugPacket_received 2
debugPacket_include_dumpMethod 2

# Focus on authentication packets
debugPacket_include 0825,0AE3,083E,006A,0081,0C32,0436

# Log everything for analysis
logToFile 1
logToFile_Packet 1
logToFile_Debug 1

######## CONNECTION SETTINGS ########
timeout 60
timeout_ex 120
dcOnMaxReconnections 0
dcOnServerClose 0

# AI settings
ai_manual 1
attackAuto 0
route_randomWalk 0

######## ERROR 5011 ANALYSIS POINTS ########
# Look for these in logs:
# 1. "OTP token sent back to server successfully" ✅
# 2. Packet 0825 with 431 bytes ✅
# 3. Packet 083E with error code 5011 ❌
# 4. Any credential encoding messages
# 5. Account validation errors

######## TROUBLESHOOTING STEPS ########
# 1. Test with official client first
# 2. Verify account is not banned/suspended
# 3. Check if account is already logged in
# 4. Try different server (Freya vs Nidhogg)
# 5. Verify account region/type compatibility

######## IDA PRO TARGETS ########
# Search for:
# - "083E" or "3E 08" (packet ID)
# - "5011" or "93 13" (error code)
# - "account", "validate", "credential"
# - Functions that check account status
# - Session token validation routines

######## EXPECTED OUTCOMES ########
# If account is valid:
# ✅ Should progress past 083E error
# ✅ Should receive character list
# ✅ Should connect to map server
# 
# If account has issues:
# ❌ Will continue getting 083E (5011)
# ❌ Need to fix account status first
# ❌ May need different credentials
