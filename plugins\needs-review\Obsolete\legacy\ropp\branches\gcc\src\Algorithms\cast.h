/*
 *	CAST-128 in C
 *	Written by <PERSON> <<EMAIL>>
 *	100% Public Domain - no warranty
 *	Released 1997.10.11
 */
#ifndef _CAST_H_
#define _CAST_H_

#include "../typedefs.h"

typedef struct {
	dword xkey[32];				// Key, after expansion
	int rounds;					// Number of rounds to use, 12 or 16
} cast_key;

CEXTERN void cast_setkey(cast_key* key, byte* rawkey, int keybytes);
CEXTERN void cast_encrypt(cast_key* key, byte* inblock, byte* outblock);
CEXTERN void cast_decrypt(cast_key* key, byte* inblock, byte* outblock);

#endif /* _CAST_H_ */

