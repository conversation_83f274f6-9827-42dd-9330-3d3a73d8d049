# Ragnarok Server
# Here you'll define the IP Address and the Port where <PERSON><PERSON><PERSON>
# will keep waiting for your ragnarok online client to connect.
ragnarokserver_ip=127.0.0.1
ragnarokserver_port=6900

# Query Server
# Here you'll define the IP Address and the Port where <PERSON>seidon
# will keep waiting for open kore to connect and send the GG/HS queries.
queryserver_ip=127.0.0.1
queryserver_port=24390

# Server Type
# Here you have to specify your current server type in order
# to the poseidon operate properly !
# The available server types for now are : Default, bRO_.* (check servertypes.txt)
# You should modify this if you're having problems with char list.
server_type=Default

debug=0
