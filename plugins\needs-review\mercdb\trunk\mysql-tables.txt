CREATE TABLE `shopcont` (
  `id` int(11) NOT NULL auto_increment,
  `shopOwnerID` varchar(20) NOT NULL default '0',
  `shopOwner` varchar(254) NOT NULL default '',
  `shopName` varchar(30) NOT NULL default '',
  `itemID` varchar(20) NOT NULL default '0',
  `name` varchar(254) NOT NULL default '',
  `amount` int(6) NOT NULL default '0',
  `typus` varchar(254) NOT NULL default '',
  `identified` tinyint(3) NOT NULL default '0',
  `custom` tinyint(3) NOT NULL default '0',
  `broken` tinyint(3) NOT NULL default '0',
  `slots` int(4) NOT NULL default '0',
  `card1ID` varchar(20) NOT NULL default '0',
  `card1` varchar(254) NOT NULL default '',
  `card2ID` varchar(20) NOT NULL default '0',
  `card2` varchar(254) NOT NULL default '',
  `card3ID` varchar(20) NOT NULL default '0',
  `card3` varchar(254) NOT NULL default '',
  `card4ID` varchar(20) NOT NULL default '0',
  `card4` varchar(254) NOT NULL default '',
  `crafted_by` varchar(30) NOT NULL default '',
  `element` varchar(5) NOT NULL default '',
  `star_crumb` char(2) NOT NULL default '',
  `price` bigint(12) NOT NULL default '0',
  `time` varchar(50) NOT NULL default '0',
  `posx` int(4) NOT NULL default '0',
  `posy` int(4) NOT NULL default '0',
  `datum` varchar(30) NOT NULL default '',
  `map` varchar(254) NOT NULL default '',
  `server` varchar(40) NOT NULL default 'Chaos',
  UNIQUE KEY `id` (`id`)
) ENGINE=MyISAM COMMENT='RO Shop Database';
    

# --------------------------------------------------------


CREATE TABLE `shopvisit` (
  `id` int(11) NOT NULL auto_increment,
  `shopOwnerID` varchar(20) NOT NULL default '0',
  `time` varchar(50) NOT NULL default '0',
  `server` varchar(40) NOT NULL default 'Chaos',
  UNIQUE KEY `id` (`id`),
  KEY `shopOwnerID` (`shopOwnerID`)
) ENGINE=MyISAM;
