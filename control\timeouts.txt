# You shouldn't need to modify these variables
# If you want to really mess up the bot, go ahead :)
#
# The value of each variable is reffered as "x".

# Server connection timeouts
master 12
gamelogin 12
charlogin 12
maplogin 12
play 40
# When disconnected, wait x seconds before reconnecting again
reconnect 10

# After repeated disconnects, wait longer before reconnecting, to reduce load on the server.
# The first value in this list overrides the "reconnect" timeout above.
reconnect_backoff 

# Add a random amount of seconds to reconnect time, up to a maximum of reconnect_random seconds.
reconnect_random 20

# Wait x seconds for a poseidon reply before disconnecting
# Ignore this if you don't use Poseidon Server
poseidon_wait_reply 15

# Activate AI after x seconds after the map's loaded
ai 1

ai_move_retry 0.25
ai_move_giveup 1

ai_homunculus_standby 0.5
ai_mercenary_standby 0.5

# Send the attack packet every x seconds, if it hasn't been send already
ai_attack 1
ai_homunculus_attack 1
ai_mercenary_attack 1

ai_attack_after_skill 0.5

ai_homunculus_dance_attack_melee 0.2

ai_mercenary_dance_attack_melee 0.2
ai_mercenary_dance_attack_ranged 0.2

# Check for monsters to attack every x seconds
ai_attack_auto 0.5
ai_homunculus_attack_auto 0.5
ai_mercenary_attack_auto 0.5

ai_attack_route_adjust 0.3
ai_homunculus_route_adjust 0.3
ai_mercenary_route_adjust 0.3

# Ignore monster for x seconds if there is no route to it
ai_attack_failedLOS 12

# Give up attacking a monster if it can't be reached within x seconds
ai_attack_giveup 6

ai_homunculus_check_monster_auto 0.2
ai_mercenary_check_monster_auto 0.2

# If you've just killed a monster, and there are no aggressives,
# and you're not picking up any items, wait x seconds before doing
# anything else.
ai_attack_waitAfterKill 0.3
ai_homunculus_attack_waitAfterKill 0.3
ai_mercenary_attack_waitAfterKill 0.3

# Every x seconds loop the attack logic routine (send move, attack, skill, avoid, etc)
ai_attack_main 0.1
ai_homunculus_attack_main 0.1
ai_mercenary_attack_main 0.1

ai_attack_unstuck 2.75
ai_attack_unfail 12

ai_route_unstuck 2

# Pause for the specified number of seconds after taking something,
# it increases the interval between taking several items and between
# taking the last item and continuing the actions.
ai_items_take_delay 1

# When your monster died, start checking for loot after x seconds
ai_items_take_start 0.3

# Stop checking for loot x seconds after it has begun checking
ai_items_take_end 0.6

# When standing near an item, send the 'take' packet every x seconds until
# the item has been taken.
ai_take .4

# Give up if unable to pickup item after x seconds
ai_take_giveup 3
ai_items_gather_giveup 3

# Every x seconds, check items for gathering
ai_items_gather_auto .3

# Only gather items that have been more than x seconds on screen
ai_items_gather_start .3

# Delay between items when tranferring in batches.
ai_transfer_items 0.15

# Delay between map_loaded and send ignoreAll command
ai_ignoreAll 3

ai_follow_lost_end 10
ai_getInfo 2
ai_thanks_set 8
ai_dealAuto 3
ai_dealAutoCancel 5
ai_partyAutoDeny 3
ai_guildAutoDeny 3
ai_dead_respawn 4
ai_wipe_old 200
ai_wipe_check 30

# Every x seconds, check party share settings
ai_partyShareCheck 60
# Every x seconds, check pet hungry for feeding
ai_petFeed 60
# Every x seconds, check homun hungry for feeding
ai_homunFeed 60

# Send the sit/stand packet at most every x second
ai_sit 1
# Sit after having idled for x seconds
ai_sit_idle 10
# Stand after x seconds, after having typed the 'stand' command
ai_stand_wait 0
# Sit after x seconds, after having typed the "sit" command
ai_sit_wait 0
# If there are non-party players around and you are to stand up due to the
# hp/sp sufficiency, do so when the specified number of seconds elapsed
ai_safe_stand_up 2

ai_skill_use 0.75
ai_skill_use_giveup 1
ai_item_use_auto 0.5
ai_item_equip_auto 0.75
ai_equipAuto_skilluse_giveup 5
ai_equip_giveup 2

ai_teleport 1
ai_teleport_away 3
ai_teleport_idle 4
ai_teleport_portal 2
ai_teleport_hp 3
ai_teleport_safe_force 120

ai_teleport_retry 0.5
ai_teleport_delay 0.5

ai_portal_wait 0.5

# These timeouts are used in missing portals logic
ai_portal_give_up 10
ai_portal_re_add_missed 3600

# You probably don't ever have to change the following timeouts
ai_route_calcRoute 1
ai_route_npcTalk 10

# These timeouts are used in npc conversation (Task::TalkNPC)
ai_npc_talk_wait_after_close_to_cancel 0.5
ai_npc_talk_wait_after_cancel_to_destroy 0.5

ai_buyAuto 5
ai_buyAuto_wait 2
ai_buyAuto_wait_giveup_npc 15
ai_buyAuto_wait_before_buy 2
ai_buyAuto_wait_after_packet_giveup 15
ai_buyAuto_wait_after_restart 2

ai_sellAuto 2
ai_sellAuto_wait_giveup_npc 15
ai_sellAuto_wait_before_sell 2
ai_sellAuto_wait_after_packet_giveup 15

ai_storageAuto 2
ai_storageAuto_giveup 15
ai_storageAuto_useItem 2
ai_storageAuto_wait_before_action 2
# delay between sending cart item add/get packets
ai_cartAuto 0.15

# delay between checking if we need to do any cartAuto functions
ai_cartAutoCheck 3
ai_avoidcheck 0.5
ai_shop 4
ai_shop_useskill_delay 5
ai_buyer_shopCheck 60

# delay between repairAuto
ai_repair 4

# delay before starting escape sequence
ai_route_escape 8

# Don't change the following timeouts!
ai_sync 12

injectSync 5
injectKeepAlive 12
welcomeText 4
patchserver 120

# Time to wait before load map in xKore mode
ai_clientSuspend 1