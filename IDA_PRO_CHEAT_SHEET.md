# IDA Pro Quick Reference Cheat Sheet

## 🔥 Essential Hotkeys

```
┌─────────────────────────────────────────────────────────────┐
│                    IDA PRO HOTKEYS                          │
├─────────────────────────────────────────────────────────────┤
│ Ctrl + F    │ Search text in current window                 │
│ Alt + B     │ Binary search (hex bytes)                     │
│ Alt + T     │ Search text/strings                           │
│ Alt + I     │ Search immediate values (constants)           │
│ Shift + F4  │ Open Names window (imports)                   │
│ Ctrl + X    │ Cross-references (where is this used?)        │
│ Ctrl + P    │ Go to function start                          │
│ F3          │ Find next occurrence                          │
│ G           │ Go to address                                 │
│ N           │ Rename variable/function                      │
│ ;           │ Add comment                                   │
│ Space       │ Switch between graph/text view               │
│ Esc         │ Go back to previous location                  │
└─────────────────────────────────────────────────────────────┘
```

## 🎯 Search Strategy Flowchart

```
START: Looking for GNJOY LATAM Checksum
│
├─ Step 1: Search for Packet IDs
│  ├─ Alt + B → "85 00" (movement)
│  └─ Alt + B → "37 04" (action)
│
├─ Step 2: Find Network Functions  
│  ├─ Shift + F4 → Search "send"
│  └─ Ctrl + X on send function
│
├─ Step 3: Locate Checksum Code
│  ├─ Look for loops near packet construction
│  ├─ Search for GetTickCount calls
│  └─ Look for mathematical operations
│
└─ Step 4: Extract Algorithm
   ├─ Document assembly instructions
   ├─ Convert to C pseudocode
   └─ Implement in Perl
```

## 🔍 Visual Pattern Recognition

### Packet Construction Patterns
```assembly
; MOVEMENT PACKET (0x0085) - Look for:
66 C7 44 24 XX 85 00    mov word ptr [esp+XX], 85h
88 44 24 XX             mov [esp+XX], al        ; coord 1
88 5C 24 XX             mov [esp+XX], bl        ; coord 2
88 4C 24 XX             mov [esp+XX], cl        ; coord 3

; ACTION PACKET (0x0437) - Look for:  
66 C7 44 24 XX 37 04    mov word ptr [esp+XX], 437h
89 44 24 XX             mov [esp+XX], eax       ; target ID
88 4C 24 XX             mov [esp+XX], cl        ; action type
```

### Checksum Calculation Patterns
```assembly
; PATTERN 1: Simple additive checksum
33 C0                   xor     eax, eax        ; checksum = 0
8B 4D XX                mov     ecx, [ebp+XX]   ; length
02 04 XX                add     al, [...]       ; add byte
E2 XX                   loop    ...             ; repeat

; PATTERN 2: Account ID usage
A1 XX XX XX XX          mov     eax, ds:account_id
03 05 XX XX XX XX       add     eax, ds:tick_count
33 C3                   xor     eax, ebx

; PATTERN 3: Multiplicative operations
69 C0 XX XX XX XX       imul    eax, constant
03 C3                   add     eax, ebx
25 FF FF FF FF          and     eax, 0FFFFFFFFh
```

## 📊 Assembly Instruction Quick Reference

```
┌─────────────────────────────────────────────────────────────┐
│                 COMMON ASSEMBLY INSTRUCTIONS                │
├─────────────────────────────────────────────────────────────┤
│ DATA MOVEMENT:                                              │
│ mov eax, ebx    │ Copy ebx to eax                           │
│ lea eax, [ebx+4]│ Load address (ebx+4) into eax            │
│ push eax        │ Push eax onto stack                       │
│ pop eax         │ Pop from stack into eax                   │
├─────────────────────────────────────────────────────────────┤
│ ARITHMETIC:                                                 │
│ add eax, ebx    │ eax = eax + ebx                           │
│ sub eax, ebx    │ eax = eax - ebx                           │
│ imul eax, 5     │ eax = eax * 5                             │
│ xor eax, ebx    │ eax = eax XOR ebx                         │
│ and eax, 0FFh   │ Keep only lower 8 bits                    │
├─────────────────────────────────────────────────────────────┤
│ CONTROL FLOW:                                               │
│ call function   │ Call function                             │
│ jmp address     │ Unconditional jump                        │
│ je address      │ Jump if equal                             │
│ loop address    │ Decrement ecx, jump if not zero           │
├─────────────────────────────────────────────────────────────┤
│ MEMORY ACCESS:                                              │
│ mov eax, [ebx]  │ Load 4 bytes from address in ebx         │
│ mov al, [ebx]   │ Load 1 byte from address in ebx          │
│ mov [ebx], eax  │ Store eax to address in ebx              │
└─────────────────────────────────────────────────────────────┘
```

## 🎯 Target Identification Checklist

### ✅ Found Packet Construction Function When:
- [ ] See packet ID (85 00 or 37 04) being written to memory
- [ ] See coordinate/target data being written after packet ID
- [ ] Function calls send/WSASend afterwards
- [ ] Function is called from game logic code

### ✅ Found Checksum Calculation When:
- [ ] See loop processing packet data byte by byte
- [ ] See account ID being loaded from global variable
- [ ] See GetTickCount API call or tick usage
- [ ] See mathematical operations (add, xor, multiply)
- [ ] Result is written to packet before sending

## 🚨 Common Hex Patterns to Recognize

```
┌─────────────────────────────────────────────────────────────┐
│                    HEX PATTERN GUIDE                        │
├─────────────────────────────────────────────────────────────┤
│ 85 00           │ Movement packet ID (little-endian)       │
│ 37 04           │ Action packet ID (little-endian)         │
│ 33 C0           │ xor eax, eax (clear register)            │
│ 8B XX           │ mov instruction (load from memory)       │
│ 03 XX           │ add instruction                           │
│ 69 XX XX XX XX  │ imul instruction (multiply)              │
│ E2 XX           │ loop instruction                          │
│ A1 XX XX XX XX  │ mov eax, ds:global_var                   │
│ FF 15 XX XX XX  │ call dword ptr [import]                  │
└─────────────────────────────────────────────────────────────┘
```

## 🔧 Troubleshooting Guide

### Problem: Can't find packet IDs
**Solutions:**
1. Try searching in different segments
2. Look for string references to "0085" or "0437"
3. Search for the decimal values (133, 1079)

### Problem: Found packet but no checksum
**Solutions:**
1. Look in calling function (press Ctrl+X on packet function)
2. Search for GetTickCount API calls
3. Look for global variables (account_id, tick_count)

### Problem: Assembly looks too complex
**Solutions:**
1. Focus on the overall pattern, not individual instructions
2. Look for repeated operations (loops)
3. Trace data flow backwards from packet send

### Problem: Multiple similar functions
**Solutions:**
1. Look for the one that handles both 0x0085 and 0x0437
2. Check which one is called during actual gameplay
3. Look for the most recent/complex implementation

## 📝 Documentation Template

```
CHECKSUM ANALYSIS RESULTS
========================

Packet Function Address: 0x________
Checksum Function Address: 0x________

Assembly Code:
[PASTE RELEVANT ASSEMBLY HERE]

Variables Identified:
- Account ID: ds:________ (global variable)
- Tick Count: [HOW OBTAINED]
- Packet Data: [HOW PROCESSED]
- Constants: [LIST ANY MAGIC NUMBERS]

Algorithm Steps:
1. Initialize checksum = 0
2. [DOCUMENT EACH STEP]
3. [...]
4. Return final checksum

C Pseudocode:
uint32_t checksum = 0;
// [YOUR ALGORITHM HERE]
return checksum;
```

## 🎉 Success Validation

**You've found the correct algorithm when:**
- ✅ It processes packet data byte by byte
- ✅ It uses account ID and/or tick count
- ✅ It produces a 32-bit result
- ✅ It's called before packet transmission
- ✅ Testing shows no server disconnections

**Print this cheat sheet and keep it handy while reverse engineering!**
