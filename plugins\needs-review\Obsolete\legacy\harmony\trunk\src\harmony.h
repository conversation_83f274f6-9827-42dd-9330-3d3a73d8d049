/**
 * OpenKore - Harmony packet enryption
 * Copyright (C) 2008 darkfate
 
 * This program is free software; you can redistribute it and/or modify it under
 * the terms of the GNU General Public License as published by the Free Software
 * Foundation; either version 3 of the License, or (at your option) any later
 * version.

 * This program is distributed in the hope that it will be useful, but WITHOUT
 * ANY WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS
 * FOR A PARTICULAR PURPOSE. See the GNU General Public License for more
 * details.

 * You should have received a copy of the GNU General Public License along with
 * this program; if not, see <http://www.gnu.org/licenses/>.
 */

#ifndef _HARMONY_H_
#define _HARMONY_H_

typedef int bool;

unsigned char bf_hard_key[256] = {
	0xE7, 0xEE, 0x7D, 0x53, 0xEF, 0x69, 0x44, 0xD8,
	0xE0, 0x45, 0xD5, 0xB9, 0x02, 0x39, 0xF3, 0x06,
	0xFA, 0x87, 0x37, 0x24, 0x98, 0xD3, 0xF2, 0x60,
	0x04, 0xAC, 0x37, 0x7C, 0x03, 0x98, 0xDA, 0xEA,
	0x87, 0x58, 0x3E, 0x76, 0xC1, 0x82, 0x4F, 0xA1,
	0xC7, 0x24, 0x5B, 0xCA, 0x5D, 0x4E, 0xD1, 0x57,
	0xD6, 0x08, 0x7C, 0x6F, 0xDB, 0x6E, 0xCF, 0xE0,
	0x1A, 0x06, 0x5D, 0x1E, 0x9E, 0x37, 0x08, 0x26,
	0x8F, 0x46, 0x9C, 0x51, 0xC9, 0xEB, 0xF2, 0x91,
	0x0F, 0x4D, 0x5C, 0x6D, 0x9C, 0x2D, 0xC4, 0x72,
	0x35, 0x40, 0xE1, 0x11, 0xAF, 0xB1, 0xF1, 0xCA,
	0xB7, 0x4E, 0xE8, 0x56, 0x86, 0xF1, 0x7C, 0x16,
	0x38, 0x18, 0x67, 0x01, 0x04, 0x59, 0x92, 0x14,
	0xA7, 0xEE, 0x81, 0x43, 0x1B, 0x46, 0xB6, 0x51,
	0x87, 0x98, 0x62, 0x36, 0x49, 0x54, 0x00, 0x01,
	0xA3, 0xE8, 0x58, 0x29, 0xD9, 0xD4, 0x3F, 0x12,
	0xED, 0xA7, 0x13, 0xF1, 0x01, 0xA5, 0x05, 0xA8,
	0x94, 0x86, 0xEC, 0xB0, 0xCC, 0xA3, 0x01, 0x54,
	0x3B, 0x64, 0x8A, 0x85, 0xB8, 0x8B, 0x87, 0x5B,
	0x74, 0xDF, 0x85, 0x4E, 0xB3, 0xC5, 0x60, 0xA1,
	0x6C, 0x73, 0x92, 0x6E, 0x19, 0x97, 0x17, 0xAD,
	0x1E, 0x03, 0x5E, 0xEB, 0xA7, 0x5F, 0x3F, 0xE3,
	0xC4, 0xCA, 0x68, 0x7D, 0x55, 0xF0, 0xD9, 0xC9,
	0xCF, 0x5E, 0x17, 0x83, 0x24, 0x78, 0x24, 0x91,
	0xEB, 0xB7, 0xFF, 0x05, 0x4E, 0x16, 0xB3, 0x6D,
	0x1A, 0x11, 0x59, 0xC1, 0x71, 0x99, 0xA4, 0x35,
	0x63, 0x0D, 0xB2, 0xB9, 0xFD, 0x8B, 0x82, 0xCD,
	0xEA, 0x9A, 0x50, 0x0E, 0x12, 0x75, 0x9F, 0xFE,
	0x2C, 0x9F, 0x03, 0x7B, 0xB5, 0xB6, 0xE9, 0xCF,
	0xC7, 0x42, 0x91, 0x38, 0xDB, 0x35, 0x6E, 0x3F,
	0x42, 0x20, 0xF8, 0x40, 0xAC, 0x7B, 0x0D, 0x96,
	0x15, 0x5E, 0xA5, 0x28, 0xD3, 0x45, 0x26, 0x00
};

unsigned char bf_key[512];
bool bf_key_switch;

unsigned char keymod_count;
unsigned long packet_count;

unsigned long create_packet(unsigned char *dst, unsigned char *src, unsigned long len);
unsigned long create_key(unsigned char *dst);
bool switch_bf_key(void);
int init_bf_key(int rand);
int modify_bf_key(unsigned char seed);
void on_connect(void);
int bf_encipher(unsigned char *input, unsigned char *output, int len, unsigned char *key, int key_len);

#endif
