# eventMacro
My personal rework of openkore's macro plugin

TODO:

1 - Create more conditions, at least the same number as the old macro plugin had.
2 - Hook AI only when we are sure an automacro is to be activated.
3 - Make slot system for automacros and macros, so in each slot a macro can be run and a group of automacros can be checked (so multiple macros can be run at the same time).
4 - Transfer macro code check to parsing time.
5 - Review macro condition code parsing (old Parser.pm and Script.pm)
