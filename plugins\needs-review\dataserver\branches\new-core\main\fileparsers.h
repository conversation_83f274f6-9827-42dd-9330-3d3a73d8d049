/*  Kore Shared Data Server
 *  Copyright (C) 2005  Hong<PERSON> Lai <hongli AT navi DOT cx>
 *
 *  This program is free software; you can redistribute it and/or modify
 *  it under the terms of the GNU General Public License as published by
 *  the Free Software Foundation; either version 2 of the License, or
 *  (at your option) any later version.
 *
 *  This program is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  ME<PERSON>HANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU General Public License for more details.
 *
 *  You should have received a copy of the GNU General Public License
 *  along with this program; if not, write to the Free Software
 *  Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA  02111-1307  USA
 */

#ifndef _DESCRIPTIONS_H_
#define _DESCRIPTIONS_H_

#include "string-hash.h"


/*****************************
 * File parser functions
 *****************************/


StringHash *desc_info_load (const char *filename);
StringHash *rolut_load (const char *filename);


#endif /* _DESCRIPTIONS_H_ */
