Version 1.3
Improvements made by <PERSON>cilon:
- Included timesToBlockPM option, where you can block/ignore any player that pmed you more then this option times, so don't wory with those anoing players that keep sending pms to you.
- Included timeToResetCount option, time in seconds to reset the chat count, so every this option seconds the count of chats received from every players will be reseted.

Version 1.2
Improvements made by Mucilon:
- Included noPlayers option, where you can not answer any player you write at here comma separeted and regexp supported, very good to bot party members.
- Included noWords option, where you can not answer any player you write at here comma separeted and regexp supported, very good to don't spam, when some player asks for buff at your side or to your priest party member.

Version 1.1
Improvements made by Mucilon:
- Included inLockOnly to answer to plublic chat, so you won't spam at cities.
- Disable the plugin to learn from system chat, so you won't speak exatly the sentences from the server messages to other players, but you will be able to answer to system chat, if you want to.

Version 1.0
First version made by <PERSON><PERSON><PERSON> was maint to answer any chat (public, pm, party, guild or system) based on the words of the chat received from other players.
- Choose the name of file to record all the sentences of each bot, so they can have a diferent personality!  
- Enable or disable the answer to any kind of chat.
- Rate to answer, type speed configurable.
- Rate to include at the end of bot answers a smiley and input any smiley you want.
- Option to turn on / off the learn state.
