#!/usr/bin/perl
# GNJOY LATAM Error 5011 Diagnostic Tool
use strict;
use warnings;

print "GNJOY LATAM ERROR 5011 DIAGNOSTIC ANALYSIS\n";
print "="x50 . "\n\n";

# Analyze the error progression
print "ERROR PROGRESSION ANALYSIS:\n";
print "Previous Error: 006A (code 30) - Connection denied\n";
print "Current Error:  083E (code 5011) - Account authentication failed\n";
print "Progress: ✅ OTP token accepted, ❌ Account validation failed\n\n";

print "ERROR CODE 5011 MEANINGS:\n";
print "1. Account banned/suspended\n";
print "2. Invalid username/password combination\n";
print "3. Account region restrictions\n";
print "4. Account type/VIP status mismatch\n";
print "5. Session token/account mismatch\n";
print "6. Account already logged in elsewhere\n\n";

print "DIAGNOSTIC CHECKLIST:\n";
print "□ Can you login with official client using same credentials?\n";
print "□ Is account active and not banned?\n";
print "□ Are you using correct server (<PERSON><PERSON> vs Nidhogg)?\n";
print "□ Is account registered for LATAM region?\n";
print "□ Any VIP/premium account requirements?\n";
print "□ Account already logged in elsewhere?\n\n";

print "PACKET ANALYSIS NEEDED:\n";
print "1. Compare 083E packet structure with 006A\n";
print "2. Analyze credential encoding in 0825 packet\n";
print "3. Check if username/password need special formatting\n";
print "4. Verify OTP token is correctly linked to account\n\n";

print "IDA PRO INVESTIGATION TARGETS:\n";
print "1. Search for '083E' or '3E 08' (packet ID)\n";
print "2. Search for '5011' or '93 13' (error code in hex)\n";
print "3. Look for account validation functions\n";
print "4. Find credential verification routines\n";
print "5. Analyze session token validation\n\n";

print "RECOMMENDED TESTS:\n";
print "1. Test with official client first\n";
print "2. Try different server (Freya vs Nidhogg)\n";
print "3. Check account status on website\n";
print "4. Verify no concurrent logins\n";
print "5. Test with different account if available\n\n";

print "NEXT STEPS:\n";
print "1. Confirm account works with official client\n";
print "2. Analyze 083E packet structure\n";
print "3. Check credential encoding in OpenKore\n";
print "4. Use IDA Pro to find error 5011 generation\n";
print "5. Compare working vs failing authentication\n\n";

print "="x50 . "\n";
print "ERROR 5011 = ACCOUNT VALIDATION FAILURE\n";
print "FOCUS: Account status and credential format\n";
print "="x50 . "\n";
