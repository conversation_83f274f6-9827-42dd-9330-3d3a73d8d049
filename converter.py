import os
import sys
import re

def main():
    # Determina o arquivo de entrada: argumento de linha de comando ou "RawRecvpackets.txt"
    if len(sys.argv) > 1:
        raw_path = sys.argv[1]
    else:
        raw_path = "RawRecvpackets.txt"

    if not os.path.isfile(raw_path):
        print(f"Erro: não foi possível encontrar '{raw_path}'.")
        return

    # Pasta onde está o arquivo de entrada
    base_dir = os.path.dirname(os.path.abspath(raw_path))

    # Caminho do arquivo de saída na mesma pasta
    out_path = os.path.join(base_dir, "recvpackets.txt")

    # Lê todas as linhas do RawRecvpackets.txt
    with open(raw_path, "r", encoding="utf-8") as f:
        raw_lines = f.readlines()

    # Extrai todos os valores de 'push'
    push_values = []
    for line in raw_lines:
        line = line.strip()
        if line.lower().startswith("push"):
            parts = line.split()
            if len(parts) >= 2:
                token = parts[1].rstrip(',')
                # Hex (termina em 'h' ou 'H')
                if token.lower().endswith('h'):
                    hex_part = token[:-1]
                    try:
                        val = int(hex_part, 16)
                    except ValueError:
                        continue
                else:
                    # Decimal
                    try:
                        val = int(token, 10)
                    except ValueError:
                        continue
                push_values.append(val)

    # Agrupa valores em blocos de 4 (flag, min_len, max_len, opcode)
    groups = [
        push_values[i:i+4]
        for i in range(0, len(push_values), 4)
        if len(push_values[i:i+4]) == 4
    ]

    # Gera linhas de saída no formato "OPCODE MIN MAX FLAG"
    output_lines = []
    for flag, min_len, max_len, opcode in groups:
        opcode_hex = f"{opcode:04X}"
        if max_len == 0xFFFFFFFF:
            max_field = -1
        else:
            max_field = max_len
        output_lines.append(f"{opcode_hex} {min_len} {max_field} {flag}")

    # Escreve no recvpackets.txt na mesma pasta do input
    with open(out_path, "w", encoding="utf-8") as f:
        for line in output_lines:
            f.write(line + "\n")

    print(f"Arquivo gerado: {out_path}")

if __name__ == "__main__":
    main()
