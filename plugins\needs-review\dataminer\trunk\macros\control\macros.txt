macro tour{
	do conf char 2
	pause 2
	do relog 3
	call walkThroughAlde
	pause 2
	do conf char 1
	pause 2
	do relog 3
	call walkThroughPayon
	pause 2
	do conf char 0
	pause 2
	do relog 3
	do conf char 2
	call walkThroughPron
	pause 2
	do quit
}

macro gspot{
	do conf char 0
	do relog 3
	do move 268 204
	do e ho
	pause 40
	do e wav
	pause 2
	do move 176 186
	pause 7
	do quit
}

automacro walkTroughAld {
	map aldebaran
	call walkThroughAlde
     	run-once 1
}

automacro walkTroughPay {
	map payon
	call walkThroughPayon
     	run-once 1
}

automacro walkThroughPr {
     	map prontera
     	call walkThroughPron
     	run-once 1
}


macro walkThroughAlde {
	do conf char 1
	pause 2
	do move 142 131
	pause 5
	do move 141 103
	pause 5
	do move 142 131
	pause 5
	do relog 3
}

macro walkThroughPayon {
	do conf char 0
	pause 2
	do conf char 0
	do move 162 61
	pause 5
	do move 162 94
	pause 5
	do move 178 100
	pause 5
	do move 195 97
	pause 5
	do move 162 61
	pause 5
	do relog 3
}

macro walkThroughPron{
	do conf char 2
	do move 176 186

	log Starting arround the well
	do move 173 209
	do move 155 222
	do move 138 208
	do move 144 189
	do move 164 185
	do move 157 178
	log Finished with well
	pause 4

	log Going to south crossing
	do move 155 118
	log Reached south crossing
	pause 4

	log Going to south exit
	do move 155 29
	log Reached south exit
	pause 4

	log Moving through wealth neighbourhood
	do move 138 58
	do move 117 70
	do move 117 119
	log Done wealth neighbourhood
	pause 4

	log Checking the Gap
	do move 155 116
	do move 138 92
	do move 128 92
	log Done with Gap
	pause 4
	
	log Moving to west Kafra
	do move 30 203
	log reached west Kafra

	log Returning to start point
	do move 176 186
	log Tour finished, will quit soon
	pause 4
	do quit
}

macro walkThroughPronOld {
	do conf char 2
	do move 176 186
     	do move 173 204
	pause 5
	do move 171 192
	do move 160 181
	do move 160 32
	pause 5
	do move 149 32
	do move 150 104
	do move 138 92
	do move 123 88
	do move 121 65
	do move 111 70
	pause 5
	do move 110 90
	do move 117 102
	do move 121 121
	pause 5
	do move 141 113
	do move 148 124
	pause 5
	do move 151 133
	do move 151 187
	do move 135 202
	pause 5
	do move 115 204
	do move 69 197
	do move 34 202
	pause 5
	do move 44 212
	do move 70 214
	do move 146 217
	do move 155 222
	do move 173 204
	do move 176 186
	do move stop
	pause 7
	do quit
}

