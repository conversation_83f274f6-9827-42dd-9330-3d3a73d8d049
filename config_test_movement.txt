# Configuração para Testar Movimento e Checksum GNJOY LATAM
# Login está funcionando - agora vamos testar o algoritmo de checksum

######## Login Settings (FUNCIONANDO) ########
master Latam - R<PERSON><PERSON>: Frey<PERSON>/Nidhogg/Yggdrasil
server 1
username christ<PERSON><PERSON><PERSON><PERSON>@gmail.com
password Chris2006@
loginPinCode 0103
char 0

# XKore 2 mode
XKore 2
XKore_port 6901
XKore_exeName ragexe.exe

######## DEBUG PARA CHECKSUM TESTING ########
debug 2
verboseLog 1
debugPacket_sent 2
debugPacket_received 1
debugPacket_include_dumpMethod 2

# Focar nos pacotes de movimento e ação
debugPacket_include 0085,0437,0360,0436

# Log to files
logToFile 1
logToFile_Packet 1
logToFile_Debug 1

######## AI SETTINGS PARA TESTE MANUAL ########
ai_manual 1
attackAuto 0
route_randomWalk 0
moveStepDelay 2000
attackDelay 2000

# Timeouts
timeout 30
timeout_ex 60

######## COMANDOS DE TESTE ########
# Após login completo, teste estes comandos no console:
# 
# move 100 150    # Primeiro movimento (seed 1)
# move 120 130    # Segundo movimento (seed 2)
# sit             # Ação de sentar (seed 3)
# stand           # Ação de levantar (seed 4)
# 
# LOGS ESPERADOS:
# [ROla] REAL XOR: seed_1=0x1F, packet_id=0x0085, bytes=[...], checksum=0x??
# Sent move to: 100, 150 (seed: 1, checksum: 0x??)
# [ROla] REAL XOR: seed_2=0x2C, packet_id=0x0085, bytes=[...], checksum=0x??
# Sent move to: 120, 130 (seed: 2, checksum: 0x??)

######## SINAIS DE SUCESSO ########
# ✅ Login completo sem desconexão
# ✅ Carregamento do mapa (iz_int02.gat)
# ✅ Comandos de movimento funcionam
# ✅ Seeds incrementam (1, 2, 3, 4, 5...)
# ✅ Checksums calculados corretamente
# ✅ Sem desconexões após movimento/ação

######## SINAIS DE PROBLEMA ########
# ❌ Desconexão após comando de movimento
# ❌ Seeds não incrementando
# ❌ Checksums incorretos
# ❌ Servidor rejeita pacotes
