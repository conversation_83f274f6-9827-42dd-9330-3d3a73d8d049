/* MacGuffin Cipher
* 10/3/94 <PERSON>
* (fast, unrolled version)
*/

//
// Changed to conform to padded packets emulator by <PERSON>
// $Id: mcg_cipher.h 5135 2006-11-18 22:06:15Z mouseland $
//

#ifndef  MCG_CIPHERH
#define  MCG_CIPHERH

#include "../typedefs.h"

#define ROUNDS 32
#define KSIZE (ROUNDS*3) //key size;

typedef struct MCGKey {word Val[KSIZE];} MCGKey;

CEXTERN void MCGKeyset(byte* Key, MCGKey* eKey);
CEXTERN void MCGBlockEncrypt0(byte* Blk, MC<PERSON><PERSON><PERSON>* eKey);
CEXTERN void MCGBlockEncrypt1(byte* Blk, MCG<PERSON><PERSON>* eKey);
#endif
