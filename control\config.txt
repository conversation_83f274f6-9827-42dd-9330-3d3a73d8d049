# CRITICAL FIX: Password Added to Token Login Packet
# This should resolve error 5011 (account authentication failed)

######## Login Settings ########
master Latam - RO<PERSON>: <PERSON><PERSON>/Nidhogg/Yggdrasil
server 1
username christiano<PERSON><PERSON><EMAIL>
password Chris2006@
loginPinCode 0103
char 0

# XKore 2 mode
XKore 2
XKore_port 6901
XKore_exeName ragexe.exe

######## DEBUG FOR PASSWORD FIX ########
debug 2
verboseLog 1
debugPacket_sent 2
debugPacket_received 2
debugPacket_include_dumpMethod 2

# Focus on authentication packets
debugPacket_include 0825,0AE3,083E,006A,0081,0C32,0436,0085,0437

# Log to files
logToFile 1
logToFile_Packet 1
logToFile_Debug 1

######## AI SETTINGS ########
ai_manual 1
attackAuto 0
route_randomWalk 0
moveStepDelay 2000
attackDelay 2000

# Timeouts
timeout 30
timeout_ex 60

######## CRITICAL FIX IMPLEMENTED ########
# ✅ Added password field to token_login packet (0x0825)
# ✅ Added Rijndael password encryption
# ✅ Updated packet template: 'v V C Z51 a32 a17 a15 a*'
# ✅ Password now included in authentication

######## EXPECTED RESULTS ########
# ✅ "OTP token sent back to server successfully"
# ✅ Packet 0825 now includes encrypted password
# ✅ NO MORE error 083E (code 5011)
# ✅ Successful character server login
# ✅ Character list received
# ✅ Map server connection
# ✅ [ROla] REAL XOR debug messages
# ✅ Movement commands working

######## PACKET STRUCTURE NOW ########
# Before: 0825 [431 bytes] - missing password
# After:  0825 [463 bytes] - includes 32-byte encrypted password
# 
# New structure:
# - len (2 bytes)
# - version (4 bytes) 
# - master_version (1 byte)
# - username (51 bytes)
# - password_rijndael (32 bytes) ← ADDED
# - mac (17 bytes)
# - ip (15 bytes)
# - token (variable)

######## TROUBLESHOOTING ########
# If still getting errors:
# 1. Verify account works with official client
# 2. Check if account is banned/suspended
# 3. Ensure no concurrent logins
# 4. Try different server (Freya vs Nidhogg)

######## SUCCESS INDICATORS ########
# ✅ Login progresses past OTP token stage
# ✅ Character selection screen appears
# ✅ Map loading successful
# ✅ Movement commands work with checksum
# ✅ Full bot functionality restored
macro_orphans terminate
