# Configuração XKore 1 com recv.asi - GNJOY LATAM
# recv.asi resolve checksum automaticamente via DLL injection

######## XKore 1 Settings ########
XKore 1
XKore_silent 1
XKore_bypassBotDetection 0
XKore_exeName Ragexe.exe

######## Server Settings ########
master La<PERSON> - R<PERSON><PERSON>: Freya/Nidhogg/Yggdrasil
server 1

# Credenciais necessárias para XKore 1
username <EMAIL>
password Chris2006@
char 0

######## Debug Settings ########
debug 2
verboseLog 1
debugPacket_sent 2
debugPacket_received 1
debugPacket_include_dumpMethod 2

# Focar nos packets de movimento e ação
debugPacket_include 0085,0437,0360

# Log to files
logToFile 1
logToFile_Packet 1
logToFile_Debug 1

######## AI Settings ########
ai_manual 1
attackAuto 0
route_randomWalk 0
moveStepDelay 2000
attackDelay 2000

# Timeouts
timeout 30
timeout_ex 60

######## INSTRUÇÕES XKORE 1 ########
# 1. PRIMEIRO: Copie recv.asi para pasta do Ragexe.exe
#    Exemplo: C:\Ragnarok\recv.asi
# 2. Feche qualquer instância do Ragexe.exe
# 3. Inicie OpenKore com esta configuração
# 4. OpenKore vai iniciar o cliente E fazer login automaticamente
# 5. recv.asi resolve o checksum automaticamente
# 6. Teste funcionamento normal do bot

######## LOGS ESPERADOS ########
# ✅ [XKore] Starting XKore mode 1...
# ✅ [XKore] Launching Ragexe.exe...
# ✅ [Network] Connecting to server...
# ✅ [Login] Login successful!
# ✅ [Game] You are now in the game
# ✅ Sem erro 5011 - recv.asi resolve checksum

######## VANTAGENS XKORE 1 + recv.asi ########
# ✅ recv.asi resolve checksum automaticamente
# ✅ Compatibilidade total com GNJOY LATAM
# ✅ Menos detecção de bot que XKore 0
# ✅ Login automático pelo OpenKore
# ✅ Solução definitiva para erro 5011

######## SE XKORE 0 FUNCIONAR ########
# ✅ Confirma que algoritmo de checksum está correto
# ✅ Problema é apenas na autenticação XKore 2
# ✅ Pode continuar usando XKore 0 ou
# ✅ Pode focar em resolver autenticação XKore 2

######## SE XKORE 0 NÃO FUNCIONAR ########
# ❌ Problema pode ser no algoritmo de checksum
# ❌ Precisa revisar implementação do checksum
# ❌ Pode precisar de mais engenharia reversa

######## PRÓXIMOS PASSOS ########
# 1. Teste XKore 0 primeiro (mais rápido)
# 2. Se funcionar: algoritmo OK, problema é autenticação
# 3. Se não funcionar: revisar algoritmo de checksum
# 4. Baseado no resultado, decidir estratégia final
macro_orphans terminate
