# Please Read the Users Manual
# The Manual is located at https://openkore.com/wiki/Manual

######## Login options and server-specific options ########

master Latam - RO<PERSON>: Frey<PERSON>/Nidhogg/Yggdrasil
server 1
username christiano<PERSON><PERSON><EMAIL>
password Chris2006@
loginPinCode 0103
char 0

# Poseidon Settings: https://openkore.com/wiki/Poseidon
# They must be the same as Query Server config in Poseidon.txt
poseidonServer 127.0.0.1
poseidonPort 24390

bindIp
forceMapIP

# 1 = hook into RO client, 2 = Act as stand-alone proxy, proxy = act as true proxy
# https://openkore.com/wiki/XKore
XKore 3
XKore_port 2350
XKore_dll NetRedirect.dll
XKore_injectDLL 1
XKore_autoAttachIfOneExe 1
XKore_silent 1
XKore_bypassBotDetection 0
XKore_exeName ragexe.exe

# XKore 2 / Proxy configuration
XKore_listenIp 127.0.0.1
XKore_listenPort 6901
XKore_publicIp 127.0.0.1
XKore_ID

# It is not advised to set secureAdminPassword if you're using Xkore 2
secureAdminPassword 1
adminPassword cajaucee
callSign
commandPrefix ;
callSignGM 0
inGameAuth 0

macAddress

pauseCharLogin 2
pauseCharServer 0
pauseMapServer 0
ignoreInvalidLogin 0

# Opening cash shop when connected to map server (recv/ST0)
whenInGame_requestCashPoints 0

message_length_max 80

######## Main configuration ########

alias_heal sp 28

allowedMaps
allowedMaps_reaction 1

attackAuto 2
attackAuto_party 1
attackAuto_onlyWhenSafe 0
attackAuto_followTarget 1
attackAuto_inLockOnly 1
attackAuto_notInTown 1
attackAuto_notWhile_storageAuto 1
attackAuto_notWhile_buyAuto 1
attackAuto_notWhile_sellAuto 1
attackAuto_considerDamagedAggressive 0
attackDistance 1
attackDistanceAuto 1
attackMaxDistance 1
attackMaxRouteTime 4
attackMinPlayerDistance 2
attackMinPortalDistance 4
attackUseWeapon 1
attackNoGiveup 0
attackCanSnipe 0
attackCheckLOS 1
attackRouteMaxPathDistance 20
attackLooters 0
attackLooters_dist 1
attackChangeTarget 1
aggressiveAntiKS 0

attackUpdateMonsterPos 1

# the following attack keys are meant to work only in private server (rAthena/Hercules)
# if you enable this in a official server you character will act weird, because character can't attack beyond the range
attackBeyondMaxDistance_waitForAgressive 0
attackBeyondMaxDistance_sendAttackWhileWaiting 0
attackSendAttackWithMove 0
attackWaitApproachFinish 1

autoMoveOnDeath 0
autoMoveOnDeath_x
autoMoveOnDeath_y
autoMoveOnDeath_map

attackEquip_topHead
attackEquip_midHead
attackEquip_lowHead
attackEquip_leftHand
attackEquip_rightHand
attackEquip_leftAccessory
attackEquip_rightAccessory
attackEquip_robe
attackEquip_armor
attackEquip_shoes
attackEquip_arrow

# You need the breakTime plugin: https://openkore.com/wiki/BreakTime
autoBreakTime {
	startTime
	stopTime
}

autoConfChange {
	minTime
	varTime
	lvl
	joblvl
}

autoMakeArrows 0

autoRestart 0

autoRestartMin 10800
autoRestartSeed 3600

autoRestartSleep 1
autoSleepMin 900
autoSleepSeed 900

autoResponse 0
autoResponseOnHeal 0

autoSpell
autoSpell_safe
autoPoison

avoidGM_namePattern
avoidGM_near 0
avoidGM_near_inTown 0
avoidGM_talk 0
avoidGM_reconnect 1800
avoidGM_ignoreList

avoidList 1
avoidList_inLockOnly 0
avoidList_reconnect 1800
avoidList_ignoreList

avoidHiddenActors 0
avoidHiddenMonsters 0

cachePlayerNames 1
cachePlayerNames_duration 900
cachePlayerNames_maxSize 100

clientSight 17

dcPause 1
dcOnDeath 0
dcOnDualLogin 0
dcOnDisconnect 0
dcOnEmptyArrow 0
dcOnMaxReconnections 0
dcOnMute 0
dcOnPM 0
dcOnZeny 0
dcOnStorageFull 1
dcOnPlayer 0
dcOnServerShutDown 0
dcOnServerClose 0
dcOnJobLevel
dcOnLevel

follow 0
followTarget
followEmotion 1
followEmotion_distance 4
followFaceDirection 0
followDistanceMax 6
followDistanceMin 3
followLostStep 12
followSitAuto 0
followBot 0

itemsTakeAuto 2
itemsTakeAuto_party 0
itemsGatherAuto 2
itemsGatherAuto_notInTown 0
itemsGatherAutoMinPlayerDistance 6
itemsGatherAutoMinPortalDistance 5
itemsMaxWeight 89
itemsMaxWeight_sellOrStore 48
itemsMaxNum_sellOrStore 99
cartMaxWeight 7900
itemsTakeGreed 0
itemsCheckWeight 1

lockMap
lockMap_x
lockMap_y
lockMap_randX
lockMap_randY

route_escape_reachedNoPortal 1
route_escape_randomWalk 1
route_escape_shout
route_avoidWalls 1
route_randomWalk 1
route_randomWalk_inTown 0
route_randomWalk_maxRouteTime 75
route_maxWarpFee
route_maxNpcTries 5
route_teleport 0
route_teleport_minDistance 75
route_teleport_maxTries 8
route_teleport_notInMaps
route_step 10
route_removeMissingPortals_NPC 1
route_removeMissingPortals 0
route_tryToGuessMissingPortalByDistance 1
route_reAddMissingPortals 1
route_randomFactor 0

runFromTarget 0
runFromTarget_inAdvance 0
runFromTarget_dist 5
runFromTarget_minStep 7
runFromTarget_maxPathDistance 13
runFromTarget_noAttackMethodFallback 0
runFromTarget_noAttackMethodFallback_attackMaxDist 14
runFromTarget_noAttackMethodFallback_minStep 8

saveMap
saveMap_x
saveMap_y
saveMap_warpToBuyOrSell 1
saveMap_warpChatCommand
memo1
memo2
memo3
memo4

shopAuto_open 0
shop_random 0
shop_useSkill 1

buyerShopAuto_open 0
buyerShop_random 0

sitAuto_hp_lower 40
sitAuto_hp_upper 100
sitAuto_sp_lower 0
sitAuto_sp_upper 0
sitAuto_follow 0
sitAuto_over_50 0
sitAuto_idle 1
sitAuto_look
sitAuto_look_from_wall
sitTensionRelax 0

statsAddAuto 0
statsAddAuto_list
statsAddAuto_dontUseBonus 0
statsAdd_over_99 1

skillsAddAuto 0
skillsAddAuto_list

tankMode 0
tankModeTarget

teleportAuto_hp 10
teleportAuto_sp 0
teleportAuto_idle 0
teleportAuto_portal 0
teleportAuto_search 0
teleportAuto_minAggressives 0
teleportAuto_minAggressivesInLock 0
teleportAuto_onlyWhenSafe 0
teleportAuto_maxDmg 500
teleportAuto_maxDmgInLock 0
teleportAuto_deadly 1
teleportAuto_useSkill 1
teleportAuto_useChatCommand
teleportAuto_allPlayers 0
teleportAuto_notPlayers
teleportAuto_atkCount 0
teleportAuto_atkMiss 10
teleportAuto_unstuck 0
teleportAuto_lostTarget 0
teleportAuto_dropTarget 0
teleportAuto_dropTargetKS 0
teleportAuto_dropTargetHidden 0
teleportAuto_attackedWhenSitting 0
teleportAuto_totalDmg 0
teleportAuto_totalDmgInLock 0
teleportAuto_equip_leftAccessory
teleportAuto_equip_rightAccessory
teleportAuto_lostHomunculus
teleportAuto_useItemForRespawn
teleportAuto_item1
teleportAuto_item2

dealAuto 1
dealAuto_names
partyAuto 1
partyAutoShare 0
partyAutoShareItem 0
partyAutoShareItemDiv 0
guildAutoDeny 1
attendanceAuto 1

verbose 1
showDomain 0
showDomain_NPC parseMsg_presence
showDomain_Shop list
squelchDomains
verboseDomains
beepDomains
beepDomains_notInTown
friendlyAID
showTime
showTimeDomains
showTimeDomainsFormat
wx_map_maxAutoSize 300
wx_map_monsterSticking 1
wx_map_npcSticking 1
wx_map_playersSticking 1
wx_map_portalSticking 5
wx_map_route
wx_npcTalk
wx_captcha
showAllDamage 0
manualURL https://openkore.com/wiki/Manual
forumURL https://forums.openkore.com

logChat 1
logPrivateChat 1
logPartyChat 1
logGuildChat 1
logSystemChat 1
logLocalBroadcast 1
logShop 1
logEmoticons
logConsole 1
logAppendUsername 1
logAppendServer 0
monsterLog 0
playerLog 0
logDead 1

questDisplayStyle 2

chatTitleOversize 0
shopTitleOversize 0
buyerShopTitleOversize 0

sleepTime 10000

ignoreAll 0
itemHistory 0
autoTalkCont 1
noAutoSkill 0
portalCompile 1
portalRecord 2
portalRecord_recompileAfter 1
missDamage 0

tankersList

repairAuto 0
repairAuto_list

status_mapProperty 0
status_mapType 0

monster_filter

######## Mercenary Support ########

mercenary_attackAuto 2
mercenary_attackAuto_party 1
mercenary_attackAuto_notInTown 1
mercenary_attackAuto_inLockOnly 1
mercenary_attackAuto_notWhile_storageAuto 1
mercenary_attackAuto_notWhile_buyAuto 1
mercenary_attackAuto_notWhile_sellAuto 1
mercenary_attackAuto_considerDamagedAggressive 0
mercenary_attackAuto_onlyWhenSafe 0
mercenary_attackAuto_duringRandomWalk 0
mercenary_attackAuto_duringItemsTake 0
mercenary_attackDistance 1
mercenary_attackMaxDistance 1
mercenary_attackDistanceAuto 1
mercenary_attackMaxRouteTime 4
mercenary_attackCanSnipe 0
mercenary_attackCheckLOS 1
mercenary_attackRouteMaxPathDistance 20
mercenary_attackUseWeapon 1
mercenary_attackNoGiveup 0
mercenary_attackChangeTarget 1
mercenary_attack_dance_melee 0
mercenary_attack_dance_ranged 0

mercenary_attackBeyondMaxDistance_waitForAgressive 0
mercenary_attackBeyondMaxDistance_sendAttackWhileWaiting 0
mercenary_attackSendAttackWithMove 0
mercenary_attackWaitApproachFinish 1

mercenary_lost_teleportToMaster_maxTries 6

mercenary_route_randomWalk_rescueWhenLost 0
mercenary_route_randomWalk_stopDuringAttack 0
mercenary_route_randomWalk_waitMinDistance 0

mercenary_runFromTarget 0
mercenary_runFromTarget_inAdvance 0
mercenary_runFromTarget_dist 5
mercenary_runFromTarget_minStep 7
mercenary_runFromTarget_maxPathDistance 20
mercenary_runFromTarget_noAttackMethodFallback 0
mercenary_runFromTarget_noAttackMethodFallback_attackMaxDist 14
mercenary_runFromTarget_noAttackMethodFallback_minStep 8

mercenary_idleWalkType 1
mercenary_followDistanceMin 3
mercenary_followDistanceMax 12

mercenary_moveNearWhenIdle 1
mercenary_moveNearWhenIdle_minDistance 3
mercenary_moveNearWhenIdle_maxDistance 8

mercenary_route_step 10

mercenary_tankMode 0
mercenary_tankModeTarget

mercenary_teleportAuto_hp 10
mercenary_teleportAuto_maxDmg 500
mercenary_teleportAuto_maxDmgInLock 0
mercenary_teleportAuto_deadly 1
mercenary_teleportAuto_unstuck 0
mercenary_teleportAuto_dropTarget 0
mercenary_teleportAuto_dropTargetKS 0
mercenary_teleportAuto_totalDmg 0
mercenary_teleportAuto_totalDmgInLock 0
mercenary_teleportAuto_attackedWhenSitting 0

######## Homunculus Support ########

homunculus_attackAuto 2
homunculus_attackAuto_party 1
homunculus_attackAuto_notInTown 1
homunculus_attackAuto_inLockOnly 1
homunculus_attackAuto_notWhile_storageAuto 1
homunculus_attackAuto_notWhile_buyAuto 1
homunculus_attackAuto_notWhile_sellAuto 1
homunculus_attackAuto_considerDamagedAggressive 0
homunculus_attackAuto_onlyWhenSafe 0
homunculus_attackAuto_duringRandomWalk 0
homunculus_attackAuto_duringItemsTake 0
homunculus_attackDistance 1
homunculus_attackMaxDistance 1
homunculus_attackDistanceAuto 1
homunculus_attackMaxRouteTime 4
homunculus_attackCanSnipe 0
homunculus_attackCheckLOS 1
homunculus_attackRouteMaxPathDistance 20
homunculus_attackUseWeapon 1
homunculus_attackNoGiveup 0
homunculus_attackChangeTarget 1
homunculus_attack_dance_melee 0

homunculus_attackBeyondMaxDistance_waitForAgressive 0
homunculus_attackBeyondMaxDistance_sendAttackWhileWaiting 0
homunculus_attackSendAttackWithMove 0
homunculus_attackWaitApproachFinish 1

homunculus_lost_teleportToMaster_maxTries 6

homunculus_route_randomWalk_rescueWhenLost 0
homunculus_route_randomWalk_stopDuringAttack 0
homunculus_route_randomWalk_waitMinDistance 0

homunculus_runFromTarget 0
homunculus_runFromTarget_dist 5
homunculus_runFromTarget_minStep 7
homunculus_runFromTarget_maxPathDistance 20
homunculus_runFromTarget_noAttackMethodFallback 0
homunculus_runFromTarget_noAttackMethodFallback_attackMaxDist 14
homunculus_runFromTarget_noAttackMethodFallback_minStep 8

homunculus_idleWalkType 1
homunculus_followDistanceMin 3
homunculus_followDistanceMax 12

homunculus_moveNearWhenIdle 1
homunculus_moveNearWhenIdle_minDistance 3
homunculus_moveNearWhenIdle_maxDistance 8

homunculus_route_step 10

homunculus_tankMode 0
homunculus_tankModeTarget

homunculus_StandByAuto 0
homunculus_teleportAuto_hp 10
homunculus_teleportAuto_maxDmg 500
homunculus_teleportAuto_maxDmgInLock 0
homunculus_teleportAuto_deadly 1
homunculus_teleportAuto_unstuck 0
homunculus_teleportAuto_dropTarget 0
homunculus_teleportAuto_dropTargetKS 0
homunculus_teleportAuto_totalDmg 0
homunculus_teleportAuto_totalDmgInLock 0
homunculus_teleportAuto_attackedWhenSitting 0

# Turn on/off homunculus autofeeding
homunculus_autoFeed 1
# Feed homunculus when meet the hunger value (homunculus_hunger > homunculus_return)
homunculus_hunger 15
# Return homunculus when meet the hunger value
homunculus_return 11
# In Wich maps should we allow feeding? (leave empty for any map)
homunculus_autoFeedAllowedMaps

# Turn on/off pet autofeeding
pet_autoFeed 1
# Feed pet when meet the hunger value
pet_hunger 25
# Return pet when meet the hunger value
pet_return 20

######## Block options ########
# You can copy & paste any block multiple times. So if you want to
# configure two attack skills, just duplicate the attackSkillSlot block.

attackSkillSlot {
	lvl
	dist 1
	maxDist 1
	maxCastTime 0
	minCastTime 0
	hp
	sp > 10
	ap
	homunculus
	homunculus_hp
	homunculus_sp
	homunculus_dead
	homunculus_resting
	homunculus_noinfo_dead
	homunculus_noinfo_resting
	homunculus_onAction
	homunculus_notOnAction
	homunculus_whenIdle
	homunculus_whenNotIdle
	mercenary
	mercenary_hp
	mercenary_sp
	mercenary_whenStatusActive
	mercenary_whenStatusInactive
	mercenary_onAction
	mercenary_notOnAction
	mercenary_whenIdle
	mercenary_whenNotIdle
	onAction
	whenStatusActive
	whenStatusInactive
	whenFollowing
	spirit
	amuletType
	aggressives
	previousDamage
	stopWhenHit 0
	inLockOnly 0
	notInTown 0
	timeout 0
	disabled 0
	monsters
	notMonsters
	monstersCount
	monstersCountDist
	maxAttempts 0
	maxUses 0
	target_hp
	target_whenStatusActive
	target_whenStatusInactive
	target_deltaHp
	whenPartyMembersNear
	whenPartyMembersNearDist
	inInventory
	isSelfSkill 0
	isStartSkill 0
	equip_topHead
	equip_midHead
	equip_lowHead
	equip_leftHand
	equip_rightHand
	equip_leftAccessory
	equip_rightAccessory
	equip_robe
	equip_armor
	equip_shoes
	equip_arrow
	manualAI 0
}

attackComboSlot {
	afterSkill
	waitBeforeUse
	dist 1
	maxDist 1
	isSelfSkill 1
	target_hp
	target_deltaHp
	monsters
	notMonsters
	monstersCount
	monstersCountDist
	whenPartyMembersNear
	whenPartyMembersNearDist
}

doCommand {
	hp
	sp
	ap
	homunculus
	homunculus_hp
	homunculus_sp
	homunculus_dead
	homunculus_resting
	homunculus_noinfo_dead
	homunculus_noinfo_resting
	homunculus_onAction
	homunculus_notOnAction
	homunculus_whenIdle
	homunculus_whenNotIdle
	mercenary
	mercenary_hp
	mercenary_sp
	mercenary_whenStatusActive
	mercenary_whenStatusInactive
	mercenary_onAction
	mercenary_notOnAction
	mercenary_whenIdle
	mercenary_whenNotIdle
	onAction
	whenStatusActive
	whenStatusInactive
	whenFollowing
	spirit
	amuletType
	aggressives
	monsters
	notMonsters
	monstersCount
	monstersCountDist
	stopWhenHit 0
	inLockOnly 0
	notWhileSitting 0
	notInTown 0
	timeout
	disabled 0
	whenPartyMembersNear
	whenPartyMembersNearDist
	inInventory
	inCart
	inMap
	manualAI 0
}

useSelf_skill {
	lvl
	maxCastTime 0
	minCastTime 0
	hp
	sp
	ap
	homunculus
	homunculus_hp
	homunculus_sp
	homunculus_dead
	homunculus_resting
	homunculus_noinfo_dead
	homunculus_noinfo_resting
	homunculus_onAction
	homunculus_notOnAction
	homunculus_whenIdle
	homunculus_whenNotIdle
	mercenary
	mercenary_hp
	mercenary_sp
	mercenary_whenStatusActive
	mercenary_whenStatusInactive
	mercenary_onAction
	mercenary_notOnAction
	mercenary_whenIdle
	mercenary_whenNotIdle
	onAction
	whenStatusActive
	whenStatusInactive
	whenFollowing
	spirit
	amuletType
	aggressives
	monsters
	notMonsters
	monstersCount
	monstersCountDist
	stopWhenHit 0
	inLockOnly 0
	notWhileSitting 0
	notInTown 0
	timeout 0
	disabled 0
	whenPartyMembersNear
	whenPartyMembersNearDist
	inInventory
	manualAI 0
}

useSelf_skill_smartHeal 1

partySkillDistance 0..8

partySkill {
	lvl
	dist 1
	maxDist 8
	maxCastTime 0
	minCastTime 0
	hp
	sp
	ap
	homunculus
	homunculus_hp
	homunculus_sp
	homunculus_dead
	homunculus_resting
	homunculus_noinfo_dead
	homunculus_noinfo_resting
	homunculus_onAction
	homunculus_notOnAction
	homunculus_whenIdle
	homunculus_whenNotIdle
	mercenary
	mercenary_hp
	mercenary_sp
	mercenary_whenStatusActive
	mercenary_whenStatusInactive
	mercenary_onAction
	mercenary_notOnAction
	mercenary_whenIdle
	mercenary_whenNotIdle
	onAction
	whenStatusActive
	whenStatusInactive
	whenFollowing
	spirit
	amuletType
	aggressives
	monsters
	notMonsters
	monstersCount
	monstersCountDist
	stopWhenHit 0
	inLockOnly 0
	notWhileSitting 0
	notInTown 0
	timeout 0
	disabled 0
	manualAI 0
	target
	target_hp
	target_isJob
	target_isNotJob
	target_whenStatusActive
	target_whenStatusInactive
	target_aggressives
	target_monsters
	target_timeout 0
	target_deltaHp
	target_dead 0
	whenPartyMembersNear
	whenPartyMembersNearDist
	inInventory
	isSelfSkill 0
}

monsterSkill {
	target
	maxUses
	whenPartyMembersNear
	whenPartyMembersNearDist
	# Skill Use Conditions, including isSelfSkill
	# Self Conditions
	# Target Monster Conditions
}

autoSwitch_default_rightHand
autoSwitch_default_leftHand
autoSwitch_default_arrow

# NOTE: In the case of two handed weapons, or no Shield,
#       duplicate the weapon name for 'rightHand'
# To attack with bare hands, specify "[NONE]" (without the quotes) for rightHand

autoSwitch {
	rightHand
	leftHand
	arrow
	distance
	useWeapon
}

equipAuto {
	topHead
	midHead
	lowHead
	leftHand
	rightHand
	leftAccessory
	rightAccessory
	robe
	armor
	shoes
	arrow
	monsters
	notMonsters
	monstersCount
	monstersCountDist
	weight 0
	whileSitting 0
	hp
	sp
	ap
	homunculus
	homunculus_hp
	homunculus_sp
	homunculus_dead
	homunculus_resting
	homunculus_noinfo_dead
	homunculus_noinfo_resting
	homunculus_onAction
	homunculus_notOnAction
	homunculus_whenIdle
	homunculus_whenNotIdle
	mercenary
	mercenary_hp
	mercenary_sp
	mercenary_whenStatusActive
	mercenary_whenStatusInactive
	mercenary_onAction
	mercenary_notOnAction
	mercenary_whenIdle
	mercenary_whenNotIdle
	onAction
	whenStatusActive
	whenStatusInactive
	whenFollowing
	spirit
	amuletType
	aggressives
	stopWhenHit 0
	inLockOnly 0
	notWhileSitting 0
	notInTown 0
	timeout 0
	disabled 0
	whenPartyMembersNear
	whenPartyMembersNearDist
	inInventory
	manualAI 0
}

useSelf_item {
	hp
	sp
	ap
	homunculus
	homunculus_hp
	homunculus_sp
	homunculus_dead
	homunculus_resting
	homunculus_noinfo_dead
	homunculus_noinfo_resting
	homunculus_onAction
	homunculus_notOnAction
	homunculus_whenIdle
	homunculus_whenNotIdle
	mercenary
	mercenary_hp
	mercenary_sp
	mercenary_whenStatusActive
	mercenary_whenStatusInactive
	mercenary_onAction
	mercenary_notOnAction
	mercenary_whenIdle
	mercenary_whenNotIdle
	onAction
	whenStatusActive
	whenStatusInactive
	whenFollowing
	spirit
	amuletType
	aggressives
	monsters
	notMonsters
	monstersCount
	monstersCountDist
	stopWhenHit 0
	inLockOnly 0
	notWhileSitting 0
	notInTown 0
	timeout 0
	disabled 0
	whenPartyMembersNear
	whenPartyMembersNearDist
	inInventory
	manualAI 0
}

######## Autostorage/autosell ########

buyAuto {
	npc
	npc_steps b
	isMarket 0
	standpoint
	distance 3
	price
	minAmount 2
	maxAmount 3
	batchSize
	onlyIdentified
	disabled 0
	maxBase
	minBase
}

sellAuto 0
sellAuto_npc
sellAuto_standpoint
sellAuto_distance 3
sellAuto_maxDistance
sellAuto_npc_steps s

storageAuto 0
storageAuto_npc
storageAuto_standpoint
storageAuto_distance 3
storageAuto_maxDistance
storageAuto_npc_type 1
storageAuto_type 0
storageAuto_npc_steps
storageAuto_password
storageAuto_keepOpen 0
storageAuto_useChatCommand
storageAuto_useItem 0
storageAuto_useItem_item
storageAuto_notAfterDeath
relogAfterStorage 0
minStorageZeny 50

npcTimeResponse
npcWrongStepsMethod 0

getAuto {
	minAmount
	maxAmount
	batchSize
	passive
	disabled 0
}

######## Debugging options; only useful for developers ########

debug 0
debugPacket_unparsed 0
debugPacket_received 0
debugPacket_ro_sent 0
debugPacket_sent 0
debugPacket_exclude
debugPacket_include
debugPacket_include_dumpMethod
debugDomains
logToFile_Debug
logToFile_Errors
logToFile_Messages
logToFile_Warnings
history_max 50
macro_orphans terminate
