/*
  This is an implementation of Snefru. Snefru is a one-way hash
  function that provides authentication. It does not provide secrecy.
  
  Snefru is named after a Pharaoh of ancient Egypt.

  Copyright (c) Xerox Corporation 1989. All rights reserved.
  
  License to copy and use this software is granted provided that it is
  identified as the "Xerox Secure Hash Function" in all material mentioning
  or referencing this software or this hash function.
  
  License is also granted to make and use derivative works provided that such
  works are identified as "derived from the Xerox Secure Hash Function" in
  all material mentioning or referencing the derived work.
  
  Xerox Corporation makes no representations concerning either the
  merchantability of this software or the suitability of this software for
  any particular purpose.  It is provided "as is" without express or implied
  warranty of any kind.
  
  These notices must be retained in any copies of any part of this software.
*/

//
// Values are changed to conform to padded packets emulator
// $Id: snefru.c 5451 2007-02-06 06:35:56Z mouseland $
//

#include "snefru.h"

#define	CHUNK_SIZE			(SNEFRU_INPUT_BLOCK_SIZE - 4)
#define MASK				(SNEFRU_INPUT_BLOCK_SIZE - 1)

#define round(L,C,N,SB)		SBE=SB[C&0xffL];L^=SBE;N^=SBE
#define rotate(B)			B=(B>>shift) | (B<<leftShift)

typedef unsigned long int dword;
/* The standard S-Box  */
dword standardSBoxes[16][256]= {
            {  /* Start of S Box 0  */
               0x7D8EA231L,0x8BB72ABAL,0xD30A00E9L,0x8D9F4CD6L,0x927FF9ACL,
               0x1CB42E53L,0x3366E403L,0xEF8F46A6L,0x3C485B76L,0x40982C5AL,
               0x259BC47DL,0xD5575AD6L,0x78D7C88FL,0x174447A1L,0xAB96CF35L,
               0x6EE67A44L,0x484E50F7L,0x62C77C47L,0xC569E55DL,0x8A5DB512L,
               0xAA8DF4BFL,0x5F11BC4DL,0x711206B5L,0x399A0B3FL,0xA192A3D6L,
               0xC0421881L,0xB193437BL,0x8CAF6CBCL,0x3A8F7E3CL,0xE44B8F25L,
               0x94EC9B91L,0x628CD977L,0x574343F1L,0x8C1A0229L,0xFA1B0EF6L,
               0xDB4F6192L,0x07DE3416L,0xC7B19F6BL,0xF4027DBAL,0xD7DA040CL,
               0x5A30407AL,0x8430380DL,0x90D006CEL,0x673BB3D5L,0x306A573DL,
               0xF675FCFEL,0xB176AB20L,0x9A758DEEL,0xAA7B8A4FL,0xEAA2CC3EL,
               0x74F26BE2L,0x50855256L,0xA753C8A1L,0x7296B6DDL,0xCA3627F4L,
               0xA96D421DL,0x47131152L,0x8D51ACBCL,0xA4510D54L,0x961C4D33L,
               0x6A998562L,0x4BF4BDFAL,0x1144FF04L,0x15926499L,0x31F7F5C1L,
               0x8C6EDA97L,0x211D0D13L,0x28FF865DL,0x8B2D8F70L,0x61BF0174L,
               0xC5AE1571L,0xCF24C361L,0x6829257EL,0xD9C754BFL,0xFC16492FL,
               0xF8201CD5L,0xE80DE7DBL,0xD4B6A24AL,0xC655882BL,0xC5038087L,
               0xFCA8B387L,0x637D2C24L,0x137CD277L,0x25AD0F99L,0xA32A9B83L,
               0x941BA06EL,0x035A4723L,0x192F89FAL,0xDD848EEEL,0x597130E6L,
               0x871FC82DL,0xAF783EBBL,0xAAA48C88L,0xA2BDEBCEL,0xAEBB5487L,
               0xC9A8EFBAL,0x0BACA681L,0x8DC1A206L,0x581FFB20L,0x76AFBB19L,
               0xFF7BDBDAL,0x0CAC738CL,0x965ABD28L,0xB67E92C7L,0x86121B82L,
               0x1E6E6062L,0x675C8B70L,0x8A1485D4L,0xA0907689L,0xB3F86897L,
               0xCB456416L,0xF1818221L,0x5EE5DDCFL,0xEB587C1BL,0xC2F6581CL,
               0xFBD6ABDDL,0x9F015E75L,0xB7809AFEL,0x5D8E6862L,0x98F1D0D8L,
               0x73F4FB6AL,0x1690D411L,0x7AED8206L,0xB9E41F32L,0xEABF94BEL,
               0x08761993L,0x2B13B8FAL,0x7DBE6ADCL,0xE5515751L,0x8E248974L,
               0x7011F94DL,0xC440D0D4L,0x73CA2644L,0xA6A9F595L,0x57F656DFL,
               0xAEB8512DL,0xA41B0086L,0x52057C14L,0xC2A2CDD1L,0x2CF8EED4L,
               0x58220527L,0x92391EE3L,0xD0254011L,0x2C1F95CAL,0xA0110617L,
               0x6434CB01L,0x6170DFA1L,0xC10E4810L,0x9BD82257L,0xDA06847FL,
               0x96A18891L,0x08C317B5L,0x096569F6L,0xD3BF684CL,0x6DAA4DDFL,
               0xC250F0BAL,0x1BDAABD4L,0x6F205767L,0xB87B1D7DL,0x20B5F5FDL,
               0xBF060911L,0x8E7751D3L,0xA712F85AL,0x11F0F590L,0xF71971AEL,
               0x41A667ADL,0x2891FE96L,0x96031082L,0xB203F69AL,0x979D98C7L,
               0x3DE7F022L,0xBDED46D3L,0x11B585C5L,0x706AC430L,0xD5052D0CL,
               0x58AE9864L,0x024E3F5FL,0xED0F1BF8L,0x10F95546L,0x9626E554L,
               0x87AEF549L,0xFC8B8E0FL,0xEFC5A8D0L,0x77865D93L,0xAFD6C672L,
               0x9FCEFC76L,0x4168F7B8L,0xDCBDE131L,0x5BE5B30AL,0xC6E9853CL,
               0x46D371E0L,0xD4CB701FL,0x99CABAE1L,0x8FDB2941L,0xCE14F677L,
               0x6FA02A5BL,0x6B78CD18L,0xEC94D9B6L,0xE93D864DL,0x8D5DEFE8L,
               0xD0FCEB9EL,0xDC34A479L,0x982D3273L,0x4FD1BFC3L,0xD9481473L,
               0x5FAB8A8CL,0xEAE4F907L,0x842C9B0DL,0x647B69A7L,0x65EA7BEEL,
               0xCF83DBDBL,0x6C5E9297L,0x8375C92BL,0x1FF07890L,0x38E9B90FL,
               0xE758A470L,0x355734FEL,0x5CEE90A1L,0x3426B181L,0xE629C2A9L,
               0x8BFFCA20L,0x2CA2B300L,0xC33AC553L,0x88D1FB30L,0x637F5BA1L,
               0x803EF1B0L,0xF526F484L,0xBD404F17L,0xEFC60872L,0x86915BCEL,
               0xBBD92F04L,0x7597AE4DL,0xEFE5D0A2L,0x10DBB02CL,0x036375D4L,
               0xD0C518C2L,0x81EBA411L,0x3DED30D9L,0xEFC4D5F2L,0xCFBA9EA7L,
               0xB7A892EFL,0xEEC5CBD5L,0x6F0D5270L,0x41873FDBL,0x7F4C9C0DL,
               0x32766220L,0x710DD94EL,0x572AEB5EL,0xDCC7A18AL,0x190D24DBL,
               0x17E47C6AL,0xFF95A320L,0xCB0AC156L,0x835BF1D5L,0x50922AD6L,
               0x3BD78552L
               /* End   of S Box 0  */  },
            {  /* Start of S Box 1  */
               0x4CFF6E5BL,0x104E56CBL,0x472D5FE1L,0x2DD9960AL,0xF1BF8984L,
               0x1E2C63ACL,0xC15D94BDL,0x69AF4617L,0x416FD815L,0xA8EAA5E4L,
               0xE96DFDF2L,0x51551A9CL,0x3FF05B0EL,0xEE890C96L,0xBC6E7A90L,
               0xE6FCF269L,0xD860E25FL,0xD01886AFL,0x2B4F2CA5L,0x26631FAFL,
               0x1EB2AE09L,0x5F772511L,0x3710E103L,0xF3CB405DL,0xF0E38E2BL,
               0x8AC6F8EBL,0xEFB2ECD9L,0x7C02A553L,0x8E0482A5L,0x5106DF2DL,
               0x4443FA07L,0x923A2FC1L,0xB806BB87L,0xC516EBB7L,0x44B53CAEL,
               0x6332CC98L,0x7FE8F7D2L,0xE5162BBAL,0xE118839CL,0xC12A8EC6L,
               0xF2BB6885L,0xA1F67F25L,0x2A4A1EF3L,0xDBF3745DL,0x015D0E90L,
               0x09B7F7E8L,0x1F6DBEC3L,0x82BB8F5DL,0xCDE0B703L,0x0D689413L,
               0xA16081EAL,0xE454BDB4L,0x246395DFL,0xAEF964A7L,0xDF53697AL,
               0xE3CE1074L,0x28B79713L,0xFB6A49A3L,0xA9168552L,0x7E3787ACL,
               0xD8FABDBFL,0xE4BC53F7L,0x2FCAB6B2L,0xC671232CL,0x151EF7B3L,
               0x8AFE80B4L,0x325E1A6BL,0xA9ABE315L,0x0E126620L,0xBC00D2D8L,
               0x01D2838BL,0x39B5D666L,0x9306F9F5L,0x9A035855L,0x6C2730F4L,
               0x65A0DF2FL,0xD4CBA022L,0x14F5F24AL,0x636BF2E6L,0x3E7B0B40L,
               0xB1708CB7L,0x3AB8B198L,0x1780E72FL,0xA2366CCAL,0x3B158CB5L,
               0x0D6B944EL,0x6696E1D1L,0xC3D1D1ABL,0x518AA00BL,0x6CFFAB5BL,
               0x627B2FEBL,0x805C7AE6L,0x13E0E8C9L,0x7762C6D2L,0xFB41715DL,
               0xEAB83798L,0x812644FFL,0x3FC615B0L,0x3FF7F827L,0xEF0419A2L,
               0x9C4CD57EL,0x830A99F7L,0x108D836AL,0x91301F15L,0x43428A65L,
               0x832F2196L,0xAD035205L,0xEF4D49F0L,0xA63953B4L,0x2F23EEADL,
               0xC66A3508L,0xDA4A9724L,0x960F516BL,0x6818CE0CL,0x9CA14D64L,
               0x60170ACFL,0x31B7728BL,0x5CDDC3D4L,0x0EC86938L,0xA3D491C3L,
               0x895EB8F3L,0xED83FC14L,0x1CD0C964L,0x82725C3FL,0x6ED513D3L,
               0x1B2849ACL,0xD6C73DF8L,0x0E0F3B03L,0xFE3D912BL,0xE49EBBADL,
               0x3D9FD6E4L,0x256D3F20L,0x2B7544CCL,0x6AF44105L,0x30488349L,
               0xF9BC67C5L,0xF49E1BD5L,0x7C3B0BD6L,0xDFE07406L,0x69FAA3B2L,
               0x69A60646L,0x3A51E9FFL,0x2A69792BL,0x76091417L,0x8A9F251FL,
               0xA568EB81L,0x12C2A3A9L,0x3E08B8D5L,0x48686951L,0xAB50027AL,
               0xA50BE19FL,0x83E761FAL,0xA222C1EBL,0x5E275DCCL,0xD53463EDL,
               0x84A74E78L,0xB8BB4DFDL,0xAEDFBD86L,0xB24EF993L,0x2246406FL,
               0x5B35EE57L,0x997640A9L,0x3A38B4A1L,0x7BD765BDL,0xA9AFB31AL,
               0x31EF0824L,0x5F028228L,0x5147AF64L,0xA4E9AB45L,0x7557D407L,
               0x10CD9607L,0xE3971D83L,0x3B14B9D8L,0x5590D452L,0x8E67BD4EL,
               0x12C8A0FAL,0x7F2FFAC3L,0xD2B9F905L,0x87E209EEL,0xFCE957DAL,
               0x5F1A5027L,0xFAC23111L,0x4D4E68F6L,0x63EA2122L,0xDAF5DAE2L,
               0xCFBBAF85L,0x9F99FC65L,0x86CC21C2L,0xF3A17807L,0x31853061L,
               0x9DC5B53EL,0x568E44DAL,0xC74D3B83L,0x4E4FE595L,0x28D1816EL,
               0xB1308C4BL,0x38D92168L,0x08E9BF32L,0x6FCD8207L,0xC9C2F813L,
               0x45353CB4L,0x6E63BD47L,0x52A9A708L,0x6D347844L,0x0E826B6AL,
               0x60CEDFA4L,0xD156F161L,0xCE972CEEL,0x63BECF77L,0x0F19167AL,
               0x0D035E23L,0xBACAF5EFL,0x55CC47FDL,0x594471A7L,0xD480E05CL,
               0x53EEF249L,0x03B9E3DAL,0x305FF04DL,0x59FE96EEL,0x98E1131BL,
               0x4D96A310L,0xC44BA33BL,0x693B71D9L,0x7AD55835L,0x345487BFL,
               0x03177B92L,0x25696F2BL,0xF7A9B2C8L,0xC6F290B5L,0xDFD37650L,
               0x8E8882E6L,0x214D30B3L,0xF581CD24L,0x577F8677L,0x9376C909L,
               0xF8E2E216L,0xD0FF1EFBL,0x7B0CCB07L,0x35553484L,0x6A56B8D1L,
               0x484E943BL,0x3C6923FEL,0x8214B478L,0x79BBA3F5L,0x6C7D3DC2L,
               0xA8B6A04EL,0x7CC368B3L,0x33F1B271L,0x2C8DFBC2L,0xB1E351F6L,
               0x02423067L,0x9B16F555L,0x988CDE2AL,0x67F12616L,0x44B23C74L,
               0x9E1C5DA1L
               /* End   of S Box 1  */  },
            {  /* Start of S Box 2  */
               0x0B6F1A86L,0xA5D481DCL,0xBA309EE9L,0xCD13F03EL,0x5FFE295CL,
               0x0F84B9F5L,0x50433378L,0xF3BF4687L,0x578764B4L,0x0F3C2E7FL,
               0xBC401588L,0xDD64CA52L,0x0508DE7DL,0xC5CEC18AL,0xCC46250AL,
               0x6DF28B8EL,0x697374A7L,0x3F5991F7L,0x822552FDL,0xB38A693CL,
               0x81E75853L,0x6FED9EE5L,0xFD1EDC51L,0xBDFB856AL,0x5F345970L,
               0x445BD844L,0x1EE07437L,0x7D75EEFAL,0xD28A971EL,0xDEC13F14L,
               0xF3AB598EL,0xD2D8640BL,0x0ACA121DL,0x1E21D456L,0x8E6F5B46L,
               0xFC35278DL,0xF803BB9EL,0x037BB619L,0xCE1DAA8FL,0xCB7B1871L,
               0x9A359180L,0xBDCDB54DL,0xC4C41739L,0x40CA36E6L,0xE251A4F3L,
               0x1CF9F2E2L,0x6E54C055L,0x8AF281CCL,0xE065E4C7L,0x212E6BF9L,
               0xCEEDA7E2L,0x79241913L,0xA263621DL,0xEB5C1281L,0xE360BCE0L,
               0x1D3FDFDBL,0x0A6A0DD4L,0x6A64E78AL,0xAEDBFD60L,0x6753C205L,
               0x375BE51CL,0x9E74F8F5L,0x2D417C50L,0x7650F2B0L,0x09350AB5L,
               0x777E37D0L,0x52AF28B2L,0x2A574FDDL,0x91074DD0L,0x0662A33DL,
               0x3CF60196L,0x9437DA7AL,0xCDD4CD6CL,0x5A5E5C0BL,0xDC3718DAL,
               0xC2209179L,0xCF997A69L,0x5424324BL,0x10716C90L,0x96F297F9L,
               0x674854E7L,0x02F3460BL,0x1AA5EDD7L,0x2FCEC9EAL,0xC3F07CD7L,
               0x76CB872DL,0xD9C19B7FL,0x6E73284DL,0xC581C138L,0x8F7D05C0L,
               0x3ED77698L,0x5231B521L,0x8C1B430AL,0x5D27B1D5L,0x57D69F23L,
               0x0AD87F76L,0x089FF25DL,0xD1CB996BL,0x26DEE52FL,0x6979773CL,
               0x291CDF12L,0x1A59AF51L,0xAAC0689CL,0x7CF3AB63L,0x0082F947L,
               0x08EFF3CAL,0xD4BB198BL,0x35860D0CL,0x9CE240EFL,0x9B5F63A3L,
               0xC28F07EBL,0xB3F2AC26L,0xED3AC507L,0xF4C8210EL,0x665C22BCL,
               0xD66869B2L,0xD47C8691L,0xF13A0CCAL,0xBF137A0EL,0xBEB762BEL,
               0x80B8759BL,0xB4861407L,0xCEC3F0B1L,0x5C1F993CL,0xE1EC9107L,
               0x2DDB69B5L,0x817CB2E6L,0x9F500D2BL,0x070AEBF5L,0x4A19ECE5L,
               0xFA1EA29CL,0x979BAE8BL,0xC2117143L,0x1D40AC86L,0x18AAC0A3L,
               0x45BF7D5DL,0x43212523L,0xA5708988L,0xEC2E1A3CL,0xA7EC79A0L,
               0x792A1664L,0xE28AA41BL,0x74ACA235L,0xCFF2A254L,0x641D4527L,
               0xD59DFB00L,0xB21467A0L,0x9C021889L,0xF509904AL,0x7DA97076L,
               0xA574598DL,0x1FFBBC3FL,0x4BDE29EFL,0xDA9F335DL,0x5DCE79EAL,
               0x46FD8D47L,0x46ADEF05L,0xEC8E22A6L,0xBB01D5B9L,0x33D89BD0L,
               0x0664D38DL,0xA4664F6FL,0xCE4E60F9L,0xD6BDC6DAL,0x6B250485L,
               0x213799AAL,0x867327BAL,0x2D7B3F46L,0x86DF51FFL,0x43012056L,
               0xF3A22BEDL,0x2A42D533L,0x5673EDEAL,0x2AB5E474L,0x26BA4D80L,
               0xDCF2B891L,0xEB00B537L,0xB662D602L,0x0ECBAC95L,0x43ACC860L,
               0x1685AB04L,0x0818F512L,0x9BC6381BL,0x6F3FF6A1L,0x0605DE43L,
               0xFFA85193L,0xEDBA0213L,0x41DB7062L,0xAB7D3E03L,0xAC31EB86L,
               0xD5A7F98CL,0xC8511886L,0xF5DFCB24L,0x1DB3A1EAL,0x828D2D66L,
               0x04DFFE2AL,0x042A96C7L,0x242E95CEL,0xF45D8EC1L,0xF547004FL,
               0xC97E9EDBL,0xF092C715L,0x0B162D8DL,0x9CA840C6L,0x33BBC350L,
               0x71E015DCL,0xD8D8E8CBL,0xF8F3BDCEL,0x52E22556L,0xA837A115L,
               0x5A83E18AL,0x09466837L,0x3712B5CEL,0x84877ABFL,0x812718AAL,
               0xC0834F12L,0xC12B82A7L,0x25B160DAL,0x7DB58B4CL,0x3BB9547EL,
               0x003E8CF1L,0x7BE49366L,0x0F3C1C5EL,0x5CD9A64BL,0xF439B3CCL,
               0x7700F474L,0x56BDEAD2L,0x32F11589L,0xAD3E2809L,0x271592E3L,
               0x6226E5C8L,0xCE03C239L,0x0C2DCAB7L,0x9E245DD3L,0x13992E3EL,
               0x1F0EBC59L,0x100499D6L,0xB91B5634L,0x8AE58316L,0x04F3C3FCL,
               0xD9D59576L,0x890C8C17L,0xA63A276FL,0xB0E0170FL,0x589FCF88L,
               0x1FF7EE5BL,0x7779F739L,0x10C79993L,0x8B52360BL,0x4ACA8F10L,
               0xFDA10465L,0x26974889L,0x640EDAFFL,0x3B975C56L,0x39D23F11L,
               0xF15124F0L
               /* End   of S Box 2  */  },
            {  /* Start of S Box 3  */
               0xDAD0D6A0L,0x496BBDECL,0x2E43ECD1L,0x6D4D5962L,0xBD3EC934L,
               0xF1FB0F3DL,0xFF3AE232L,0x7CDE47F8L,0x5DAEE153L,0x677EA819L,
               0x7F233E0EL,0x5A628A18L,0xCC1050ECL,0xAC13766FL,0xCD1EC065L,
               0xF5F804A3L,0xF9950600L,0xAEAA9B4EL,0xD91C9945L,0x5F90C4C8L,
               0xE40CF1ADL,0x7F4306B9L,0xB30CB7A0L,0x773BDA78L,0xAE8533D5L,
               0xFEEFA89DL,0x4C0E0C95L,0x6DD71791L,0x2600BB77L,0x4B8D9F0CL,
               0xA313A804L,0x1176A945L,0x6C9E7AA3L,0x572DCDF5L,0xC81989FDL,
               0x84288273L,0x601D7E6AL,0x21DF4168L,0xBC22B161L,0xC5DB923BL,
               0x23AFC99BL,0xC984FC65L,0x5D3D1F6FL,0xB481F76EL,0xB3444A56L,
               0x2F3BFCDDL,0xCD4BC3E7L,0x7239932AL,0x03DA119BL,0x44F433DFL,
               0xFB6ABDEAL,0xFEF37561L,0x10733F5BL,0x27BFC06BL,0xF87CFE66L,
               0x48A09D33L,0xFB0E8395L,0xD86D8461L,0xA291756DL,0x507E0C7EL,
               0x95BB2D69L,0x473D9DF2L,0x2BC722FFL,0x263FB044L,0xFD5B0DB7L,
               0x75FFED0DL,0x63E0360AL,0xBBF3AB94L,0x130D2480L,0x61C38392L,
               0x781B7FA0L,0x0EC8ED7EL,0xF8B191D3L,0x1B9A50A1L,0x3C480FB0L,
               0x2F9054C3L,0xAB6744B0L,0x9363724BL,0xCE77D55AL,0x0F7A12A2L,
               0x1C103D17L,0xDA3EDB6FL,0x1EB9E27EL,0xAC46260BL,0x4BDB6DF9L,
               0xEF1B7A0DL,0x3CFD351DL,0x082560FEL,0x4998D264L,0xA2FB6035L,
               0x0933DE46L,0x3305F15CL,0x04579E4AL,0x33DD9BE8L,0xA47BBDF9L,
               0x2BF8C843L,0x8F1991BBL,0x83C10D15L,0x0DB6D227L,0xD2DED5B5L,
               0xC7ECE9A5L,0x91A7B5CCL,0x34033EDFL,0x57C538B2L,0xCDB2682AL,
               0x7D90B4FEL,0x1A52E011L,0x8ABFC128L,0x928B3D19L,0x179BF8B9L,
               0xCEB4D8CDL,0x8CABA129L,0x25554993L,0x708873FFL,0x30070703L,
               0x5BA9C7A4L,0x77329AA6L,0x978734BFL,0x706D7BD4L,0xD9AA33B9L,
               0x96113224L,0x8C894C0AL,0x6FC6361FL,0x26ACE639L,0x74F2103CL,
               0x3F8D9ADEL,0x2C2037E4L,0x30A1DF43L,0x10D634CFL,0xA1832D2DL,
               0xA79D7F34L,0x09B90ED7L,0x7ACC9FDBL,0xD09BF8F6L,0xF1FC0D0DL,
               0x90C273F5L,0xA3B43062L,0xDFA6184AL,0xF86DC061L,0xF5DE3E8DL,
               0x8AAE2783L,0x8BB37F27L,0xCEE0DB4FL,0x18EC30A0L,0x3F9B542EL,
               0x06D21B8FL,0x52562BA8L,0xFA0C784DL,0xB29AC844L,0x5EE3FF62L,
               0xB5DEC28BL,0xAA1F0674L,0xE39B82E4L,0x560728DEL,0xC6589FF8L,
               0x0843CB16L,0xE39E821EL,0x2B3D88C5L,0xA4D4B2DFL,0x356AD642L,
               0xA193C8C2L,0xBE554D35L,0x42631B50L,0x208437F8L,0x2EAB45F1L,
               0x108F3A21L,0xBDF4CB4CL,0xE9BFCE28L,0x78B517BAL,0x20BC6C96L,
               0xD787B1B2L,0x50EDACE3L,0x71C210EDL,0x0FEBE596L,0xCF1DDDB2L,
               0x952DCF27L,0x48E060AAL,0xAB1CA410L,0x75A4503DL,0xA95F29E6L,
               0xEDF105E2L,0x065FB914L,0xF94E0841L,0x6B94DA20L,0x3114D1B4L,
               0x3F95F4A2L,0x2CEA07C0L,0xFBDAB0F3L,0x5339F3F0L,0x27DD86ABL,
               0x2D894DDAL,0x4A23ED41L,0x33615CC6L,0xDC275E3EL,0xEC3AC86DL,
               0x566F5019L,0xD18BFA27L,0x40637C5BL,0x79ED8A7BL,0x21BC196BL,
               0x4DB7EFF1L,0x82B2C0E2L,0xB5929213L,0xCB0C1948L,0x68E51A57L,
               0x73136B04L,0xCF49E035L,0x225E5EAFL,0x51268B36L,0x41664CA0L,
               0x970374D1L,0x48B2DA80L,0x1959339FL,0xCECB73C6L,0x3EBF4FF8L,
               0x3C199DEBL,0x8E9E3173L,0x0923D076L,0x827D60A9L,0xDE71A5D0L,
               0xC2D565E1L,0x138D8491L,0xA54DC7C4L,0x4FDCF440L,0xC42EEFDAL,
               0xEAE97E46L,0x8600657AL,0x7D4A893AL,0x846AB04CL,0x8057AE85L,
               0x36D548B9L,0x7AB964AEL,0x13B8B739L,0xD4C8243EL,0x93AC7263L,
               0x362B76ADL,0x500904B0L,0xF63BE172L,0xCF76E2A8L,0xAF9FDE26L,
               0x6B6B97B2L,0xD7B0E430L,0xB9519966L,0xF7158C29L,0x44C0613DL,
               0x76272D78L,0x722F96AFL,0xFD8E60A6L,0xDB178043L,0xE3A1BC4BL,
               0xF90FC972L,0xC1189ABEL,0x3180E6C3L,0x1E3D8287L,0x2DF242CFL,
               0x53950B4FL
               /* End   of S Box 3  */  },
            {  /* Start of S Box 4  */
               0x9A3192CAL,0xDEF1D9FDL,0x91563BD9L,0x0D87A385L,0x1B7E581CL,
               0xE3535586L,0x8E2091EDL,0x06EE4768L,0x63C57EF2L,0xCFC011A3L,
               0x42F65794L,0xD6604ACFL,0x8339D36BL,0x82583B53L,0xDDE67BCFL,
               0x7D0E8CC8L,0x8AA79768L,0x1DFBA5A6L,0x30F2DF9DL,0xFBA72E65L,
               0x5831ABF7L,0x7FA97F8DL,0x6A0A920EL,0x316B1F85L,0xFDD60E2AL,
               0xB86387F6L,0x7B3CA5F3L,0x5D3A5F48L,0x7A96C0E0L,0xB848FFF3L,
               0x537A068AL,0x5115FF9FL,0xBE51D139L,0x9038B693L,0x12C3B795L,
               0x1D1BDD78L,0xD9383126L,0x5F34DCB7L,0x9927C753L,0xBF2C1BE6L,
               0xCB2AE1A6L,0xD55B327DL,0xF7A72795L,0x2948B9F6L,0x9537F0B9L,
               0x337DF7D7L,0x2C32D57AL,0x7A709599L,0x254F5E5FL,0x67BA0BB4L,
               0x29D8D3D2L,0x82C3D1C0L,0x9E730C89L,0x63027E35L,0x0C9930EDL,
               0x72116C9AL,0xDDB20956L,0x36762148L,0xA766ED7BL,0x398A46E7L,
               0xF30C55B6L,0xE1E533FFL,0x294DE8ADL,0xC71F8FD8L,0xD17200A9L,
               0x627F9429L,0x83314352L,0x3CAF085CL,0x96F20A30L,0xBB2554F7L,
               0xA43FEDBAL,0x785AE073L,0x238E6459L,0xEBD55457L,0xAC58E796L,
               0x8C10272DL,0x96250D06L,0xE3A1B24BL,0x7B8D3F14L,0x67E2AD5AL,
               0xD1E80547L,0xA28960D2L,0x11CDE726L,0x2ADE833BL,0xD3B55D1AL,
               0x577B7EFDL,0x9F29EECBL,0xB3D7B89FL,0xCD9EF481L,0xC579CAAAL,
               0xE48F25F4L,0x14EA3C96L,0x7D83FA8BL,0x199276EBL,0x0001DAC0L,
               0x4C090021L,0x05924F19L,0x35C671CFL,0xF4AECF2FL,0x5B42324FL,
               0x54BDE349L,0x2806DC37L,0xBE461311L,0x3288B4F0L,0x9BE3D70DL,
               0xF2507532L,0x500AB796L,0xE0E88644L,0x98242A54L,0x83C67ECFL,
               0xCAD9A9BFL,0x6564B62CL,0x7D80BC3EL,0xFC48C6E1L,0x0AB3EB6BL,
               0xC1EA3696L,0x29F8AEACL,0x3CD47DA5L,0x22B88CAAL,0xE48C05A4L,
               0xAC7BFFBDL,0x537C64FDL,0x11B87D7CL,0xFF3A2336L,0xF7098F51L,
               0x413FBAE7L,0xD7D4BCE2L,0xC1F2906AL,0x29B27E98L,0x070D5F56L,
               0x640B4BEBL,0x6AD66D32L,0x2277BD53L,0x94E65367L,0xD95E3977L,
               0xEBC5898DL,0xF2474AB1L,0xF8EB970CL,0x05BB7797L,0x33C0F47BL,
               0x9A2237A2L,0x33EB3934L,0x1824035AL,0x62D5CFFDL,0x19297426L,
               0x46062B0FL,0x03A8FFAFL,0x5806E801L,0x6F2A0F4EL,0x203D6D6DL,
               0xB5372A89L,0x263251A9L,0x8C57EAE8L,0xE18F0D5FL,0x4EE2B5F5L,
               0xCA8A19E5L,0x718F2427L,0x6ADCFDD4L,0xAEA8AE05L,0x46FC21B3L,
               0x4DC3BD08L,0xC9443CFCL,0xD579D7A8L,0x8A6A9715L,0xE021966CL,
               0xF0D7CBA7L,0xE4656FDEL,0xA4F35D1AL,0x5A9BCD64L,0x0E66B8E6L,
               0xCA5D4797L,0x96986392L,0x9C2144FFL,0xE410F5B8L,0x776F7DF4L,
               0x5F47D7BEL,0xB4B12B1EL,0x90C5812DL,0xFC9DF3E4L,0xFF129B6DL,
               0xA56D50DFL,0x14A66C15L,0x47C7D867L,0x67F8AEBFL,0x6C24E514L,
               0x6F82A7B1L,0x7A1B0D7EL,0xA6EA0094L,0x0A06B9FCL,0x928931D0L,
               0x757B8027L,0xBCE6B1FCL,0x60E3EC78L,0x9B9B1A82L,0x46E65364L,
               0xA90EC208L,0xADEC6F76L,0x6CB842D7L,0xFF7C8535L,0x6E212297L,
               0xB2F03018L,0x14E1DAB0L,0x7F0E0798L,0xF960D1C9L,0xAD2F614DL,
               0x8435B03CL,0xC6BBD79FL,0x5CC9E08FL,0x5059E205L,0xDB94E63BL,
               0xE4730729L,0x972E4DD8L,0xEAAFB161L,0xF80F6CDDL,0xEA567646L,
               0xA8AEFAB4L,0x4C00D040L,0xFE844003L,0xA74545F6L,0x8139F633L,
               0x848C5EC2L,0xCA2574CCL,0x4B4E624AL,0x31D04246L,0x84041BE7L,
               0x6ED2F728L,0xC753EF21L,0xC892FCEBL,0x6BA64880L,0xD999CA17L,
               0x0974BB9BL,0x176FF624L,0x1A54B3CCL,0x1B7CFB9AL,0x23BFC799L,
               0x4D4740E1L,0xAF1D7E9BL,0x345A7CA0L,0x15064139L,0x5A3BF840L,
               0xFCF299FDL,0x25534C59L,0xDD680B6DL,0x2E4AE043L,0x20E102F3L,
               0xED686B95L,0x7DE52525L,0xD95437C9L,0x3BECDB8BL,0x8C97EA65L,
               0xE47E9D7FL,0x5C89EDE2L,0x0EF2E3A7L,0xE2C2B8C8L,0x2202556DL,
               0xB6DAD29FL
               /* End   of S Box 4  */  },
            {  /* Start of S Box 5  */
               0x59A25EF4L,0x6388040EL,0x05697AD1L,0xAEB10DB9L,0x79BDF8E4L,
               0xD5BBABCFL,0x1D1641A7L,0x800E57D9L,0x79EDFB81L,0x27119A3DL,
               0x05D8801AL,0x427FFA85L,0x5A4156DAL,0x699DF147L,0xDECF1739L,
               0xF50405EDL,0x1AC929B0L,0x8C4CB0FEL,0x97D916F5L,0x87CD89F2L,
               0xCB665541L,0x8F00E750L,0x20087D5DL,0xFA9B5492L,0x4C27E87FL,
               0x62F7674FL,0xA95B3D50L,0x5E9D98EFL,0xBE1CD459L,0x36035FEAL,
               0x02D255F1L,0x91C334D9L,0x1F2538CFL,0xE944AF22L,0x5C7DE64DL,
               0xB50E386EL,0x4152F5E2L,0x7D986815L,0x763DDE36L,0xB97CA5A0L,
               0x63A41AA1L,0xE1117895L,0x80112FCBL,0x9E0F6A8EL,0x662A961CL,
               0x46BEF1C1L,0x8B29E80CL,0x62B79708L,0x48B48C13L,0x8B80D39AL,
               0x5655F9D9L,0x17923C2FL,0x1B73C9C7L,0xA0652C1FL,0x11A67353L,
               0xAC822AE1L,0xCE668F17L,0xA57FCE2FL,0xBC2B5579L,0x21A68050L,
               0x526D9D03L,0x9AADD8EDL,0x38B4AF4BL,0x77FE4E6CL,0xC59803ABL,
               0x60004A56L,0x937151BAL,0xBD5A6413L,0x29E8E1EFL,0x1676255CL,
               0xEF636CC4L,0xE3EBF377L,0x5D5B28C0L,0xAC11570DL,0x1C69DE7BL,
               0xE990EA77L,0x82F3D74DL,0x23D0F24CL,0x2893A9CEL,0xD0693913L,
               0x87B0DE77L,0x89C4F646L,0x15E2EDCEL,0xA767E05CL,0x6B904D4CL,
               0xC0CB61DDL,0xF254886AL,0x5E79F040L,0x319515BEL,0xE7F7251FL,
               0xBFEB8CA2L,0xF5BE78C1L,0xE6CE45CCL,0xFF4751FFL,0x5DA6F896L,
               0x6D2958FFL,0x7C1BED86L,0xE7CBE57AL,0xDB86BC16L,0xC4A790D8L,
               0xF18DEEEDL,0xBF54E292L,0x4989E943L,0x1D5A414EL,0x581346E0L,
               0x67113665L,0x97A17E2CL,0x35215A60L,0x9ECD078FL,0x0FF2F3D6L,
               0xC5FD7B91L,0x4D1CCB2EL,0xB5AB30DAL,0x780719E2L,0xE46ED0B2L,
               0x363C9489L,0xCCAEC2B2L,0xE121A69BL,0xD3F28E70L,0x0F6FC69FL,
               0xA3D4BC56L,0x2A7F9CFFL,0xB2BBB4DAL,0xB9C76043L,0x8A1F0D86L,
               0x54E1DAF1L,0x728932D0L,0x52536292L,0x328FC852L,0x5D87909EL,
               0x228A2883L,0xDCF4CC8DL,0xCA12EADBL,0x4732AED8L,0xA2A076D1L,
               0x36C99F15L,0x42CA65FFL,0x211125CDL,0x12FA2DCCL,0x81B2CA59L,
               0xAAA648C0L,0xDC14F450L,0x72683C64L,0xBBCF5D39L,0xF3A6832EL,
               0x774B4B8EL,0xA3FAB497L,0xC60048C6L,0x1CCB3637L,0xF287EB59L,
               0xC4A19377L,0xB156ACEFL,0x250442EDL,0x6DF7F3D0L,0xC67CDB03L,
               0x8CD058B4L,0x1E71C64FL,0xA88B63F3L,0xA77B8B2BL,0x488E7C25L,
               0xE802923EL,0xD4433BB2L,0x699E83FFL,0xE4410832L,0xA2B8D7D8L,
               0xE0106B2EL,0x1BD6F460L,0x6037EDFCL,0x4C81731FL,0xEC100426L,
               0xAD32DD7DL,0xDC423A42L,0xC67F88F2L,0xB93605DAL,0x10C20E26L,
               0x1972EF44L,0x2192E581L,0x758F6E2AL,0x6276A78CL,0x46D5FCF3L,
               0x6CD8BBBDL,0x12EC2027L,0xA54FA98DL,0x634D723EL,0x9743E885L,
               0xAF7E5AB0L,0xC94C022BL,0x50E94025L,0xA2C27EF9L,0x0D24ECF5L,
               0xCC5DC475L,0x3EB986B8L,0xAE757D19L,0x5B0FD6D6L,0xB093FF6CL,
               0xFB9E34E7L,0x7A4DD3D6L,0x89FD2864L,0x74FC81FFL,0x9AA63BC3L,
               0x3639923EL,0xA710E38EL,0x288A7C2DL,0x28D3AA4BL,0xD359B934L,
               0x95570654L,0xBD3CDFF9L,0x8624716FL,0x5F9D48E5L,0x74C381D6L,
               0x32F38A70L,0xD5AAA020L,0xCCF53F32L,0x334265E5L,0x87FDAE94L,
               0x1434488DL,0x19728F2CL,0xD2F6B1A0L,0xCC0C2A54L,0x24F23796L,
               0x472337B2L,0x61CE6507L,0xE14F1EC0L,0x23E4A04BL,0x54E947F5L,
               0xD1CB710AL,0xF7A76AC8L,0x03EA60ACL,0x42E2CFC3L,0x21DBE5AAL,
               0xED221F7DL,0xC315899AL,0x20F0A05EL,0x5110C2F6L,0xA3C22BCEL,
               0x64641934L,0xEF22F975L,0x727907EEL,0x6A9791CBL,0xF4D7037AL,
               0x9D889A48L,0x73F6A472L,0xF18F7E65L,0x657F545DL,0x1C03A4A8L,
               0x53A8AAA3L,0x789CB3AAL,0xB62A1EECL,0x8BB215B4L,0x157E1780L,
               0xDFDD518CL,0xF70A3F07L,0xDB75FF7CL,0xC668ED09L,0x1722670AL,
               0x191FBAEEL
               /* End   of S Box 5  */  },
            {  /* Start of S Box 6  */
               0x28121A1EL,0xF71F301FL,0x898CC9D9L,0x4EFB66EDL,0xD7FD98CCL,
               0xC622E118L,0xACFDF062L,0x091D5839L,0x7F148820L,0x8E6313C8L,
               0xC9BBA8A0L,0xCE7DBA4BL,0x1059E949L,0x40E2B63CL,0xEE97C294L,
               0x7C1A9E02L,0xABDBBB18L,0xFB8DCA56L,0xEDBF4C4DL,0x24D4D38EL,
               0x2E9B0E9BL,0x9F764024L,0xE60558ABL,0xB4DB99A0L,0xAB77C3D4L,
               0x2C8C47A9L,0xD789C6BEL,0x4E00D187L,0x0291F9B2L,0xA3CFBFD2L,
               0xB239B477L,0xC1617913L,0x61E89055L,0x224F98C1L,0x962714E5L,
               0x4EF19464L,0xCA6DB8AEL,0x9BFDF364L,0x6442E518L,0xA3CD2F5AL,
               0x0C2F32BCL,0xFEE8BFBDL,0x2A9A37F1L,0x02C62B06L,0x471D3D7FL,
               0x59F0FCCCL,0xDA10EB9EL,0x5AFDA967L,0x7B3AB9D7L,0xAE46AB7FL,
               0x83D31FD1L,0xAC61988DL,0x99839605L,0xECC8DAE8L,0x25C3B5D9L,
               0xD6F3F849L,0xA00AF5D7L,0x13786B16L,0xB1E0DD87L,0x0AC1CAC9L,
               0xB0CEC560L,0x34667DEAL,0x363A65E9L,0x27DD0DFFL,0xBABF16ADL,
               0x5D80F172L,0xB4C26F01L,0x4E06C1DBL,0xACDDC89FL,0x60D8F5B0L,
               0x1B87DADFL,0x4D7DF67BL,0x8839FC37L,0x6D5D6BA3L,0x7C7AB651L,
               0x4610ADC1L,0x6DC29194L,0x621F325CL,0xD5991389L,0x38E1B4CCL,
               0x3C88B7A7L,0x510E9BA9L,0x19F6E276L,0x24EF4E7CL,0xF47B3E6EL,
               0x392B55BCL,0x55802208L,0xF82A48E2L,0xB49C37DBL,0x0A759F84L,
               0x8A47E35FL,0xC693B30CL,0x5FFAA00DL,0xC50C4CF2L,0xA93C066CL,
               0x8D39A0DCL,0xF2858CE4L,0x88C16924L,0xC16DAA1EL,0x4E1CFE52L,
               0x9F5DE871L,0x35B2F80CL,0xD3CCCF85L,0xE71CDD9CL,0x1543B5C3L,
               0xDCD10899L,0xDD5945A1L,0x8A5A1E8CL,0x9476F4BAL,0x7B2E89DCL,
               0xC1124C73L,0x16D5C021L,0x0CD6A466L,0x04B77BD3L,0xBE19B50AL,
               0xAC7DF37BL,0x6E63D6C8L,0x867EDF80L,0x944D9F46L,0x1A52978BL,
               0xB93E78EEL,0xF172B4E2L,0x64AEEA38L,0x8265AD4FL,0x0D268CBAL,
               0x56930A0AL,0x2D3DB7DEL,0xE3A533B9L,0x4B5B012CL,0xB4F1C1D6L,
               0xEF09F43AL,0x4E123BD9L,0x71CD0862L,0x0B8DFA58L,0x8A02A33CL,
               0x81CCA5BDL,0x915D7F4EL,0x4B56A48FL,0x2F38D4F2L,0xCEA48F47L,
               0xBB2A58EFL,0x844CCF6CL,0xCDAB757EL,0xF5B8EB86L,0xCD24A235L,
               0xB7705B0DL,0x443C889FL,0x240AA87AL,0xC95C7E31L,0xD3D05954L,
               0xC40A0C75L,0x3D7AE624L,0xCEC0BAE2L,0xE86ED850L,0x3E06F100L,
               0x4E26A682L,0xAC526858L,0xE73AC812L,0x904E6741L,0x4A00C796L,
               0x83318773L,0xEF322A88L,0xFCA44F47L,0x4E276950L,0x643E2844L,
               0xD0680CA4L,0x424798F2L,0x2C7C7CDEL,0x2E6729CAL,0xBACB4075L,
               0x80176252L,0x02FDF102L,0xE1DEBC05L,0x9D6B05FCL,0xB8259E68L,
               0xD3ACF6EBL,0x9E72A0F5L,0x6A383C37L,0xE95F4B33L,0xAC886D79L,
               0x3344059AL,0x2033E328L,0x04C769A3L,0x5FA146CDL,0xC252ECF5L,
               0xDF6BFCCEL,0x176D08E9L,0x0AF89FC5L,0x5A9E34F5L,0x78D0872AL,
               0x144F18C3L,0xA07C5A73L,0xDC070DBBL,0x1A63822AL,0x1A4F9A53L,
               0x4E3D96D7L,0x47AE3725L,0xA5330FE0L,0xF98C8DA8L,0xC61B43FFL,
               0xAA72E355L,0x294FFD6BL,0xD2F6E1A3L,0x473772BDL,0x0882002AL,
               0xA67A4C8CL,0xB4AEC763L,0xB1800240L,0x5EC1AEB4L,0x1DE12C61L,
               0x7F731DC7L,0x24161378L,0x9E3CAD04L,0x7D866EFCL,0x3385D5E2L,
               0x71D99565L,0xD7E52EF9L,0xC668213CL,0xE1C41FB1L,0xC7CA89F8L,
               0xF9CA10A3L,0x09675532L,0x9840B936L,0x06E8FD50L,0x14DE8302L,
               0x45A4FADCL,0x28FAD570L,0x4E33D35DL,0x391E5717L,0x7A1D013CL,
               0xB1C1726FL,0x60CB2B10L,0x379CADD0L,0x98B49951L,0x33E57003L,
               0x8B81E378L,0x3E37645FL,0xBF98921CL,0xAF27F05DL,0x9F731D95L,
               0x2F1F9C84L,0xC0AA0C9BL,0x14A6E05CL,0xAC94C877L,0x0834456DL,
               0xCAE9E8C0L,0x73424210L,0x93F1E4FEL,0xDB7760FCL,0xBE5545AAL,
               0xCA3B1589L,0x838B723BL,0xA8F7FC40L,0xAA0E133AL,0x0B426AB8L,
               0x6C43812DL
               /* End   of S Box 6  */  },
            {  /* Start of S Box 7  */
               0xE883D638L,0x8CA56C2FL,0xFC9F18D0L,0xEE25B001L,0x353C28A5L,
               0xB88A3761L,0x4BE3AF2CL,0x933D58AAL,0x853C05CFL,0xE6B59C62L,
               0x9C8EC126L,0x4A8B7A01L,0xD7726CB8L,0x27267B20L,0xEF6F6D0EL,
               0x041F1727L,0x3BFE4D70L,0x5ADED5ADL,0x44968395L,0xB0EA3D1BL,
               0x92B0A8E5L,0x9FDCB8F8L,0xACF333F9L,0x7E0BEEBDL,0xFAC89E29L,
               0xE61027F2L,0x06A75E0CL,0x4F62092EL,0x55170D2BL,0x209A1FC9L,
               0x729103FDL,0x0100BF6CL,0xC3ACF7EBL,0x7B5B816FL,0xD0D1429DL,
               0xD6F3FF59L,0x32876C7AL,0xB9528EB3L,0x4147FC0BL,0xAD1DA915L,
               0xA4A96AC7L,0x1AAFF5D6L,0xB4043F26L,0x778EED9EL,0x1801E3D2L,
               0x6C32F7B6L,0x3907FD21L,0x5234ABD6L,0x9EAFE6ABL,0xC10C7265L,
               0xB04035D9L,0x3031F4DCL,0x17836333L,0x282B88C2L,0x3ADF0850L,
               0x1164B7A0L,0x91BD7B98L,0x818208FEL,0xB6A54495L,0xF3ED0433L,
               0x1E2E0DBCL,0xED2E13E7L,0x34B11B98L,0xD8ACDC83L,0xAED5199FL,
               0x5B00B79FL,0xC4037C59L,0xCFB22DA2L,0x2FD2AF4FL,0xCB39C615L,
               0x57AB48E9L,0xB80E0970L,0xB306CFBEL,0x3D986F59L,0xEC8AAD37L,
               0xA3806F1BL,0x59806AEBL,0xA24E825CL,0x83AF8D43L,0xA1694075L,
               0xE1508FD6L,0x2949201DL,0x1C0AE81DL,0xA177ABADL,0x8C562E80L,
               0xA27B48ACL,0xB8BBDCB6L,0xA3DC8083L,0x28A35808L,0x2DF3FAE9L,
               0x56A33B1DL,0xA778FF37L,0xD7350C4EL,0xABB126F5L,0x06D12332L,
               0xBE59E8BAL,0x790E3A42L,0x3AB6DDDFL,0xB845A716L,0xB7716CEBL,
               0x2C1EE215L,0xCC010E67L,0x6D0094C7L,0xC2EF5AEAL,0xD27325A5L,
               0x5192C9CDL,0x14001C37L,0xE093D2A8L,0x8A1FE1F4L,0xE7690EE2L,
               0xCD471E65L,0xFF8DD524L,0x44F12802L,0x8077CED5L,0x88C49A52L,
               0x11BE516EL,0x0129EADDL,0x2BDB1876L,0x4698901CL,0x35356876L,
               0xCF983577L,0xC865DCE5L,0x06912195L,0x5CF2FA4CL,0x804C0BDFL,
               0x68452A13L,0xD8F13CDCL,0x85F605D1L,0x54375BF5L,0x1A7CF30FL,
               0x9C87C1D2L,0xA03F9B34L,0x296925EAL,0xCED855C9L,0x6364E0A6L,
               0xEDCFBB45L,0xF1E08A9DL,0x748C2341L,0x3C768A37L,0x1C954525L,
               0xCBAE690EL,0x3D758A88L,0x17DFAE88L,0x4EA269C2L,0xA7A2C23DL,
               0xE8A47B8DL,0xF48E4C96L,0x8205183EL,0x86FCA52BL,0xA52AD750L,
               0xD4646473L,0xC89E3169L,0x667D12E6L,0x64E6CDE1L,0xB69F171EL,
               0x007DE451L,0x49440A60L,0x35E93E31L,0x9A014477L,0x5B921208L,
               0x3F617CA9L,0xF921284FL,0x80C9FB9EL,0xA80EC97DL,0x16B469BFL,
               0xCFB09D1BL,0x79B83D84L,0xF7A00BB0L,0x005DEF84L,0x98759CB5L,
               0x64FCF838L,0x4998C8B2L,0x0C3DE017L,0x7280161EL,0x61773EAAL,
               0x9CC60E71L,0xFA536B68L,0x4FE21944L,0x5048FEDAL,0xF24BCFFFL,
               0xFBBF6078L,0x2E7A972AL,0x524039D9L,0x5A061A4BL,0xFD62F056L,
               0x0F58AFDDL,0x569D0D96L,0xC5F7EF56L,0x025B09F2L,0xF37C314FL,
               0x6C215C11L,0x224F3F2FL,0x0A8A9D5CL,0xD9D74E7EL,0x740B255BL,
               0x91CC08C6L,0x130FAC85L,0xC188E57DL,0x8F1C8962L,0x02905C1BL,
               0x2FCA447BL,0xBB7FF639L,0x9C725638L,0x75AB3A3EL,0x3DBC5720L,
               0xB8AC91B3L,0xAB1FBECCL,0xFBFB9320L,0x5D040494L,0xA610B7FCL,
               0xCCF3A01FL,0x638286C0L,0x70822BC5L,0xA8C96703L,0xE02C0D3FL,
               0xED6FE33EL,0x9457DDC5L,0xBBCA81D9L,0x069CF31EL,0x7982DA6BL,
               0xBB62F983L,0xB000356CL,0x3E4164BCL,0xF8EB4B56L,0xE5B3BF0FL,
               0xC89C74BEL,0x583E5017L,0x898B370FL,0x105AEF5AL,0xC25F2DDEL,
               0x8570D541L,0x1C71BD86L,0x4E27AA63L,0xEF6860ADL,0xB4F9C539L,
               0xA2ADBDCBL,0x7E4BDE3AL,0xFDB82D49L,0xF5B74FEFL,0x491038CFL,
               0xB0A59DCFL,0x1E4D65B4L,0x38BD5353L,0xE3C83C81L,0xF556E723L,
               0x312927DDL,0x7E08D196L,0x8FC7CB11L,0x3B4CAA34L,0x564B73C5L,
               0xB69AEA97L,0x2E0DC460L,0x7479F815L,0x7EA3497AL,0xF0627D56L,
               0xCF88587CL
               /* End   of S Box 7  */  },
            {  /* Start of S Box 8  */
               0xB7E49262L,0x212C8730L,0x60A257D8L,0x8E6F1A34L,0x937CC87DL,
               0xAAE28CAAL,0xDADA5FD7L,0x1C4C581AL,0x9B53926EL,0x4E0715FCL,
               0x5F61EABCL,0xC6892AC8L,0xAE8AEF37L,0x0E6B3005L,0xFF471869L,
               0x8C159F4CL,0xCC10DEC8L,0xC92FEFF5L,0xAB7CCAEDL,0x5C0198A8L,
               0x05E5523FL,0xAF3221CCL,0x62F11E48L,0x383B23DBL,0x4A19787EL,
               0x9094065BL,0x34D5F76AL,0x3FC542D5L,0xA99D1294L,0x9D567FC1L,
               0x22F86264L,0x41AE04A6L,0x14705F71L,0xB4567BFEL,0x2A7B7045L,
               0x6FE65A4FL,0xAB922F36L,0xE8B60A12L,0x3E5D02EDL,0xA76E32CFL,
               0x4C1392C2L,0x26753CFEL,0x5D7E376CL,0xEB45AE16L,0xF9F48935L,
               0x7074F1B1L,0x98FE00B3L,0x4B7BBD35L,0xB114136FL,0xE4D24A4BL,
               0xDDCD5BD1L,0xB500403AL,0x84933071L,0x558F36ACL,0x4EEC4AC6L,
               0x4BD57507L,0x7361F159L,0xE08BA5D5L,0xBA6ACC92L,0xDC094EACL,
               0x7D8F3409L,0x96E6B8E4L,0x4237D236L,0x888C9A17L,0x82FC1B91L,
               0x48816ECCL,0xD5548AA1L,0x5F6E8A6AL,0xB2C886FFL,0x158B977AL,
               0x82C0B6F3L,0x228F0C74L,0xEEE39325L,0xFEE463F0L,0x5C9B850DL,
               0x10002265L,0x345E2422L,0xE18CC25CL,0x20B5F7FDL,0x19D0CB3DL,
               0xA7185806L,0xF084B580L,0x102EEDC5L,0x2E0008CDL,0x14311FB2L,
               0x1ADB3B8CL,0x1BE77654L,0x4E7ED734L,0xACA97935L,0x4071555EL,
               0x31FF92CBL,0x894C3A72L,0x4061578FL,0x80770108L,0x52664108L,
               0xDF693188L,0xFF88D8B0L,0xECBB4189L,0x9F2D941DL,0x30E5CA75L,
               0xC9EEECA8L,0x435F24D2L,0xE7436AF9L,0xADA1E739L,0x9FA39488L,
               0xC6528AF1L,0x5AA8D3BDL,0x35CC96C4L,0x80B8CE2FL,0x639584F8L,
               0xC86CEF47L,0xD836EA26L,0x9C2C9C9EL,0x0C3710C6L,0x527F7FA9L,
               0x870FC050L,0xA3EFFEE3L,0xC028406CL,0xF7D2A1E3L,0x41183A61L,
               0xD6E1F200L,0x906804D8L,0xB79467E3L,0x26803749L,0x14537A04L,
               0x7A075A2CL,0x73A5B1CBL,0x1647D7F9L,0x6D0495CFL,0x70E63447L,
               0x5906AD89L,0x116DFA8FL,0xC1144362L,0x8124A139L,0x4BB62D00L,
               0x38C2B1EDL,0x407394DBL,0xAEC1B103L,0x48C5306DL,0x5A871B13L,
               0xDB22692CL,0xD5AD5594L,0x6113D792L,0x979BF81FL,0x7120D135L,
               0x28E98C0CL,0x95C0009EL,0xE00F78F3L,0x338DEC24L,0x7674464BL,
               0xD3DDDD61L,0x53B18C9EL,0x0F397BEBL,0xE05EA362L,0x2E293D1BL,
               0xC2C33310L,0xE735AC79L,0x74889340L,0x93D4209DL,0x5D246D79L,
               0xDA9051EEL,0x04101705L,0x14DFB7E6L,0xF2E43A9AL,0xD83ABA2BL,
               0xBFF83E91L,0xA039D116L,0xB3E49B92L,0xF233953FL,0x752FD805L,
               0x47D28E0DL,0x8F438F61L,0x278C141AL,0x47B61630L,0x09DACEDCL,
               0x56F11608L,0x673416DCL,0x349CF742L,0xD7219282L,0x590E3085L,
               0xC22BCB66L,0x3CC15B2BL,0xB0C809FFL,0x565BEEDAL,0x2971F3C6L,
               0x4F4552ECL,0xA4CE0343L,0x7F072FF7L,0xAA27CFFEL,0x6E18EC74L,
               0xB313906FL,0xA40203EAL,0x471C1E0EL,0xA84B0AC3L,0xDEB8C152L,
               0xE36C6AA5L,0xE06010E4L,0xEDCDBB09L,0x04AC841CL,0x3E067547L,
               0xA3039692L,0x4D9E0F07L,0x45FFDCBEL,0xA40E03B0L,0x72F6BE17L,
               0xC9CEE7EBL,0xA281B626L,0x255624F1L,0x5C386A73L,0x404E5297L,
               0x19722366L,0xB1FEE818L,0x41D9A997L,0xE21D601BL,0x9CC3348DL,
               0x59F43007L,0x52C97C92L,0x9F3BF165L,0x2A53D87CL,0x1C4A2BCDL,
               0x7E09E274L,0x58A92697L,0xD4420021L,0xEAEF995BL,0xA598EB1DL,
               0x3C75ED90L,0x8881DACEL,0xC4D3AAC0L,0xF786779DL,0x2B914861L,
               0x681F3822L,0xC9274FFCL,0x55C396E5L,0x250D3708L,0x440C296EL,
               0xC9CA770FL,0xCD505914L,0x3BD7B887L,0x4A48AE70L,0xE4BC42F9L,
               0x414CAF0BL,0x6CF1CDDDL,0x4CD3C55AL,0x1AFDA1ABL,0xD17788D8L,
               0x986A65FBL,0x69BE600CL,0x6C9E9234L,0x8B01F57DL,0xEF22A0EFL,
               0xA1F8AE94L,0xB98E1794L,0x41EB04F9L,0x52397FBBL,0xE4818FF4L,
               0x21CD3FCCL
               /* End   of S Box 8  */  },
            {  /* Start of S Box 9  */
               0x76545E8CL,0xB5B2B341L,0xD3B5A6D0L,0x2E997368L,0xF1BB6855L,
               0xAB59D2F3L,0x69C00E91L,0xA66C588BL,0xA17B1F0DL,0xA6598E86L,
               0x22330242L,0x4398EA8EL,0x649261A7L,0xE4B0E5F9L,0xF01FB3C3L,
               0x032B1861L,0x5C226020L,0x3770F95DL,0xF2530035L,0xF817F234L,
               0x681A0B89L,0xBF989A80L,0x28FFFA96L,0xF27B68E8L,0xA96A43D3L,
               0x5A28E6A4L,0x62F38FC8L,0x3F388B7CL,0xFD2336FDL,0x1A11DFB8L,
               0xD160C1EAL,0x815C4AE0L,0x7633B607L,0xFE62649CL,0x64259FEDL,
               0xF7D9A545L,0x13BCE2F2L,0x061B9561L,0x1B5219CFL,0x91CFBC89L,
               0xE59EBBDDL,0x323C7216L,0xF7F74092L,0x500C60AEL,0xCAE73F88L,
               0x83B6FCABL,0xE7E50245L,0x43B2BFA4L,0xD4894023L,0x08981220L,
               0x0B4B71D9L,0x4AD0AB99L,0x02930DBEL,0x91E2E476L,0x53098D4CL,
               0x7546446FL,0x6405671AL,0x5E8453BCL,0xBF2F34A0L,0xC5248815L,
               0xDBE07C66L,0x309E6DE2L,0x40BE98D4L,0x386B69ABL,0x76121E93L,
               0x461114E8L,0xF59598F9L,0xD01AE621L,0x35BD6CAFL,0x70EC67DFL,
               0xCEE4340DL,0x9D111078L,0x18B1679CL,0xBE2067A6L,0xCCAB7DE2L,
               0x7D80E5CFL,0x202CFD79L,0x31CB025DL,0xDDBB51B7L,0x725857E6L,
               0x5CE03036L,0xC8CF4AE4L,0x1333E26DL,0xAB8865FEL,0xAC1C0FD4L,
               0x832B3F6CL,0x8E1310F2L,0xE8202FD5L,0x10A09B51L,0x63FFBFC3L,
               0x0C5BF978L,0x5A2176BDL,0xB9ACB2CFL,0x662CEC0BL,0xBF0C6FDFL,
               0xF0898966L,0x76F1861EL,0x8EB1B534L,0x86158115L,0xAA4A28FEL,
               0x57BEF64CL,0xDABE3A4CL,0x71864F3CL,0x88736377L,0x5CD3036BL,
               0x4B035B35L,0x805FAA42L,0x8A055BD0L,0x8661BB5AL,0xDFC1190EL,
               0xC481B02AL,0xB1FEEE29L,0xD4471039L,0x98F673B8L,0x2C2B53F1L,
               0xFC402F42L,0x469511F9L,0x65747951L,0xA82DA3B9L,0x6C0BFB5CL,
               0xDC4BBFA9L,0x676B2CDBL,0x5987AE41L,0xFF1D7346L,0x9769F829L,
               0x8CB97A35L,0x2E5A36C9L,0xA7989820L,0x76D0EE99L,0xD660658FL,
               0x17857A21L,0x838B69DBL,0x79BF70E9L,0x357F0CAAL,0x24185A6AL,
               0x83C6C775L,0x90F6AF2AL,0xC7F630B5L,0x5503D793L,0xA879D0F0L,
               0xECA67A4BL,0x8ED610B0L,0xBB570FACL,0xE185866CL,0x5BAEF03CL,
               0x591EAC8BL,0x3513D495L,0x5E09D8A7L,0xE01E142EL,0x58CEC447L,
               0xD337466FL,0xDFD5D6D3L,0x98F6D3E0L,0x7BC698E3L,0xA6B35329L,
               0x841971EFL,0x74263F92L,0xB337096FL,0x9CA81DB3L,0x6EB6B8EBL,
               0x75CF4624L,0x1F0F16DBL,0xA7F4633DL,0x5CBB9BB7L,0x9AB0FB97L,
               0xAF41CF18L,0xD7AA65A8L,0x7F282A74L,0xD4294BE9L,0x53EA2445L,
               0x2AB714E3L,0xB5FE5611L,0x41EB482CL,0x2CEB2662L,0xA22D5E1EL,
               0x102B2E9EL,0xD314D040L,0x1945C45FL,0x4E0A4529L,0xAFB1920CL,
               0x89961643L,0x3A071E3DL,0x0E41D915L,0x52BFB259L,0x5491F727L,
               0x7F32F5FAL,0xF2FF08F1L,0x2A167F87L,0x52E385FBL,0xD9C48798L,
               0x0BF6E4BDL,0x15D4D796L,0x75AEAEAFL,0x67BFC617L,0x38646C59L,
               0x36FBDC94L,0xBCC17534L,0x09029285L,0x893C80D6L,0x7A8B8E73L,
               0x184CE7B8L,0xD0CD19D4L,0xFF6B4143L,0xD262DB32L,0xA720060DL,
               0xDAE13D03L,0x9902AD80L,0x5FB2C5D1L,0x5B7BB043L,0xE96DFD23L,
               0x57F2B6BDL,0x007A5B60L,0x232F1868L,0x2C515922L,0x395A6CDBL,
               0xC58A9DD0L,0x1F3C2B7EL,0x849D6102L,0x4F2BBDC9L,0xBF026C20L,
               0x30A0CB54L,0x0F4216C2L,0x7A43BBA7L,0xDDE3E750L,0x758D162AL,
               0xAF6E6762L,0xC9D45566L,0x0F3C1E71L,0xDEC2FFE1L,0x73D364F3L,
               0x3CCD9B04L,0x65DDD171L,0x5B6F9378L,0x6CB10D64L,0xC41F7E93L,
               0xE0E64152L,0x0D65C4FFL,0x78F633B5L,0x8FD80D02L,0x8E585D14L,
               0xD3D2A056L,0xA99425F6L,0x6FEA2852L,0x512215C5L,0xCDA82A9DL,
               0x0F9AA308L,0x6464FF81L,0x49646957L,0xDBD74FB5L,0x8808DE0AL,
               0x9C5772A1L,0x540F6AC9L,0x1E6D01CDL,0x26DFA4FCL,0xE9918291L,
               0x8401061BL
               /* End   of S Box 9  */  },
            {  /* Start of S Box 10  */
               0x36C51AA6L,0x4A49EF52L,0x47C8F5D8L,0xCED3CD9CL,0x50FB083DL,
               0x9DB1284CL,0xF7B6BD5CL,0x207C59FBL,0xA792ACADL,0x0E9B0710L,
               0xE5163BC8L,0xCF96AA44L,0x3BBAF416L,0xCBF5ABEEL,0x00E76E3EL,
               0x8B219186L,0xED44F288L,0xA6B1F4A4L,0x5939479CL,0x853E4CC1L,
               0xDC4FB5D3L,0xBEFE0254L,0xDEFDD5E5L,0xBCABADF6L,0xF8BB2D28L,
               0x04BDC60DL,0x91121726L,0x209AC413L,0x41A84B66L,0x87DC3FA0L,
               0x81C81061L,0xC1FB8F2AL,0xD8071E9DL,0x477D5D3BL,0xAEDFCD94L,
               0x90CC003AL,0x8CD7A6CEL,0x248020B0L,0xF96730B2L,0x9B1F3634L,
               0x7D18E3D8L,0x4E03B93EL,0x806148C8L,0xC4C32127L,0xABDAD6EBL,
               0x96F8F7A5L,0x45DC15D8L,0x3B08B112L,0xF7FE8DF7L,0x2B6EEA06L,
               0x38C897D0L,0xDEAF07E8L,0x80A3DAECL,0xDD459250L,0x6716CFC3L,
               0xAFB702B6L,0x46B9EDDBL,0xCD9DF0A3L,0xB4E4BCAEL,0xBD40C28EL,
               0x3930A4B3L,0xE95703DFL,0x4E245E83L,0xE94A283FL,0x6A392184L,
               0x3392CB05L,0x05E6A651L,0x51B643E9L,0xB8A3435FL,0xCB4E3834L,
               0x0A18A218L,0xF7A2138CL,0x439E2B03L,0x8F6B6B4CL,0x2CBC54C8L,
               0xDA00A819L,0x1CEAB7B0L,0x71FA435DL,0x8BC1CB72L,0xEBD0D29FL,
               0x01B80966L,0x900AD057L,0x1747E815L,0x2810C21EL,0x34F700F5L,
               0xFC8B224BL,0xE04EC9A1L,0x93D26787L,0x94B7AC8EL,0x857D1A38L,
               0xD7B74026L,0x3BF5C1E8L,0x22D81E00L,0x4CD1C61FL,0x0B918DA5L,
               0x1099C133L,0xFC7B257BL,0x30B639EEL,0x6DFC7E1DL,0x23BF8688L,
               0xF48EF1E0L,0x610C40A7L,0x0BB9157EL,0x6336F0C5L,0x2904723EL,
               0xB0C41D69L,0xC7F771C8L,0xE03E2FFCL,0x8C0AA985L,0x4B0C9E14L,
               0xC0B6821CL,0x8AA7F32CL,0x1B7284D5L,0x14A6C6B9L,0xF6D64859L,
               0x62828D35L,0xE85A250FL,0x0BC1B247L,0x6A77B48FL,0x77EDCC47L,
               0xE2A57C31L,0x3E5E44DEL,0xFA8AE5AEL,0xC9BAB053L,0x1A70775DL,
               0x9F6B9A4EL,0xC80EBBC7L,0x48F96A48L,0x8EBC2863L,0x3DDAA7B8L,
               0xC4F346D9L,0xF5A8C936L,0x206A9E71L,0xF8CA582BL,0x0C6A97D4L,
               0xDEC9DD1EL,0xF089B969L,0xF02CCF77L,0x62418DC8L,0xE66B96EEL,
               0xFC2A8A5AL,0x260FEACCL,0x059A38B6L,0x3A7E14A8L,0x363B1044L,
               0x8952BC0BL,0xE565999DL,0xBB03486BL,0x9CBE4B18L,0x29084232L,
               0xE3A0AE6DL,0x6AF92118L,0x31A23BE4L,0xF74E7E64L,0x2E5D7926L,
               0x4660BFBEL,0x1218D1AAL,0xF1E66E8EL,0x867BF9E9L,0x6048034CL,
               0x11FE3B5AL,0x290F0591L,0x3B0A2F95L,0xB6920CD5L,0x5D464C02L,
               0x9F99609EL,0x0D1B0A3AL,0x3A6CBA56L,0xC60FF194L,0x31947094L,
               0x1D9CA9C8L,0xFBA81DC1L,0x6C4A7B2FL,0x01013684L,0x5A8FEF50L,
               0xDA563535L,0x40E59BB3L,0x0EFFA16CL,0xC5F3E9D1L,0xF5740382L,
               0x41127021L,0x485EC23EL,0x6CC9A94BL,0x5E0486D8L,0x8FA00B97L,
               0xAF2EA709L,0x41100EAEL,0xD415BE28L,0x0AAF4AF7L,0x546032BDL,
               0x53D8280BL,0x9797AC51L,0xA3203F41L,0x2623726BL,0x9211F751L,
               0x888B4E83L,0x8912E993L,0x25577812L,0x0FCB7C90L,0xA6F096AFL,
               0x9C8548CFL,0x62FD22A2L,0xB8E7B6C8L,0xF1D694B3L,0xDC595DF4L,
               0xDB03823BL,0x9F74A5FAL,0x892D56B1L,0x5AAF1722L,0x829B97BEL,
               0xA4724A15L,0x4FE6CEB9L,0x0566963AL,0x5794523AL,0xE5F29329L,
               0x322FEBA9L,0xEDAECA4BL,0x680FD19FL,0x64E2A226L,0x62CABD83L,
               0xF247A445L,0xA7FA06FDL,0x1044562DL,0xBFE74566L,0x35624238L,
               0x2347E045L,0xF928D01DL,0x4A848122L,0xB5FE8624L,0xCC158085L,
               0x006CFEF6L,0x128373E7L,0x620B800AL,0xA255C4CFL,0x5422C2C8L,
               0x07031B96L,0x4C793ED9L,0xB616CEF3L,0xD5695C94L,0x39F4774EL,
               0x6469A291L,0xF7378D1FL,0x83019A59L,0x995789DFL,0xB9CABB43L,
               0x75DBE225L,0x6E2B9E07L,0x253B406AL,0x3B9C89FEL,0x21EF0B34L,
               0x87B537AEL,0xEF80BCFDL,0xEBEF1D92L,0x0A75DA2DL,0xDDB1954FL,
               0xE746EE6AL
               /* End   of S Box 10  */  },
            {  /* Start of S Box 11  */
               0x0536D6D0L,0xDFD01A62L,0xBBEB44C0L,0x6E0D26B0L,0xBE3B9805L,
               0x8F296E85L,0x96AD6D06L,0xB99B695CL,0xBDBA294CL,0x75EC80BBL,
               0xA8F9545EL,0x3BA46A0AL,0xF2C37795L,0x923A60D2L,0x01CF0A98L,
               0x133729ABL,0x7D5684D0L,0x15020E0CL,0xBF107DE4L,0x2144A75EL,
               0x3F746F2DL,0xCE657B28L,0x94EBB033L,0x85DBF203L,0x470CF87DL,
               0xCE31A566L,0xCF40B084L,0x10FD0CBAL,0x952E5FDFL,0x04989F97L,
               0x312F7FD7L,0xF199D474L,0x2ACB7523L,0x807946DAL,0xE889FB3CL,
               0x28BF6B30L,0x04E1698AL,0x52E4BB1FL,0xE66D4694L,0x8560CFFEL,
               0x15921BE3L,0x5ACAFF56L,0x2ADB40FEL,0x399AE2BFL,0x7CCE7C4EL,
               0xA939F190L,0xA4C3276AL,0x234FC471L,0x1A73BBBBL,0x3E23B1ECL,
               0x6536BDC8L,0x637E6346L,0x0EA3A82AL,0x19A8403AL,0x7C320139L,
               0xD928D11DL,0x285D639CL,0x3B969D8AL,0xB9AA24BCL,0x966B0DE7L,
               0x9891EC00L,0x931FA8DCL,0x4CAB0421L,0x9929F7C3L,0x4E5F2486L,
               0x31127131L,0x2626B3A8L,0xE261AFA0L,0x4BA82A0FL,0x159F0998L,
               0x363C1022L,0x62342681L,0x8E6BFE8AL,0x40A76EF2L,0x9CCC4CAEL,
               0x37806B63L,0xF7B88107L,0xB039835DL,0x38C7352CL,0x43476E58L,
               0xC680D196L,0x674565CBL,0x1B6BEDBCL,0x95A92F3FL,0xCCD1F027L,
               0x65DB153BL,0x437A634FL,0x3E74BF28L,0x18BDCEBBL,0x98FB84ADL,
               0xB213A8D4L,0x1CDA0D23L,0xAA146941L,0x1296B112L,0x6837AA7BL,
               0x31BA1911L,0x73F4C3E9L,0xE2BBAD99L,0x53D46B04L,0x9C14E411L,
               0x815FFB74L,0xE85B5612L,0x85FCFAA0L,0x4E088C13L,0xE644E111L,
               0x3584DE9DL,0x0DAE485EL,0x3567E318L,0x82A396C0L,0xC738141BL,
               0xCCDA53FEL,0x6350082EL,0x639C0861L,0x906618AAL,0xD0812DA0L,
               0xD7D3EC27L,0x8B103914L,0xB01EEB3DL,0x1BB2C555L,0x92C09D32L,
               0xF90E38CAL,0x05517CC1L,0xAC7D1B0CL,0x92480D50L,0xAD86F682L,
               0xA11ECB57L,0x73B230B5L,0xD94A3B60L,0x9789723CL,0x8355D8F0L,
               0x81722370L,0x56C62881L,0xC806BCF9L,0xBB16A39BL,0xE5CCC43EL,
               0x2ADCE3A6L,0x4F1CC4B8L,0x2A614D39L,0x8F8033FEL,0x335D6BCCL,
               0x0CAE9A79L,0xDF37A5E8L,0x6FDE61C0L,0x8368A3F5L,0x00B92F4CL,
               0xCA87DC8AL,0x86A75D84L,0x190DA820L,0x594F8211L,0xFB51B03EL,
               0xE30A175BL,0xF61C7C5DL,0xD96F93E9L,0x72B663E4L,0x96E79F34L,
               0x18B60E8CL,0xA00973B3L,0x3095D4ADL,0x8F4ED60FL,0x71DA5EBEL,
               0xBC2E219FL,0x34FE0358L,0xBF20DBECL,0x10786CF2L,0x1FCC8D7EL,
               0x8ED10115L,0x348BAECCL,0x06A14938L,0xA8E5B75EL,0x0F4FBDD4L,
               0xF0723FAEL,0x2143E471L,0x8798AF31L,0xD63637A6L,0xF3E28F82L,
               0xA3704DCBL,0xACC65627L,0xF3A88F69L,0x3BDC9D78L,0x5B276418L,
               0x188ECB1EL,0x4695853FL,0xCA417961L,0x4A695A67L,0xCAB00E08L,
               0xEF1B5A08L,0x8F40035CL,0x8F240EB9L,0xA27C00F4L,0xCF1CEDE2L,
               0x9BCA6C59L,0x096A701CL,0xE1B2CFF2L,0xE5973FBFL,0xFCCD9348L,
               0xDB2AA162L,0x55735DE3L,0x519C5E9EL,0x845B784AL,0xD265AFCBL,
               0x10CE9AE5L,0xF42C2C70L,0x62532B5EL,0x20396C35L,0x1193A4FAL,
               0xED36C863L,0x86E69D54L,0xB388E792L,0x59E37DF2L,0x1CCA3259L,
               0xE1F2DD6CL,0x9E512101L,0xD7BC140BL,0x91D85B31L,0x9299CB77L,
               0xAEB53872L,0xAB107917L,0x5D70423BL,0x89AA8784L,0x05930EE5L,
               0xB5EF8D35L,0x5E93F728L,0xB6450293L,0xA2FA926BL,0x05477E45L,
               0x96306A27L,0x2A7B4AB4L,0x95DDE5E3L,0x9C3A1E68L,0x1557AB28L,
               0xE41B52D8L,0xBE39066DL,0x79978C8CL,0xE9099B2BL,0xD44527FEL,
               0x2E20E4E9L,0x9C8EB9A3L,0xF4355921L,0x1AF9BB26L,0xD4918268L,
               0xF6FFA4DDL,0x45EBE638L,0xA7280C40L,0xC08CEDF9L,0x95EB5D08L,
               0xEC1B2042L,0x69D12D7DL,0x0201178CL,0x8B61D436L,0xBAD6395FL,
               0x7214FBBCL,0x8B010F12L,0xB7611966L,0xDD1A006DL,0xC2D1A7EDL,
               0x4A8BB5B9L
               /* End   of S Box 11  */  },
            {  /* Start of S Box 12  */
               0xC49792FAL,0x73664683L,0x2EFE82C8L,0x0E4780E3L,0x1C7A38EDL,
               0x7181B4DEL,0x25931CC0L,0x33AB69CCL,0xC3D1A5EBL,0xDD3E0945L,
               0x7CCC7DD4L,0xB7A21AC0L,0xB9EBFA04L,0x797F25C7L,0x1197B5F2L,
               0x9B3DA2C0L,0x0E781538L,0x84531954L,0x06F6B43CL,0xCD5B01EAL,
               0xA2990877L,0xDECBE3FCL,0x5AE99B81L,0x4F1C3710L,0x965DD3D2L,
               0x78C585B0L,0xFE6E48E2L,0x10503552L,0xE9A47438L,0x7153FE8EL,
               0xE087CD5DL,0x31371ABEL,0x8B8EDDB9L,0xC9843F68L,0x32332AE4L,
               0xB1B2C626L,0x7D0C2D46L,0x7049476EL,0xC3725D76L,0x8FB049A9L,
               0xBE1D34FEL,0x6790367EL,0xB4445824L,0xAE519437L,0x5EB122A1L,
               0xBC7BFC9AL,0xF3BA2AFCL,0x2B86C6E0L,0x3DE8E87FL,0x52F989C1L,
               0x92B3D3C0L,0xE84ECFA5L,0x7BA37558L,0x56FBFE03L,0x704F54BFL,
               0x04999F75L,0x1900E95DL,0x999F3A61L,0xBE6FACBAL,0x8F874750L,
               0xF6F2146DL,0x3CD74DDAL,0x5A21CBCFL,0x4909B656L,0x32762778L,
               0x2E92285EL,0x3677C1F0L,0x631D0C68L,0xCE9D01BFL,0x70F1E9FDL,
               0x71508E3CL,0xDCC52985L,0xB948C2F0L,0x00E36298L,0x0CCD3384L,
               0x94F12EBDL,0xE3865A5DL,0xF077C35DL,0xE5DDAFE6L,0xBCCFE901L,
               0x7C58BAC6L,0x3F800A3EL,0x1E70E364L,0x12218C6FL,0x54BCE149L,
               0xDD3B091BL,0xA6A60DEDL,0xD826F7D9L,0x8CB4EFD8L,0xBB7AEF12L,
               0x8E7FFF81L,0xFDBE485EL,0x134FC482L,0xF84B9C25L,0xB4CCC841L,
               0x52DA51FFL,0xF96E7147L,0x84B11043L,0x3ABC580CL,0x168841AAL,
               0x2F2FF518L,0x7FB97C7CL,0x1F3FC0E2L,0x28CA0961L,0xA47450F3L,
               0xAA459FC1L,0x44560ED3L,0x8BA0A734L,0x885C73FAL,0x3364A921L,
               0xC7FF25D0L,0x4C180D21L,0xABC77C0DL,0x1D267B9CL,0xAA3C02F8L,
               0x4D144A1AL,0x2DC64D2AL,0x557B1422L,0xCC0CC62BL,0xADA36F3EL,
               0x0F68F563L,0xDC5493C4L,0x4E705269L,0x6CD54A5DL,0x209D75A7L,
               0xB3C0EB71L,0x1E67B5B3L,0x6A9B0D97L,0xA065CB06L,0xE9DF0939L,
               0x4EF1FF28L,0xC8E487DDL,0x70B1E970L,0x7F610F0CL,0xCD1E0198L,
               0x75D0F83EL,0x9FA0DE06L,0x4397DCFAL,0x9CDEEA23L,0x714F21BAL,
               0x1D22AB97L,0x776070F4L,0xB9129ADAL,0xDD513131L,0xEA374F43L,
               0xFABCEC09L,0x26F9218CL,0x770718D4L,0x06E0B90BL,0xDCAB3E3AL,
               0xF2638059L,0x7130C682L,0x721B0CEEL,0xFE2E4865L,0x1E71B531L,
               0xDA0C4C5BL,0x4DEA15DCL,0x7F4439BCL,0x8801B225L,0x736CA920L,
               0x575D06C5L,0x4FED022EL,0x42359734L,0x7B5FDD1FL,0xD142CEE9L,
               0x7E29929BL,0x6BFC425EL,0xC1D5D81AL,0x9ADB6D09L,0xEDF90924L,
               0xD457C583L,0x68FEAB30L,0xA2F7D344L,0xBA5C47C8L,0x9B351FC4L,
               0x6DAA5552L,0x19A7119AL,0xD8525C77L,0xB2B54020L,0xA2EAD69EL,
               0xDFF926FCL,0x54EB4941L,0x18CA3987L,0x46CD2EE6L,0xF5CF0269L,
               0x1F08FD16L,0xDE710909L,0x39244E59L,0x5A38C5F1L,0x3AB89807L,
               0xE2ACB0A7L,0x8B2D45C8L,0x1E445094L,0xA40BFB03L,0x567A2E40L,
               0x2DBA1352L,0x22D4B242L,0x7ED1242BL,0x09DB7304L,0x1EEAB8F6L,
               0x8507FB0CL,0x865B355DL,0x1CDF90D3L,0x5EAD25A7L,0x47CDFBE1L,
               0xFE581E9BL,0x8D5794BEL,0xEEF47872L,0x5816D3D1L,0xB5E8CDE4L,
               0x3E7250B4L,0xDCDD9459L,0xB80392DDL,0xDB1B5448L,0x4E20F2C5L,
               0x1A4A864BL,0x687318F3L,0x31D2B2C8L,0xAE726CE1L,0xA75B5F48L,
               0x77867515L,0xF63CE753L,0x5C46BD19L,0x94FEE060L,0xC53CAA53L,
               0x0A19E3F9L,0x5ABEC56BL,0xD0255894L,0x7366A6ABL,0x7D9AB7BAL,
               0xB7BAB5BAL,0x6AEF98D3L,0x8032791FL,0x20AD6277L,0x64587C23L,
               0x454CBE2DL,0xDC93248EL,0x3144E45EL,0x6F7A0AB8L,0x7E3D9C92L,
               0x8786A518L,0x938E4E51L,0xBA3F7F47L,0x07B15113L,0x811CFEBEL,
               0x435C6F50L,0x6487BBF3L,0xEFD8EEAFL,0xDB362E7EL,0x52BC6779L,
               0x6E72CFB9L,0x16725146L,0x84E3163AL,0xB1A036AEL,0xB6F1AA8BL,
               0xACBF8C09L
               /* End   of S Box 12  */  },
            {  /* Start of S Box 13  */
               0x94075E15L,0x08FD6294L,0x9201D1C0L,0xAE81DA17L,0x7ABAD8C5L,
               0x62E80A27L,0xB48ACB8BL,0xBDCB6A3DL,0xC808328AL,0x358082DFL,
               0x3FAE956AL,0x33B1DA77L,0x8FF37D73L,0x50C4EABBL,0x126F506DL,
               0x12332BE5L,0x9E8BA790L,0xE3A423ABL,0x6DDDFB84L,0x5A716B77L,
               0x16CEB2C1L,0xEE215CC0L,0x10E676D0L,0x094C7C2EL,0xF5AEAD27L,
               0x325A5519L,0x2C9CD140L,0x01C37E09L,0x3D2A88A1L,0xFE1F4E76L,
               0x90EE2CD4L,0x71E65FF8L,0xDD52444FL,0x12802807L,0x7CED588CL,
               0x49A5211BL,0xE516E012L,0x9EADD2BDL,0xB1876469L,0x8901C353L,
               0x56876CF9L,0x83577C86L,0x5DCE5069L,0x121955CFL,0x2FA4C804L,
               0xC0BDF684L,0x52A13D8FL,0x13CDC85FL,0x605D1543L,0x75BF51A7L,
               0xCF30F9C8L,0x7C1D2A03L,0xF9B34296L,0x925EACEDL,0x855C9636L,
               0x4E0A6EDCL,0xFBB45F1EL,0x08A9D748L,0xC23413C7L,0x68A371C9L,
               0x54525CBAL,0xE690E3D7L,0x58A8817DL,0xFAE884EAL,0x269C2A7AL,
               0x2C23DE8AL,0x47B8DF48L,0xE4C96820L,0x5183E86FL,0xCA52BA52L,
               0xAD750D46L,0x46473C89L,0xE3169667L,0xD12E664EL,0x6CDE1B69L,
               0xF171E007L,0xDE451494L,0x40A6035EL,0x93E319A0L,0x144775B9L,
               0x212083F6L,0x17CA9F92L,0x1284F80CL,0x9FB9EA80L,0xEC97D16BL,
               0x469BFCFBL,0x09D1B79BL,0x83D84F7AL,0x00BB0005L,0xDEF84987L,
               0x59CB564FL,0xCF838499L,0x8C8B20C3L,0xDE017728L,0x0161E617L,
               0x73EAA9CCL,0x60E71FA5L,0x36B684FEL,0x21944504L,0x8FEDAF24L,
               0xBCFFFFBBL,0xF60782E7L,0xA972A524L,0x039D95A0L,0x61A4BFD6L,
               0x2F0560F5L,0x8AFDD569L,0xD0D96C5FL,0x7EF56025L,0xB09F2F37L,
               0xC314F6C2L,0x15C11224L,0xF3F2F0A8L,0xA9D5CD9DL,0x74E7E740L,
               0xB255B91CL,0xC08C6130L,0xFAC85C18L,0x8E57D8F1L,0xC8962029L,
               0x05C1B2FCL,0xA447BBB7L,0xFF6399C7L,0x2563875AL,0xB3A3E3DBL,
               0xC5720B8AL,0xC91B3AB1L,0xFBECCFBFL,0xB93205D0L,0x40494A61L,
               0x0B7FCCCFL,0x3A01F638L,0x286C0708L,0x22BC5A8CL,0x96703E02L,
               0xC0D3FED6L,0xFE33E945L,0x7DDC5BBCL,0xA81D9069L,0xCF31E798L,
               0x2DA6BBB6L,0x2F983B00L,0x0356C3E4L,0x164BCF8EL,0xB4B56E5BL,
               0x3BF0FC89L,0xC74BE583L,0xE5017898L,0xB370F105L,0xAEF5AC25L,
               0xF2DDE857L,0x0D5411C7L,0x1BD864E2L,0x7AA63EF6L,0x860ADB4FL,
               0x9C539A2AL,0xDBDCB7E4L,0xBDE3AFDBL,0x82D49F5BL,0x74FEF491L,
               0x038CFB0AL,0x59DCF1E4L,0xD65B438BL,0xD5353E3CL,0x83C81F55L,
               0x6E723312L,0x927DD7E0L,0x8D1968FCL,0x7CB113B4L,0xCAA34564L,
               0xB73C5B69L,0xAEA972E0L,0xDC460747L,0x9F8157EAL,0x3497AF06L,
               0x27D56CF8L,0x8587CC0EL,0xCC0C3A84L,0x29AFE4C7L,0x08AD3715L,
               0xA76570D9L,0x5222FD42L,0x764209BDL,0x4212F275L,0x20EE15D9L,
               0x5FF5A025L,0x2C921EC7L,0xF3339DEAL,0x02048BFDL,0xA564333CL,
               0x3A9EF4F5L,0x0DF01983L,0x4CC7E035L,0x637FA758L,0xB026C947L,
               0x70597541L,0xFE3526A2L,0x9A270BB7L,0x9F6B7FCEL,0x4A5FC122L,
               0x09404C22L,0x187A4F2BL,0xD54C1558L,0x8D01FD28L,0x7CF753D7L,
               0x0F7A63B3L,0x74D98C28L,0x285F0A52L,0x575A39B1L,0x5E17687FL,
               0x8BF2E30BL,0x2B4906A1L,0x8A4900AEL,0x065F5D50L,0xEBC82A12L,
               0x87D0D314L,0x26E5B7C0L,0x25442264L,0xC339413EL,0x5A1391BAL,
               0x293D5E06L,0x9DD5D78EL,0xF258589EL,0x86F23E66L,0x9611D660L,
               0x7D026DDBL,0x8B024003L,0x1A7DCC45L,0x6AA23EFFL,0xC6DCD34CL,
               0x8B6818ABL,0x07A53A49L,0x96DE7691L,0x665139D2L,0xE46BC058L,
               0x6C697871L,0x2BA79E68L,0x7F637F9CL,0xB50A6949L,0x29D9B7BDL,
               0x181CA763L,0xE022A67AL,0xDE56E14FL,0x4EE5C52DL,0x7E3E9073L,
               0xBA9CAD7DL,0x6F4D4A78L,0xCBAEC4C2L,0x3BFC69B7L,0xFB939494L,
               0x59D183B6L,0xB1F3A47BL,0x5155221FL,0x95466BEFL,0xAB01BD38L,
               0x0FF46358L
               /* End   of S Box 13  */  },
            {  /* Start of S Box 14  */
               0x53781A4FL,0x9D839EA5L,0x051420C8L,0x4EBB334BL,0xD8F9689DL,
               0x54505060L,0x43707B35L,0x46DA6AADL,0xDE20BF29L,0x9DD20B69L,
               0x0281CEE0L,0xBFBF9A3DL,0x460C0FF2L,0x3709AF90L,0x22480BC7L,
               0x9A49A30AL,0x2FAD39E8L,0x52F53D03L,0xC4B331DCL,0xF688B604L,
               0x79F36C1BL,0xEE87B494L,0xD6D4512EL,0xC37CB13BL,0x44FF887CL,
               0xFCDE3562L,0x5ABA799DL,0x0125B6A0L,0x81AF9D1AL,0x6BEAAE6DL,
               0x40468B4AL,0xB184A542L,0x3F16ACD5L,0x5B9B11A6L,0xB6978634L,
               0xD1988C11L,0x5E3193DEL,0xBC026D0BL,0x9E8C7B4BL,0x74515D1DL,
               0xFE018404L,0x9F2EB2AFL,0xE738689FL,0x87D01747L,0x00977F66L,
               0xD3FFF17FL,0xB1984F11L,0x1B03DABEL,0x83C24207L,0x9875298CL,
               0xEDAE1FC0L,0x01EC8652L,0x77B31FD4L,0xDEB15AB7L,0x9979D9ACL,
               0x787B2C33L,0xEC58D5DFL,0x76A2742FL,0xC7F99BC5L,0x50CEBB32L,
               0xB3B38407L,0x9F4888D4L,0x562E471CL,0xAAC7437EL,0x1BB33D7CL,
               0x19A385A7L,0x6709EC90L,0x7575C5E7L,0xD478CE1EL,0x25B48BB7L,
               0xE9A97B51L,0xA1D83F8EL,0x1EF359EEL,0x917A7AE4L,0xDCEE023FL,
               0x5EF1A361L,0xBA13EEEBL,0x8FE5536EL,0x40E9835BL,0x8DCEF062L,
               0xD6F85B25L,0xEE052406L,0x15A8FDB3L,0x1C4147A0L,0x7582C28DL,
               0xBFEBF0EAL,0x6C0D513AL,0x2D7A972CL,0x74C22222L,0xF076A4FCL,
               0x2427BDFDL,0xA067CFC4L,0xF5B67B04L,0xB3B6513CL,0x6DF703EDL,
               0x930AF2AAL,0xE660CE03L,0xE8BB08A8L,0x187B420BL,0x08521DBDL,
               0x59C0F94FL,0x8D669852L,0x34B57B56L,0xEE5F22FEL,0x2ED42EB9L,
               0x94B62239L,0xC0A5ACEFL,0x3512207BL,0x739E5D60L,0x2CDBA44DL,
               0xCF49C7A4L,0xFE892726L,0x3A1D6434L,0x2595108FL,0x4E93DB97L,
               0x38A6170EL,0x72417546L,0x9F15850DL,0x3FA2D9C7L,0xD379F114L,
               0x1C2B7F84L,0x7B4AE3BAL,0x9156DF15L,0xFF00C456L,0x36CA62F0L,
               0xD8243B93L,0x74CFCFAFL,0x9D3D90D6L,0xC20E5FA9L,0xA6C37CA9L,
               0xB9FEA877L,0x9B3F5684L,0xCF072480L,0xE508B6FDL,0x7EC27B6CL,
               0x1BD6046EL,0x4EB6F394L,0xA602E97EL,0xB55B469EL,0x0D33BC86L,
               0x3D2ACCD5L,0xC8C1062CL,0x5E990BFEL,0x6F344EDAL,0x9E337D53L,
               0x6B251C08L,0x778DB98BL,0x430BD85DL,0x6001280EL,0x7F4F2B21L,
               0x02365145L,0x98786C0CL,0xB494CCE7L,0x051E1377L,0x0E94F14CL,
               0x5EA9D9E9L,0x78CD59FDL,0xFC9205FAL,0x7BA88B71L,0x86704F03L,
               0xAEBBE040L,0x64DBFFAAL,0x69600FD3L,0x2F0CAE5AL,0x455F50B1L,
               0x4EBAC398L,0xC9EE7B72L,0x495DF7EEL,0x6DA7C97EL,0xA85E91B3L,
               0x9A11E13EL,0xD4443990L,0xF7A53B59L,0x64A7570CL,0xECFA4F38L,
               0xE1FF748FL,0xF2688661L,0xA1B50781L,0xA088986FL,0x5E50A9ABL,
               0x6ED0DBB7L,0x6079B044L,0xC5CAD9D3L,0x4D77C6F4L,0x5BFE193AL,
               0x8FE25334L,0x7BC31474L,0xAE32DD8BL,0xBAC140FAL,0x2000EE51L,
               0x82704743L,0x7FB3ED3FL,0x8A5971D7L,0x33D363ACL,0x1AE3654EL,
               0xC2E9E720L,0xCB968BF1L,0xB66CE133L,0x14FB7B77L,0x76D4DA5EL,
               0x8D899E49L,0xABAA58F8L,0x8FC88BEEL,0xBB64C5AAL,0xA130AADEL,
               0x119DB9EBL,0x7B4A8381L,0x52BA9B33L,0x568D9F80L,0xF845031AL,
               0xC9727652L,0x6AB569F9L,0x6C908E70L,0x40A35667L,0x976F5160L,
               0xF37531ECL,0xE357669CL,0x0AB682F1L,0xD701369CL,0xFDDBE21DL,
               0xECD437E6L,0x457EB8B8L,0x99590404L,0x69F69C6BL,0x56F6027EL,
               0xF1EBE7BDL,0xCB55CBAAL,0x55C63FF6L,0x41DEB632L,0x1E1EFFDFL,
               0x6F077B8DL,0xB35BCCCFL,0x9D6A6324L,0xADF6003EL,0x747E259EL,
               0x838542B4L,0x6BBC1943L,0xBD830ACAL,0xFA9BC8DBL,0xC375C1E7L,
               0xAAB3A8AFL,0x3ED5FE93L,0xF17C5446L,0x851A3A47L,0x6A5F3138L,
               0x20CDEB8AL,0x6AF3D9EEL,0xA8749BD5L,0x8BC1B3FFL,0x8479C2BEL,
               0x443058C3L,0x4C74E6AFL,0x2ED82EE3L,0x69EC9110L,0xAF21C0D6L,
               0x62393BA7L
               /* End   of S Box 14  */  },
            {  /* Start of S Box 15  */
               0x1D5BEEF3L,0xB9A5D8FEL,0xC86C5AF3L,0xA6DBD37FL,0x805F9F90L,
               0xE537DBB1L,0xEFBEF431L,0x0D32464DL,0x6C001050L,0x7865BEA9L,
               0x7FBD7D84L,0xCD46AA4FL,0xA05D8245L,0x644F81B5L,0x6758D60CL,
               0xE5E7DD75L,0x4C47E34FL,0xC8B535F6L,0xB79020A7L,0x7533F1BFL,
               0x40DC158CL,0x85D8C94AL,0x70834A67L,0x6E2C052EL,0xBD1E37DEL,
               0xA9973DC3L,0x8003544BL,0xBFB1E9B1L,0x72EC3A44L,0x26F29260L,
               0x093F4F53L,0x68E2BE68L,0xAF661CEEL,0x1C09D632L,0xEB082A8FL,
               0x89CF6344L,0x449DEFADL,0x59BDFB17L,0x348CD5D0L,0x0349F834L,
               0x325F9290L,0x0F0CF121L,0xD69D7055L,0xE56F7D58L,0x98CE2697L,
               0x2DF8E65FL,0xE05A0BEEL,0x3F31C3A0L,0x56DA99C2L,0x9391BCB1L,
               0x52B467ABL,0xD1A0090CL,0x7C81FD12L,0x71D56228L,0x3DC9B38DL,
               0xEBBA3F9DL,0x0BE52185L,0xB8B6F8C3L,0x6F6BE093L,0x6E613542L,
               0xE2E5462DL,0x67337E82L,0x0AB9FCCDL,0x39B42C1BL,0x31815AEAL,
               0x6E6DC565L,0x0EB4E90BL,0x7DB4F319L,0xF8CA3FCAL,0xDD321C7DL,
               0x794AC68EL,0x185FAA2AL,0x08AE04CFL,0xA5A433A9L,0x3D8D8315L,
               0x1C973160L,0x803FBAF8L,0xD5B24BF9L,0x695C21D0L,0x788CB9DBL,
               0x606D4F35L,0x7D7C226DL,0xFDE8AEB0L,0x4C0C1059L,0xA846C5A7L,
               0x7EC30A06L,0xF90F1CB3L,0x7939580CL,0x59BC2C4DL,0xD6D6B3C3L,
               0x4ED36BDBL,0x0E0281D3L,0x62DE62F7L,0x97754BA4L,0x0B439927L,
               0x0B8799CEL,0xD39C89D5L,0xC0B0C4A9L,0x2050B898L,0x50A882DDL,
               0xACF79FD7L,0x52A82DD3L,0xBE08A72CL,0x1E665B11L,0xDE0D770EL,
               0x4B3B7621L,0x946D86E5L,0x43C00579L,0x78A04E49L,0x9D7C9F93L,
               0xE26E46B2L,0xC1C6AEF5L,0x7AF006B9L,0x5848A929L,0x980CF6B4L,
               0xB87928A6L,0xD4FB9E4CL,0x6AC093E4L,0xB64576D9L,0x06C7827BL,
               0xA78316F4L,0xE5E57DC3L,0x2D3BE623L,0xBDB2DC64L,0xD2C77ED0L,
               0xD8B628C6L,0x0CCD3682L,0xCC59F771L,0x65A7B7D0L,0x1314C2EDL,
               0x43FC7714L,0x438D02A2L,0x5024E0F5L,0xC62D4D39L,0xF3C798DBL,
               0x236D0D08L,0xA45CF93EL,0xD2D3B9A8L,0xFB4D89A6L,0x6BF9D882L,
               0x6021E2ABL,0x5625F43DL,0x6B617BA5L,0x1B119420L,0x8493BC2CL,
               0x222320E5L,0x43203DC8L,0x13E530F3L,0x00A076D1L,0x66CE3CC1L,
               0x738CEF00L,0x8456BCE9L,0xF56A00BBL,0x030538C2L,0x1C93716CL,
               0x5C6328F4L,0x41B08AA9L,0x08E804E7L,0x0D48E4FCL,0x9E1C6524L,
               0xF7B2F5CCL,0x7577C121L,0x769836A0L,0x37719277L,0x04613002L,
               0x4BA36E9EL,0x38957979L,0x3873BFFFL,0x7AAB6B6DL,0x787BEB20L,
               0x612D9D66L,0x9211AC9BL,0x579276FCL,0x0EEF59D6L,0xE3639087L,
               0x546B8A5BL,0x9E2662AFL,0x0C0FA6A1L,0xEE4474CCL,0x8F42474FL,
               0x4C554E77L,0x63CCD4AFL,0x30D24826L,0x11D4E558L,0x43310A62L,
               0x2124E3C2L,0xFBFBFCB4L,0x0C136485L,0xB1B9A683L,0x4829E109L,
               0x1EC28156L,0x5FEFD2D6L,0x89DD23C7L,0xD8DBC065L,0x9934052DL,
               0x1A66214CL,0xB89E902FL,0xBF288FF4L,0x7D535A27L,0x3E8A50E6L,
               0x5FFADDACL,0x0F122EA8L,0xC80D9036L,0xBA4A70A4L,0x4F030A5DL,
               0xC7A8BD70L,0x7C65B579L,0xAEA67F86L,0xA7B92812L,0xD7EA2D6CL,
               0x8978CBD1L,0xEABE5F9CL,0x780A160CL,0x5FB97D8DL,0xFD18A15CL,
               0x9F631FC7L,0x9008F42AL,0x3034ADC2L,0xDA6377FCL,0xBBB4BF16L,
               0x21A2B35CL,0x896BCE2BL,0x1F4C3EC0L,0x20B14089L,0x1AE851B2L,
               0x2A2ECEA8L,0xBCE0C5B9L,0x0E5AB111L,0x6CCBE03DL,0x339EAF5AL,
               0xB2114CC5L,0x438002DCL,0x255A5FDBL,0xA5AA6120L,0x2FFDA3F6L,
               0xE17243BCL,0x3775AFAEL,0x7F62121AL,0xF577EA5CL,0xE7F055B1L,
               0xB14CDE96L,0xB1B6A428L,0x249C02D5L,0x552B57E9L,0xA4CFFEA1L,
               0x5CB7166BL,0xBA5E1A73L,0x2CF14936L,0xEEDFEED1L,0x5E5368C2L,
               0xB9CC0345L,0x5B640BA7L,0x919BCF35L,0xB9ACAA4DL,0x10D6DB2BL,
               0xF284BE3CL
               /* End   of S Box 15  */  }
        };

//-----------------------------------------------------------------------------
void snefruHash512(dword output[4], dword input[SNEFRU_INPUT_BLOCK_SIZE])
{
    static int shiftTable[4] = { 16, 8, 16, 24 };
    dword	SBE;
    int     shift, leftShift;
    int     index;
    int     byteInWord;
    dword	*SBox0;
    dword	*SBox1;
    dword	B00,B01,B02,B03,B04,B05,B06,B07,B08,B09,B10,B11,B12,B13,B14,B15;

    /* initialize the block to be encrypted from the input  */
    B00 = input[0];
    B01 = input[1];
    B02 = input[2];
    B03 = input[3];
    B04 = input[4];
    B05 = input[5];
    B06 = input[6];
    B07 = input[7];
    B08 = input[8];
    B09 = input[9];
    B10 = input[10];
    B11 = input[11];
    B12 = input[12];
    B13 = input[13];
    B14 = input[14];
    B15 = input[15];


    for (index = 0; index < 8; index++) {
        SBox0 = standardSBoxes[2*index+0];
        SBox1 = standardSBoxes[2*index+1];
        for (byteInWord = 0; byteInWord < 4; byteInWord++) {
            round(B15,B00,B01,SBox0);
            round(B00,B01,B02,SBox0);
            round(B01,B02,B03,SBox1);
            round(B02,B03,B04,SBox1);
            round(B03,B04,B05,SBox0);
            round(B04,B05,B06,SBox0);
            round(B05,B06,B07,SBox1);
            round(B06,B07,B08,SBox1);
            round(B07,B08,B09,SBox0);
            round(B08,B09,B10,SBox0);
            round(B09,B10,B11,SBox1);
            round(B10,B11,B12,SBox1);
            round(B11,B12,B13,SBox0);
            round(B12,B13,B14,SBox0);
            round(B13,B14,B15,SBox1);
            round(B14,B15,B00,SBox1);
            /*
             * Rotate right all 32-bit words in the entire block
             * at once.
             */
            shift = shiftTable[byteInWord];
            leftShift = 32-shift;
            rotate(B00);
            rotate(B01);
            rotate(B02);
            rotate(B03);
            rotate(B04);
            rotate(B05);
            rotate(B06);
            rotate(B07);
            rotate(B08);
            rotate(B09);
            rotate(B10);
            rotate(B11);
            rotate(B12);
            rotate(B13);
            rotate(B14);
            rotate(B15);
        };		/* end of byteInWord going from 0 to 3 */
    };			/* end of index going from 0 to 8-1 */
    output[0] = input[0] ^ B15;
    output[1] = input[1] ^ B14;
    output[2] = input[2] ^ B13;
    output[3] = input[3] ^ B12;
}


