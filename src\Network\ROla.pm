package Network::Send::ROla;
use strict;
use base qw(Network::Send::ServerType0);
use Globals qw($net %config);
use Utils qw(getTickCount);
use Log qw(debug);

# Declaração correta da tabela como variável de pacote
my @fletcher_table = (
    0x00, 0x1F, 0x3E, 0x21, 0x7C, 0x63, 0x42, 0x5D, 0xF8, 0xE7, 0xC6, 0xD9, 0x84, 0x9B, 0xBA, 0xA5,
    0xF1, 0xEE, 0xCF, 0xD0, 0x8D, 0x92, 0xB3, 0xAC, 0x09, 0x16, 0x37, 0x28, 0x75, 0x6A, 0x4B, 0x54,
    0xE3, 0xFC, 0xDD, 0xC2, 0x9F, 0x80, 0xA1, 0xBE, 0x1B, 0x04, 0x25, 0x3A, 0x67, 0x78, 0x59, 0x46,
    0x12, 0x0D, 0x2C, 0x33, 0x6E, 0x71, 0x50, 0x4F, 0xEA, 0xF5, 0xD4, 0xCB, 0x96, 0x89, 0xA8, 0xB7,
    0xC7, 0xD8, 0xF9, 0xE6, 0xBB, 0xA4, 0x85, 0x9A, 0x3F, 0x20, 0x01, 0x1E, 0x43, 0x5C, 0x7D, 0x62,
    0x36, 0x29, 0x08, 0x17, 0x4A, 0x55, 0x74, 0x6B, 0xCE, 0xD1, 0xF0, 0xEF, 0xB2, 0xAD, 0x8C, 0x93,
    0x24, 0x3B, 0x1A, 0x05, 0x58, 0x47, 0x66, 0x79, 0xDC, 0xC3, 0xE2, 0xFD, 0xA0, 0xBF, 0x9E, 0x81,
    0xD5, 0xCA, 0xEB, 0xF4, 0xA9, 0xB6, 0x97, 0x88, 0x2D, 0x32, 0x13, 0x0C, 0x51, 0x4E, 0x6F, 0x70,
    0x8F, 0x90, 0xB1, 0xAE, 0xF3, 0xEC, 0xCD, 0xD2, 0x77, 0x68, 0x49, 0x56, 0x0B, 0x14, 0x35, 0x2A,
    0x7E, 0x61, 0x40, 0x5F, 0x02, 0x1D, 0x3C, 0x23, 0x86, 0x99, 0xB8, 0xA7, 0xFA, 0xE5, 0xC4, 0xDB,
    0x6C, 0x73, 0x52, 0x4D, 0x10, 0x0F, 0x2E, 0x31, 0x94, 0x8B, 0xAA, 0xB5, 0xE8, 0xF7, 0xD6, 0xC9,
    0x9D, 0x82, 0xA3, 0xBC, 0xE1, 0xFE, 0xDF, 0xC0, 0x65, 0x7A, 0x5B, 0x44, 0x19, 0x06, 0x27, 0x38,
    0x48, 0x57, 0x76, 0x69, 0x34, 0x2B, 0x0A, 0x15, 0xB0, 0xAF, 0x8E, 0x91, 0xCC, 0xD3, 0xF2, 0xED,
    0xB9, 0xA6, 0x87, 0x98, 0xC5, 0xDA, 0xFB, 0xE4, 0x41, 0x5E, 0x7F, 0x60, 0x3D, 0x22, 0x03, 0x1C,
    0xAB, 0xB4, 0x95, 0x8A, 0xD7, 0xC8, 0xE9, 0xF6, 0x53, 0x4C, 0x6D, 0x72, 0x2F, 0x30, 0x11, 0x0E,
    0x5A, 0x45, 0x64, 0x7B, 0x26, 0x39, 0x18, 0x07, 0xA2, 0xBD, 0x9C, 0x83, 0xDE, 0xC1, 0xE0, 0xFF
);

sub new {
    my ($class) = @_;
    my $self = $class->SUPER::new(@_);
    
    my %packets = (
        '0C26' => ['master_login', 'a4 Z51 a32 a5', [qw(game_code username password_rijndael flag)]],
        '0825' => ['token_login', 'v V C Z51 a17 a15 a*', [qw(len version master_version username mac_hyphen_separated ip token)]],
        '0436' => ['map_login', 'a4 a4 a4 V V C', [qw(accountID charID sessionID unknown tick sex)]],
        '0AEF' => ['otp_token', 'v a*', [qw(len token)]],
    );

    $self->{packet_list}{$_} = $packets{$_} for keys %packets;

    my %handlers = qw(
        master_login 0C26
        token_login 0825
        char_create 0A39
        map_login 0436
        sync 0360
        otp_token 0AEF
    );

    $self->{packet_lut}{$_} = $handlers{$_} for keys %handlers;
    $self->{char_create_version} = 0x0A39;
    $self->{cryptKey} = 0;         # Chave inicial de criptografia
    $self->{useChecksum} = 0;      # Flag para ativar checksum
    
    return $self;
}

# Ativa o uso de checksum nos pacotes
sub enableChecksum {
    my ($self) = @_;
    $self->{useChecksum} = 1;
}

# Calcula o checksum conforme lógica do C++
sub computeChecksum {
    my ($self, $packet) = @_;
    my $cryptKey = $self->{cryptKey};
    
    # Extrai bytes da chave
    my $b0 = $cryptKey & 0xFF;
    my $b1 = ($cryptKey >> 8) & 0xFF;
    my $b2 = ($cryptKey >> 16) & 0xFF;
    my $b3 = ($cryptKey >> 24) & 0xFF;
    
    # Calcula valor inicial
    my $checksum = $fletcher_table[$b3 ^ $fletcher_table[$b2 ^ $fletcher_table[$b1 ^ $fletcher_table[$b0]]]];
    
    # Processa cada byte do pacote
    foreach my $byte (unpack('C*', $packet)) {
        $checksum = $fletcher_table[$checksum ^ $byte];
    }
    
    # Atualiza chave para próximo pacote
    $self->{cryptKey} = $cryptKey + 1;
    
    return $checksum;
}

# Sobrescreve o envio para adicionar checksum quando necessário
sub sendToServer {
    my ($self, $packet) = @_;
    
    if ($self->{useChecksum}) {
        # Calcula e anexa o checksum
        my $checksum = $self->computeChecksum($packet);
        $packet .= pack('C', $checksum);
        
        # Atualiza campo de comprimento se existir
        if (length($packet) > 2) {
            my $header = substr($packet, 0, 2);
            my $len = unpack('v', $header);
            $len++;
            substr($packet, 0, 2) = pack('v', $len);
        }
    }
    
    $self->SUPER::sendToServer($packet);
}

# Envia token OTP com checksum
sub sendOTPToken {
    my ($self, $token) = @_;
    
    my $msg = $self->reconstruct({
        switch => 'otp_token',
        len => length($token),
        token => $token,
    });
    
    $self->sendToServer($msg);
    debug "Sent OTP token with checksum\n", "sendPacket", 2;
}

1;