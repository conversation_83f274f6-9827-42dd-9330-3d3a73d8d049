package BGM;
 
use strict;
use Plugins;
use Globals;
use Log qw(message warning error);
use Field;
my $dataDir = $Plugins::current_plugin_folder;
use FindBin qw($RealBin);
use lib "$RealBin/plugins/mediaClient";
use client;

my %BGM;
my $previousField;
my $pathTo = 'sounds/BGM/';

Plugins::register('BGM', 'plays background music', \&Unload);

my $hooks = Plugins::addHooks(
	['packet/map_change', \&onMapChange, undef],
	['packet/map_changed', \&onMapChange, undef],
	['packet/received_character_ID_and_Map', \&onMapChange, undef],
	['packet_outMangle/0181', \&onQuit, undef],
);

sub Unload {
	Plugins::delHooks($hooks);
	client->getInstance()->quit;
}

sub onMapChange {
	my ($packet, $args) = @_;
	my $field;
	
	if ($packet =~ /map_change/) {
		$field = $args->{map};
	} elsif ($packet =~ /ID_and_Map/) {
		$field = $args->{mapName};
	}
	$field =~ s/.gat//;
	($field, undef) = Field::nameToBaseName(undef, $field); # Clean Up Instance ID
	
	if (!$previousField) {
		$previousField = $field;
		my $file = $pathTo . $BGM{$field};
		client->getInstance()->play($file, 'BGM');
	} elsif ($previousField ne $field) {
		$previousField = $field;
		my $file = $pathTo . $BGM{$field};
		client->getInstance()->play($file, 'BGM');
	}
}

sub onQuit {
	client->getInstance()->quit;
}

%BGM = (
	alde_tt03 => '27.ogg',
	auction_01 => '08.ogg',
	auction_02 => '70.ogg',
	que_job03 => '06.ogg',
	que_job02 => '06.ogg',
	que_job01 => '11.ogg',
	abyss_01 => '49.ogg',
	abyss_02 => '49.ogg',
	abyss_03 => '49.ogg',
	tha_t01 => '88.ogg',
	tha_t02 => '88.ogg',
	tha_t03 => '88.ogg',
	tha_t04 => '88.ogg',
	tha_t05 => '89.ogg',
	tha_t06 => '89.ogg',
	tha_t07 => '89.ogg',
	tha_t08 => '89.ogg',
	tha_t09 => '89.ogg',
	tha_t10 => '89.ogg',
	tha_t11 => '89.ogg',
	tha_t12 => '89.ogg',
	thana_step => '89.ogg',
	thana_boss => '40.ogg',
	thana_scene01 => '88.ogg',
	job_soul => '47.ogg',
	job_star => '65.ogg',
	hu_fild07 => '72.ogg',
	hu_fild05 => '04.ogg',
	hu_fild04 => '72.ogg',
	hu_fild01 => '06.ogg',
	yuno_fild06 => '72.ogg',
	quiz_02 => '18.ogg',
	jupe_cave => '89.ogg',
	juperos_01 => '89.ogg',
	juperos_02 => '89.ogg',
	jupe_gate => '89.ogg',
	jupe_area1 => '89.ogg',
	jupe_area2 => '89.ogg',
	jupe_ele => '89.ogg',
	jupe_ele_r => '89.ogg',
	jupe_core => '89.ogg',
	lighthalzen => '90.ogg',
	lhz_in01 => '90.ogg',
	lhz_in02 => '90.ogg',
	lhz_in03 => '90.ogg',
	lhz_cube => '92.ogg',
	lhz_que01 => '92.ogg',
	lhz_airport => '90.ogg',
	airplane_01 => '90.ogg',
	lhz_dun01 => '92.ogg',
	lhz_dun02 => '92.ogg',
	lhz_dun03 => '92.ogg',
	lhz_fild01 => '91.ogg',
	yuno_pre => '70.ogg',
	y_airport => '70.ogg',
	lhz_fild03 => '91.ogg',
	lhz_fild02 => '91.ogg',
	ein_fild04 => '87.ogg',
	ein_fild03 => '87.ogg',
	que_sign02 => '85.ogg',
	que_sign01 => '84.ogg',
	ein_dun02 => '88.ogg',
	ein_dun01 => '88.ogg',
	ein_fild10 => '87.ogg',
	ein_fild09 => '87.ogg',
	ein_fild08 => '87.ogg',
	ein_fild07 => '87.ogg',
	ein_fild06 => '87.ogg',
	airplane => '87.ogg',
	airport => '86.ogg',
	ein_in01 => '86.ogg',
	einbech => '86.ogg',
	einbroch => '86.ogg',
	nif_fild02 => '85.ogg',
	nif_fild01 => '85.ogg',
	nif_in => '84.ogg',
	niflheim => '84.ogg',
	yggdrasil01 => '83.ogg',
	turbo_e_16 => '75.ogg',
	turbo_e_8 => '75.ogg',
	turbo_e_4 => '75.ogg',
	turbo_n_16 => '18.ogg',
	turbo_n_8 => '18.ogg',
	turbo_n_4 => '18.ogg',
	turbo_n_1 => '18.ogg',
	turbo_room => '52.ogg',
	yuno_fild12 => '71.ogg',
	yuno_fild11 => '71.ogg',
	yuno_fild09 => '71.ogg',
	yuno_fild08 => '71.ogg',
	yuno_fild07 => '72.ogg',
	yuno_fild05 => '72.ogg',
	ayo_in02 => '81.ogg',
	ayo_in01 => '81.ogg',
	ayo_dun02 => '82.ogg',
	ayo_dun01 => '82.ogg',
	ayo_fild02 => '82.ogg',
	ayo_fild01 => '81.ogg',
	ayothaya => '81.ogg',
	que_god02 => '50.ogg',
	que_god01 => '70.ogg',
	quiz_test => '28.ogg',
	gefenia04 => '44.ogg',
	gefenia03 => '44.ogg',
	gefenia02 => '44.ogg',
	gefenia01 => '44.ogg',
	himinn => '09.ogg',
	jawaii_in => '28.ogg',
	jawaii => '28.ogg',
	lou_dun03 => '80.ogg',
	lou_dun02 => '80.ogg',
	lou_dun01 => '80.ogg',
	lou_fild01 => '79.ogg',
	lou_in02 => '79.ogg',
	lou_in01 => '79.ogg',
	louyang => '79.ogg',
	valkyrie => '09.ogg',
	um_dun02 => '69.ogg',
	um_dun01 => '69.ogg',
	um_fild04 => '78.ogg',
	um_fild03 => '63.ogg',
	um_fild02 => '78.ogg',
	um_fild01 => '63.ogg',
	um_in => '68.ogg',
	umbala => '68.ogg',
	ama_dun01 => '77.ogg',
	ama_dun02 => '77.ogg',
	ama_dun03 => '77.ogg',
	ama_in01 => '76.ogg',
	ama_in02 => '76.ogg',
	ama_test => '77.ogg',
	amatsu => '76.ogg',
	ama_fild01 => '73.ogg',
	gon_dun01 => '75.ogg',
	gon_dun02 => '75.ogg',
	gon_dun03 => '75.ogg',
	gon_fild01 => '73.ogg',
	gon_in => '74.ogg',
	gonryun => '74.ogg',
	gon_test => '75.ogg',
	sec_in01 => '70.ogg',
	sec_in02 => '70.ogg',
	sec_pri => '42.ogg',
	alde_alche => '39.ogg',
	yuno_in05 => '70.ogg',
	yuno_in04 => '70.ogg',
	job_duncer => '62.ogg',
	in_rogue => '60.ogg',
	job_monk => '28.ogg',
	monk_test => '28.ogg',
	job_cru => '02.ogg',
	job_sage => '02.ogg',
	mag_dun02 => '06.ogg',
	mag_dun01 => '06.ogg',
	yuno_fild04 => '72.ogg',
	yuno_fild03 => '72.ogg',
	yuno_fild02 => '71.ogg',
	yuno_fild01 => '71.ogg',
	yuno_in03 => '70.ogg',
	yuno_in02 => '70.ogg',
	yuno_in01 => '70.ogg',
	yuno => '70.ogg',
	job_wiz => '02.ogg',
	job_prist => '02.ogg',
	job_knt => '02.ogg',
	job_hunte => '02.ogg',
	gld_dun04 => '44.ogg',
	gld_dun03 => '44.ogg',
	gld_dun02 => '44.ogg',
	gld_dun01 => '44.ogg',
	gefg_cas05 => '66.ogg',
	gefg_cas04 => '66.ogg',
	gefg_cas03 => '66.ogg',
	gefg_cas02 => '66.ogg',
	gefg_cas01 => '66.ogg',
	gef_fild13 => '66.ogg',
	aldeg_cas05 => '66.ogg',
	aldeg_cas04 => '66.ogg',
	aldeg_cas03 => '66.ogg',
	aldeg_cas02 => '66.ogg',
	aldeg_cas01 => '66.ogg',
	alde_gld => '66.ogg',
	payg_cas05 => '66.ogg',
	payg_cas04 => '66.ogg',
	payg_cas03 => '66.ogg',
	payg_cas02 => '66.ogg',
	payg_cas01 => '66.ogg',
	pay_gld => '66.ogg',
	gefg_cas05 => '66.ogg',
	gefg_cas04 => '66.ogg',
	gefg_cas03 => '66.ogg',
	prtg_cas05 => '66.ogg',
	prtg_cas04 => '66.ogg',
	prtg_cas03 => '66.ogg',
	prtg_cas02 => '66.ogg',
	prtg_cas01 => '66.ogg',
	prt_gld => '66.ogg',
	tur_dun01 => '65.ogg',
	tur_dun02 => '65.ogg',
	tur_dun03 => '65.ogg',
	tur_dun04 => '65.ogg',
	tur_dun05 => '65.ogg',
	tur_dun06 => '65.ogg',
	guild_vs4 => '07.ogg',
	guild_vs5 => '07.ogg',
	guild_vs3 => '07.ogg',
	guild_vs2 => '07.ogg',
	guild_vs1 => '07.ogg',
	guild_room => '40.ogg',
	quiz_00 => '07.ogg',
	quiz_01 => '18.ogg',
	cmd_in02 => '62.ogg',
	gef_fild14 => '64.ogg',
	gef_fild12 => '64.ogg',
	cmd_fild09 => '63.ogg',
	cmd_fild08 => '37.ogg',
	cmd_fild07 => '63.ogg',
	cmd_fild06 => '63.ogg',
	cmd_fild05 => '63.ogg',
	cmd_fild04 => '63.ogg',
	cmd_fild03 => '63.ogg',
	cmd_fild02 => '63.ogg',
	cmd_fild01 => '63.ogg',
	beach_dun3 => '41.ogg',
	beach_dun2 => '41.ogg',
	beach_dun => '41.ogg',
	cmd_in01 => '62.ogg',
	comodo => '62.ogg',
	xmas => '59.ogg',
	xmas_in => '59.ogg',
	xmas_fild01 => '55.ogg',
	xmas_dun01 => '58.ogg',
	xmas_dun02 => '58.ogg',
	mjolnir_01 => '31.ogg',
	mjolnir_02 => '33.ogg',
	mjolnir_03 => '33.ogg',
	mjolnir_04 => '33.ogg',
	mjolnir_05 => '34.ogg',
	mjolnir_06 => '31.ogg',
	mjolnir_07 => '31.ogg',
	mjolnir_08 => '33.ogg',
	mjolnir_09 => '31.ogg',
	mjolnir_10 => '34.ogg',
	mjolnir_11 => '34.ogg',
	mjolnir_12 => '34.ogg',
	prt_fild00 => '05.ogg',
	prt_fild01 => '12.ogg',
	prt_fild02 => '05.ogg',
	prt_fild03 => '05.ogg',
	prt_fild04 => '05.ogg',
	prt_fild05 => '12.ogg',
	prt_fild06 => '12.ogg',
	prt_fild07 => '05.ogg',
	prt_fild08 => '12.ogg',
	prt_fild09 => '04.ogg',
	prt_fild10 => '04.ogg',
	prt_fild11 => '04.ogg',
	prt_monk => '28.ogg',
	gef_fild00 => '25.ogg',
	gef_fild01 => '23.ogg',
	gef_fild02 => '35.ogg',
	gef_fild03 => '35.ogg',
	gef_fild04 => '25.ogg',
	gef_fild05 => '23.ogg',
	gef_fild06 => '23.ogg',
	gef_fild07 => '25.ogg',
	gef_fild08 => '23.ogg',
	gef_fild09 => '23.ogg',
	gef_fild10 => '35.ogg',
	in_orcs01 => '35.ogg',
	gef_fild11 => '04.ogg',
	moc_fild01 => '24.ogg',
	moc_fild02 => '03.ogg',
	moc_fild03 => '03.ogg',
	moc_fild04 => '24.ogg',
	moc_fild05 => '24.ogg',
	moc_fild06 => '24.ogg',
	moc_fild07 => '24.ogg',
	moc_fild08 => '37.ogg',
	moc_fild09 => '37.ogg',
	moc_fild10 => '37.ogg',
	moc_fild11 => '37.ogg',
	moc_fild12 => '37.ogg',
	moc_fild13 => '03.ogg',
	moc_fild14 => '37.ogg',
	moc_fild15 => '37.ogg',
	moc_fild16 => '45.ogg',
	in_moc_16 => '45.ogg',
	moc_fild17 => '37.ogg',
	moc_fild18 => '37.ogg',
	moc_fild19 => '37.ogg',
	pay_fild01 => '03.ogg',
	pay_fild02 => '03.ogg',
	pay_fild03 => '03.ogg',
	pay_fild04 => '03.ogg',
	pay_fild05 => '36.ogg',
	pay_fild06 => '36.ogg',
	pay_fild07 => '36.ogg',
	pay_fild08 => '03.ogg',
	pay_fild09 => '36.ogg',
	pay_fild10 => '36.ogg',
	pay_fild11 => '03.ogg',
	'new_1-1' => '30.ogg',
	'new_2-1' => '30.ogg',
	'new_3-1' => '30.ogg',
	'new_4-1' => '30.ogg',
	'new_5-1' => '30.ogg',
	'new_1-2' => '30.ogg',
	'new_2-2' => '30.ogg',
	'new_3-2' => '30.ogg',
	'new_4-2' => '30.ogg',
	'new_5-2' => '30.ogg',
	'new_1-3' => '30.ogg',
	'new_2-3' => '30.ogg',
	'new_3-3' => '30.ogg',
	'new_4-3' => '30.ogg',
	'new_5-3' => '30.ogg',
	'new_1-4' => '30.ogg',
	'new_2-4' => '30.ogg',
	'new_3-4' => '30.ogg',
	'new_4-4' => '30.ogg',
	'new_5-4' => '30.ogg',
	anthell01 => '46.ogg',
	anthell02 => '46.ogg',
	gef_dun00 => '21.ogg',
	gef_dun01 => '21.ogg',
	gef_dun02 => '50.ogg',
	gef_dun03 => '50.ogg',
	iz_dun00 => '29.ogg',
	iz_dun01 => '29.ogg',
	iz_dun02 => '29.ogg',
	iz_dun03 => '49.ogg',
	iz_dun04 => '49.ogg',
	iz_dun05 => '49.ogg',
	in_sphinx1 => '38.ogg',
	in_sphinx2 => '38.ogg',
	in_sphinx3 => '38.ogg',
	in_sphinx4 => '38.ogg',
	in_sphinx5 => '38.ogg',
	moc_pryd01 => '22.ogg',
	moc_pryd02 => '22.ogg',
	moc_pryd03 => '22.ogg',
	moc_pryd04 => '22.ogg',
	moc_pryd05 => '22.ogg',
	moc_pryd06 => '22.ogg',
	moc_prydb1 => '22.ogg',
	mjo_dun01 => '27.ogg',
	mjo_dun02 => '27.ogg',
	mjo_dun03 => '27.ogg',
	orcsdun01 => '48.ogg',
	orcsdun02 => '48.ogg',
	etc_cave01 => '20.ogg',
	pay_dun00 => '20.ogg',
	pay_dun01 => '20.ogg',
	pay_dun02 => '20.ogg',
	pay_dun03 => '47.ogg',
	pay_dun04 => '47.ogg',
	prt_maze01 => '16.ogg',
	prt_maze02 => '16.ogg',
	prt_maze03 => '16.ogg',
	prt_sewb1 => '19.ogg',
	prt_sewb2 => '19.ogg',
	prt_sewb3 => '19.ogg',
	prt_sewb4 => '19.ogg',
	treasure01 => '17.ogg',
	treasure02 => '17.ogg',
	'hunter_1-1' => '51.ogg',
	'hunter_2-1' => '51.ogg',
	'hunter_3-1' => '51.ogg',
	'in_hunter' => '51.ogg',
	'knight_1-1' => '51.ogg',
	'knight_2-1' => '51.ogg',
	'knight_3-1' => '51.ogg',
	'priest_1-1' => '51.ogg',
	'priest_2-1' => '51.ogg',
	'priest_3-1' => '51.ogg',
	'sword_1-1' => '51.ogg',
	'sword_2-1' => '51.ogg',
	'sword_3-1' => '51.ogg',
	'job_thief1' => '51.ogg',
	'wizard_1-1' => '51.ogg',
	'wizard_2-1' => '51.ogg',
	'wizard_3-1' => '51.ogg',
	'force_1-1' => '18.ogg',
	'force_2-1' => '18.ogg',
	'force_3-1' => '18.ogg',
	'force_1-2' => '18.ogg',
	'force_2-2' => '18.ogg',
	'force_3-2' => '18.ogg',
	'force_1-3' => '18.ogg',
	'force_2-3' => '18.ogg',
	'force_3-3' => '18.ogg',
	'ordeal_1-1' => '06.ogg',
	'ordeal_2-1' => '06.ogg',
	'ordeal_3-1' => '06.ogg',
	'ordeal_1-2' => '06.ogg',
	'ordeal_2-2' => '06.ogg',
	'ordeal_3-2' => '06.ogg',
	'ordeal_1-3' => '06.ogg',
	'ordeal_2-3' => '06.ogg',
	'ordeal_3-3' => '06.ogg',
	'ordeal_1-4' => '06.ogg',
	'ordeal_2-4' => '06.ogg',
	'ordeal_3-4' => '06.ogg',
	alb_ship => '52.ogg',
	alberta => '15.ogg',
	alberta_in => '15.ogg',
	alb2trea => '52.ogg',
	aldebaran => '39.ogg',
	aldeba_in => '39.ogg',
	gef_tower => '13.ogg',
	geffen => '13.ogg',
	geffen_in => '13.ogg',
	moc_castle => '11.ogg',
	moc_ruins => '52.ogg',
	morocc => '11.ogg',
	morocc_in => '11.ogg',
	pay_arche => '14.ogg',
	payon => '14.ogg',
	payon_in01 => '14.ogg',
	payon_in02 => '14.ogg',
	payon_in03 => '14.ogg',
	prontera => '08.ogg',
	prt_in => '08.ogg',
	prt_castle => '09.ogg',
	prt_church => '10.ogg',
	izlude => '26.ogg',
	izlude_in => '26.ogg',
	izlu2dun => '52.ogg',
	monk_in => '28.ogg',
	prt_are_in => '52.ogg',
	arena_room => '52.ogg',
	prt_arena01 => '02.ogg',
	prt_are01 => '02.ogg',
	glast_01 => '42.ogg',
	alde_dun01 => '61.ogg',
	alde_dun02 => '61.ogg',
	alde_dun03 => '61.ogg',
	alde_dun04 => '61.ogg',
	c_tower1 => '60.ogg',
	c_tower2 => '60.ogg',
	c_tower3 => '60.ogg',
	c_tower4 => '60.ogg',
	gl_cas01 => '43.ogg',
	gl_cas02 => '43.ogg',
	gl_church => '40.ogg',
	gl_chyard => '40.ogg',
	gl_dun01 => '42.ogg',
	gl_dun02 => '42.ogg',
	gl_in01 => '42.ogg',
	gl_knt01 => '44.ogg',
	gl_knt02 => '44.ogg',
	gl_prison => '40.ogg',
	gl_prison1 => '40.ogg',
	gl_sew01 => '48.ogg',
	gl_sew02 => '48.ogg',
	gl_sew03 => '48.ogg',
	gl_sew04 => '48.ogg',
	gl_step => '42.ogg',
	pvp_y_room => '52.ogg',
	pvp_n_room => '52.ogg',
	'pvp_n_1-1' => '02.ogg',
	'pvp_n_2-1' => '02.ogg',
	'pvp_n_3-1' => '02.ogg',
	'pvp_n_4-1' => '02.ogg',
	'pvp_n_5-1' => '02.ogg',
	'pvp_n_6-1' => '02.ogg',
	'pvp_n_7-1' => '02.ogg',
	'pvp_n_8-1' => '02.ogg',
	'pvp_n_1-2' => '18.ogg',
	'pvp_n_2-2' => '18.ogg',
	'pvp_n_3-2' => '18.ogg',
	'pvp_n_4-2' => '18.ogg',
	'pvp_n_5-2' => '18.ogg',
	'pvp_n_6-2' => '18.ogg',
	'pvp_n_7-2' => '18.ogg',
	'pvp_n_8-2' => '18.ogg',
	'pvp_n_1-3' => '17.ogg',
	'pvp_n_2-3' => '17.ogg',
	'pvp_n_3-3' => '17.ogg',
	'pvp_n_4-3' => '17.ogg',
	'pvp_n_5-3' => '17.ogg',
	'pvp_n_6-3' => '17.ogg',
	'pvp_n_7-3' => '17.ogg',
	'pvp_n_8-3' => '17.ogg',
	'pvp_n_1-4' => '19.ogg',
	'pvp_n_2-4' => '19.ogg',
	'pvp_n_3-4' => '19.ogg',
	'pvp_n_4-4' => '19.ogg',
	'pvp_n_5-4' => '19.ogg',
	'pvp_n_6-4' => '19.ogg',
	'pvp_n_7-4' => '19.ogg',
	'pvp_n_8-4' => '19.ogg',
	'pvp_n_1-5' => '21.ogg',
	'pvp_n_2-5' => '21.ogg',
	'pvp_n_3-5' => '21.ogg',
	'pvp_n_4-5' => '21.ogg',
	'pvp_n_5-5' => '21.ogg',
	'pvp_n_6-5' => '21.ogg',
	'pvp_n_7-5' => '21.ogg',
	'pvp_n_8-5' => '21.ogg',
	'pvp_y_1-1' => '08.ogg',
	'pvp_y_2-1' => '08.ogg',
	'pvp_y_3-1' => '08.ogg',
	'pvp_y_4-1' => '08.ogg',
	'pvp_y_5-1' => '08.ogg',
	'pvp_y_6-1' => '08.ogg',
	'pvp_y_7-1' => '08.ogg',
	'pvp_y_8-1' => '08.ogg',
	'pvp_y_1-2' => '26.ogg',
	'pvp_y_2-2' => '26.ogg',
	'pvp_y_3-2' => '26.ogg',
	'pvp_y_4-2' => '26.ogg',
	'pvp_y_5-2' => '26.ogg',
	'pvp_y_6-2' => '26.ogg',
	'pvp_y_7-2' => '26.ogg',
	'pvp_y_8-2' => '26.ogg',
	'pvp_y_1-3' => '14.ogg',
	'pvp_y_2-3' => '14.ogg',
	'pvp_y_3-3' => '14.ogg',
	'pvp_y_4-3' => '14.ogg',
	'pvp_y_5-3' => '14.ogg',
	'pvp_y_6-3' => '14.ogg',
	'pvp_y_7-3' => '14.ogg',
	'pvp_y_8-3' => '14.ogg',
	'pvp_y_1-4' => '15.ogg',
	'pvp_y_2-4' => '15.ogg',
	'pvp_y_3-4' => '15.ogg',
	'pvp_y_4-4' => '15.ogg',
	'pvp_y_5-4' => '15.ogg',
	'pvp_y_6-4' => '15.ogg',
	'pvp_y_7-4' => '15.ogg',
	'pvp_y_8-4' => '15.ogg',
	'pvp_y_1-5' => '11.ogg',
	'pvp_y_2-5' => '11.ogg',
	'pvp_y_3-5' => '11.ogg',
	'pvp_y_4-5' => '11.ogg',
	'pvp_y_5-5' => '11.ogg',
	'pvp_y_6-5' => '11.ogg',
	'pvp_y_7-5' => '11.ogg',
	'pvp_y_8-5' => '11.ogg',
	pvp_y_room => '52.ogg',
	pvp_2vs2 => '02.ogg',
	pvp_room => '52.ogg',
);

1;