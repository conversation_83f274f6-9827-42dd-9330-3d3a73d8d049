# 🔧 Como Compilar recv.asi

## 📋 **PRÉ-REQUISITOS**
- Visual Studio 2019/2022
- Windows SDK
- Conhecimento básico de C++

## 🚀 **PASSOS PARA COMPILAR**

### **1. Abrir Projeto**
```
1. Navegue para: C:\openkore2\recv\
2. Duplo-clique em: recv.sln
3. Visual Studio vai abrir o projeto
```

### **2. Configurar Build**
```
1. No Visual Studio, selecione:
   - Configuration: Release
   - Platform: x86 (32-bit) ou x64 (64-bit)
2. Verifique se está configurado para gerar .asi
```

### **3. Compilar**
```
1. Menu Build > Build Solution
2. Ou pressione Ctrl+Shift+B
3. Aguarde compilação terminar
```

### **4. Localizar recv.asi**
```
Arquivo compilado estará em:
- recv\Release\recv.asi
- recv\x64\Release\recv.asi
```

## ⚠️ **POSSÍVEIS PROBLEMAS**

### **Erro de Compilação:**
- Verificar se Windows SDK está instalado
- Verificar se todas as dependências estão presentes
- Verificar configurações do projeto

### **Arquivo não é .asi:**
- Verificar se projeto está configurado para gerar DLL
- Renomear .dll para .asi se necessário

## 🎯 **ALTERNATIVA MAIS FÁCIL**

Se compilação for complexa, **peça o recv.asi já compilado** para quem enviou o código fonte.
