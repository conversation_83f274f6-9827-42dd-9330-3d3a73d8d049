# Configuração com Packet Size Corrigido - 431 bytes
# OTP token handler funcionando + packet size correto

######## Login Settings ########
master Latam - R<PERSON><PERSON>: <PERSON><PERSON>/Nidhogg/Yggdrasil
server 1
username christ<PERSON><PERSON><PERSON><PERSON>@gmail.com
password Chris2006@
loginPinCode 0103
char 0

# XKore 2 mode
XKore 2
XKore_port 6901
XKore_exeName ragexe.exe

######## DEBUG SETTINGS ########
debug 2
verboseLog 1
debugPacket_sent 2
debugPacket_received 2
debugPacket_include_dumpMethod 2

# Focar nos pacotes importantes
debugPacket_include 0825,0AE3,006A,0081,0C32,0436,0085,0437

# Log to files
logToFile 1
logToFile_Packet 1
logToFile_Debug 1

######## AI SETTINGS ########
ai_manual 1
attackAuto 0
route_randomWalk 0
moveStepDelay 2000
attackDelay 2000

# Timeouts
timeout 30
timeout_ex 60

######## MUDANÇAS IMPLEMENTADAS ########
# ✅ OTP token handler funcionand<PERSON>
# ✅ "OTP token sent back to server successfully"
# ✅ Packet size corrigido: 0825 agora aceita 431 bytes
# ✅ Token sendo incluído corretamente nos packets

######## LOGS ESPERADOS ########
# ✅ "OTP token sent back to server successfully"
# ✅ Packet 0825 com 431 bytes aceito
# ✅ Sem erro 006A (connection denied)
# ✅ Progressão para character/map server
# ✅ [ROla] REAL XOR debug messages
# ✅ Movimento funcionando

######## SE AINDA HOUVER ERRO 006A ########
# Precisaremos investigar no IDA Pro:
# 1. Buscar por "006A" ou "6A 00"
# 2. Procurar código de erro 30
# 3. Analisar validação de token
# 4. Verificar formato esperado do token

######## COMANDOS DE TESTE ########
# Após login completo:
# move 100 150    # Movimento (seed 1)
# move 120 130    # Movimento (seed 2)
# sit             # Ação (seed 3)
# stand           # Ação (seed 4)

######## PROGRESSO ATUAL ########
# ✅ Login inicial: FUNCIONANDO
# ✅ OTP token: FUNCIONANDO
# ✅ Token processing: FUNCIONANDO
# 🔄 Token validation: EM TESTE
# ⏳ Map server: AGUARDANDO
# ⏳ Checksum: PRONTO PARA TESTE
